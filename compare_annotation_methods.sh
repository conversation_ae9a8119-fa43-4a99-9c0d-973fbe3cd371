#!/bin/bash

# 对比Diamond和HMMER注释方法的效果
# 生成详细的对比报告

echo "=== Diamond vs HMMER 注释方法对比分析 ==="
echo "开始时间: $(date)"

# 设置文件路径
DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
HMMER_FILE="../10_Annotation/eggnog_results_hmmer/hagfish_proteins_hmmer.emapper.annotations"
OUTPUT_DIR="../10_Annotation/method_comparison"

# 创建对比结果目录
mkdir -p "$OUTPUT_DIR"

echo ""
echo "=== 检查输入文件 ==="

if [ ! -f "$DIAMOND_FILE" ]; then
    echo "❌ Diamond结果文件不存在: $DIAMOND_FILE"
    exit 1
fi

if [ ! -f "$HMMER_FILE" ]; then
    echo "❌ HMMER结果文件不存在: $HMMER_FILE"
    echo "请先运行HMMER分析: ./run_hmmer_analysis.sh"
    exit 1
fi

echo "✅ 找到Diamond结果文件: $DIAMOND_FILE"
echo "✅ 找到HMMER结果文件: $HMMER_FILE"

echo ""
echo "=== 基础统计对比 ==="

# Diamond统计
DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)

# HMMER统计
HMMER_TOTAL=$(tail -n +5 "$HMMER_FILE" | wc -l)
HMMER_GO=$(tail -n +5 "$HMMER_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
HMMER_KEGG=$(tail -n +5 "$HMMER_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
HMMER_GENE=$(tail -n +5 "$HMMER_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)

echo "方法对比表:"
echo "指标          Diamond    HMMER      差异"
echo "总注释序列    $DIAMOND_TOTAL       $HMMER_TOTAL      $((HMMER_TOTAL - DIAMOND_TOTAL))"
echo "GO注释        $DIAMOND_GO       $HMMER_GO      $((HMMER_GO - DIAMOND_GO))"
echo "KEGG注释      $DIAMOND_KEGG       $HMMER_KEGG      $((HMMER_KEGG - DIAMOND_KEGG))"
echo "基因名        $DIAMOND_GENE       $HMMER_GENE      $((HMMER_GENE - DIAMOND_GENE))"

echo ""
echo "=== 生成详细对比报告 ==="

# 生成详细报告
REPORT_FILE="$OUTPUT_DIR/annotation_methods_comparison_report.md"

cat > "$REPORT_FILE" << EOF
# Diamond vs HMMER 注释方法对比报告

**生成时间**: $(date)
**分析数据**: 盲鳗蛋白质序列

## 1. 方法概述

### Diamond方法
- **算法**: 基于BLAST的快速序列比对
- **特点**: 速度快，适合大规模数据
- **数据库**: eggnog_proteins.dmnd
- **结果文件**: $DIAMOND_FILE

### HMMER方法  
- **算法**: 基于隐马尔可夫模型(HMM)
- **特点**: 更精确，能发现远程同源序列
- **数据库**: Eukaryota.hmm
- **结果文件**: $HMMER_FILE

## 2. 注释统计对比

| 指标 | Diamond | HMMER | 差异 | 改进率 |
|------|---------|-------|------|--------|
| 总注释序列 | $DIAMOND_TOTAL | $HMMER_TOTAL | $((HMMER_TOTAL - DIAMOND_TOTAL)) | $(echo "scale=1; ($HMMER_TOTAL - $DIAMOND_TOTAL) * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")% |
| GO注释 | $DIAMOND_GO | $HMMER_GO | $((HMMER_GO - DIAMOND_GO)) | $(echo "scale=1; ($HMMER_GO - $DIAMOND_GO) * 100 / $DIAMOND_GO" | bc -l 2>/dev/null || echo "N/A")% |
| KEGG注释 | $DIAMOND_KEGG | $HMMER_KEGG | $((HMMER_KEGG - DIAMOND_KEGG)) | $(echo "scale=1; ($HMMER_KEGG - $DIAMOND_KEGG) * 100 / $DIAMOND_KEGG" | bc -l 2>/dev/null || echo "N/A")% |
| 基因名 | $DIAMOND_GENE | $HMMER_GENE | $((HMMER_GENE - DIAMOND_GENE)) | $(echo "scale=1; ($HMMER_GENE - $DIAMOND_GENE) * 100 / $DIAMOND_GENE" | bc -l 2>/dev/null || echo "N/A")% |

## 3. 覆盖率分析

### Diamond方法覆盖率
- GO注释覆盖率: $(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l)%
- KEGG注释覆盖率: $(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l)%
- 基因名覆盖率: $(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l)%

### HMMER方法覆盖率
- GO注释覆盖率: $(echo "scale=1; $HMMER_GO * 100 / $HMMER_TOTAL" | bc -l)%
- KEGG注释覆盖率: $(echo "scale=1; $HMMER_KEGG * 100 / $HMMER_TOTAL" | bc -l)%
- 基因名覆盖率: $(echo "scale=1; $HMMER_GENE * 100 / $HMMER_TOTAL" | bc -l)%

## 4. 方法选择建议

### 推荐使用Diamond方法的情况
- 大规模数据集(>100K序列)
- 对速度要求较高
- 计算资源有限
- 初步筛选和快速注释

### 推荐使用HMMER方法的情况
- 精确注释需求
- 研究远程同源序列
- 小到中等规模数据集
- 发表级别的高质量注释

## 5. 结论

基于本次分析结果，对于盲鳗RNA-seq数据：

1. **注释质量**: HMMER方法在[具体指标]方面表现更好
2. **计算效率**: Diamond方法速度更快
3. **推荐策略**: [根据实际结果给出建议]

## 6. 文件位置

- **Diamond结果**: \`$DIAMOND_FILE\`
- **HMMER结果**: \`$HMMER_FILE\`
- **对比报告**: \`$REPORT_FILE\`

---
*报告生成时间: $(date)*
EOF

echo "✅ 详细对比报告已生成: $REPORT_FILE"

echo ""
echo "=== 生成基因注释对比文件 ==="

# 提取基因ID和注释信息进行对比
python3 << 'EOF'
import pandas as pd
import sys

try:
    # 读取Diamond结果
    diamond_df = pd.read_csv('../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations', 
                           sep='\t', comment='#', header=0)
    
    # 读取HMMER结果
    hmmer_df = pd.read_csv('../10_Annotation/eggnog_results_hmmer/hagfish_proteins_hmmer.emapper.annotations', 
                         sep='\t', comment='#', header=0)
    
    # 合并数据进行对比
    comparison_df = diamond_df[['query_name', 'Preferred_name', 'GOs', 'KEGG_ko']].merge(
        hmmer_df[['query_name', 'Preferred_name', 'GOs', 'KEGG_ko']], 
        on='query_name', suffixes=('_diamond', '_hmmer'), how='outer'
    )
    
    # 保存对比结果
    comparison_df.to_csv('../10_Annotation/method_comparison/gene_annotation_comparison.csv', index=False)
    
    print(f"✅ 基因注释对比文件已生成: gene_annotation_comparison.csv")
    print(f"   - 对比基因数: {len(comparison_df)}")
    print(f"   - Diamond独有: {comparison_df['Preferred_name_diamond'].notna().sum() - comparison_df['Preferred_name_hmmer'].notna().sum()}")
    print(f"   - HMMER独有: {comparison_df['Preferred_name_hmmer'].notna().sum() - comparison_df['Preferred_name_diamond'].notna().sum()}")
    
except Exception as e:
    print(f"❌ 生成对比文件时出错: {e}")
EOF

echo ""
echo "完成时间: $(date)"
echo "=== 注释方法对比分析完成 ==="

echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"

echo ""
echo "查看详细报告:"
echo "cat $REPORT_FILE"
