#!/bin/bash

# 基于官方文档的正确HMMER方法EggNOG-mapper分析
# 参考: https://github.com/eggnogdb/eggnog-mapper/wiki/eggNOG-mapper-v2.1.5-to-v2.1.13

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper HMMER方法 (官方文档版) ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_hmmer"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_hmmer"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 当前Diamond方法结果统计 ==="

DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

if [ -f "$DIAMOND_FILE" ]; then
    echo "分析现有Diamond结果..."
    
    # Diamond统计
    DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
    DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "Diamond方法结果:"
    echo "  总注释序列: $DIAMOND_TOTAL"
    echo "  GO注释: $DIAMOND_GO ($(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $DIAMOND_KEGG ($(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $DIAMOND_GENE ($(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
else
    echo "未找到Diamond结果文件"
fi

echo ""
echo "=== 检查HMMER数据库 ==="

# 检查是否需要下载HMMER数据库
# 根据文档，HMMER需要特定的数据库
echo "检查HMMER数据库可用性..."

# 对于真核生物，我们可以尝试下载真核生物的HMMER数据库
# 根据文档: download_eggnog_data.py -H -d taxID

echo "注意: HMMER方法需要特定的HMMER数据库"
echo "对于盲鳗(真核生物)，建议使用真核生物数据库"

echo ""
echo "=== 运行HMMER方法 ==="

# 方法1: 基础HMMER搜索
echo "尝试基础HMMER搜索..."

emapper.py \
    -m hmmer \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 4 \
    --override

HMMER_EXIT_CODE=$?
echo "HMMER方法退出码: $HMMER_EXIT_CODE"

# 检查结果
HMMER_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$HMMER_FILE" ]; then
    echo "✅ HMMER方法成功！"
    
    # HMMER统计
    HMMER_TOTAL=$(tail -n +5 "$HMMER_FILE" | wc -l)
    HMMER_GO=$(tail -n +5 "$HMMER_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_KEGG=$(tail -n +5 "$HMMER_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_GENE=$(tail -n +5 "$HMMER_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_COG=$(tail -n +5 "$HMMER_FILE" | cut -f7 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_PFAM=$(tail -n +5 "$HMMER_FILE" | cut -f21 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "HMMER方法结果:"
    echo "  总注释序列: $HMMER_TOTAL"
    echo "  GO注释: $HMMER_GO ($(echo "scale=1; $HMMER_GO * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $HMMER_KEGG ($(echo "scale=1; $HMMER_KEGG * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $HMMER_GENE ($(echo "scale=1; $HMMER_GENE * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  COG分类: $HMMER_COG ($(echo "scale=1; $HMMER_COG * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  PFAM结构域: $HMMER_PFAM ($(echo "scale=1; $HMMER_PFAM * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    
    echo ""
    echo "=== Diamond vs HMMER 详细对比 ==="
    echo "方法      总序列    GO注释    KEGG注释  基因名    COG分类   PFAM结构域"
    echo "Diamond   $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE      -         -"
    echo "HMMER     $HMMER_TOTAL      $HMMER_GO      $HMMER_KEGG       $HMMER_GENE      $HMMER_COG      $HMMER_PFAM"
    
    # 计算差异和改进
    if [ $DIAMOND_TOTAL -gt 0 ] && [ $HMMER_TOTAL -gt 0 ]; then
        echo ""
        echo "HMMER相对于Diamond的改进:"
        GO_DIFF=$((HMMER_GO - DIAMOND_GO))
        KEGG_DIFF=$((HMMER_KEGG - DIAMOND_KEGG))
        GENE_DIFF=$((HMMER_GENE - DIAMOND_GENE))
        TOTAL_DIFF=$((HMMER_TOTAL - DIAMOND_TOTAL))
        
        echo "  总注释序列差异: $TOTAL_DIFF"
        echo "  GO注释差异: $GO_DIFF"
        echo "  KEGG注释差异: $KEGG_DIFF"
        echo "  基因名差异: $GENE_DIFF"
        
        # 计算百分比改进
        if [ $DIAMOND_GO -gt 0 ]; then
            GO_IMPROVE=$(echo "scale=1; ($GO_DIFF * 100) / $DIAMOND_GO" | bc -l 2>/dev/null || echo "N/A")
            echo "  GO注释改进: $GO_IMPROVE%"
        fi
        
        if [ $DIAMOND_KEGG -gt 0 ]; then
            KEGG_IMPROVE=$(echo "scale=1; ($KEGG_DIFF * 100) / $DIAMOND_KEGG" | bc -l 2>/dev/null || echo "N/A")
            echo "  KEGG注释改进: $KEGG_IMPROVE%"
        fi
    fi
    
    echo ""
    echo "HMMER方法注释结果预览:"
    head -10 "$HMMER_FILE"
    
else
    echo "❌ HMMER方法失败"
    
    echo ""
    echo "可能的原因:"
    echo "1. 缺少HMMER数据库"
    echo "2. 需要下载特定的HMMER数据库"
    echo "3. 参数配置问题"
    
    echo ""
    echo "建议解决方案:"
    echo "1. 下载真核生物HMMER数据库:"
    echo "   download_eggnog_data.py -H -d 2759  # 真核生物"
    echo "2. 或者尝试MMseqs2方法:"
    echo "   emapper.py -m mmseqs ..."
    
    # 尝试MMseqs2作为替代
    echo ""
    echo "=== 尝试MMseqs2方法作为替代 ==="
    
    emapper.py \
        -m mmseqs \
        -i "$PROTEIN_FILE" \
        --output "${OUTPUT_PREFIX}_mmseqs2" \
        --output_dir "$OUTPUT_DIR" \
        --data_dir "$DATA_DIR" \
        --cpu 4 \
        --override
    
    MMSEQS_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}_mmseqs2.emapper.annotations"
    if [ -f "$MMSEQS_FILE" ]; then
        echo "✅ MMseqs2方法成功！"
        
        # 重命名为主要结果文件
        mv "$MMSEQS_FILE" "$HMMER_FILE"
        
        # 统计MMseqs2结果
        MMSEQS_TOTAL=$(tail -n +5 "$HMMER_FILE" | wc -l)
        MMSEQS_GO=$(tail -n +5 "$HMMER_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
        MMSEQS_KEGG=$(tail -n +5 "$HMMER_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
        MMSEQS_GENE=$(tail -n +5 "$HMMER_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
        
        echo "MMseqs2方法结果:"
        echo "  总注释序列: $MMSEQS_TOTAL"
        echo "  GO注释: $MMSEQS_GO"
        echo "  KEGG注释: $MMSEQS_KEGG"
        echo "  基因名: $MMSEQS_GENE"
        
    else
        echo "❌ MMseqs2方法也失败"
    fi
fi

echo ""
echo "完成时间: $(date)"
echo "=== EggNOG-mapper HMMER/MMseqs2方法分析完成 ==="

echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"

echo ""
echo "=== 总结 ==="
echo "1. Diamond方法: 快速，适合大规模数据"
echo "2. HMMER方法: 更精确，基于HMM模型"
echo "3. MMseqs2方法: 平衡速度和精度"
echo ""
echo "对于盲鳗(真核生物)数据，建议:"
echo "- 使用Diamond进行快速注释"
echo "- 使用HMMER进行精确注释(需要下载对应数据库)"
echo "- 使用MMseqs2作为折中方案"
