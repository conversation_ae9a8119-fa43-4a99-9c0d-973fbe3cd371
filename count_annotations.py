#!/usr/bin/env python3

import pandas as pd
import sys

def count_annotations():
    print("🎯 === HMMER vs Diamond 注释结果数量统计 === 🎯")
    print()
    
    # 文件路径
    diamond_file = "Data_Organized/10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
    hmmer_file = "Data_Organized/10_Annotation/eggnog_results_hmmer/hagfish_proteins_hmmer.emapper.annotations"
    
    try:
        # 读取Diamond结果
        diamond_df = pd.read_csv(diamond_file, sep='\t', comment='#', header=0)
        print(f"✅ Diamond结果读取成功: {len(diamond_df)} 条总记录")
        
        # 读取HMMER结果  
        hmmer_df = pd.read_csv(hmmer_file, sep='\t', comment='#', header=0)
        print(f"✅ HMMER结果读取成功: {len(hmmer_df)} 条总记录")
        
        print()
        print("📊 === 有效注释统计 ===")
        
        # 统计有效注释（有基因名的）
        diamond_annotated = diamond_df[
            (diamond_df['Preferred_name'].notna()) & 
            (diamond_df['Preferred_name'] != '-') & 
            (diamond_df['Preferred_name'] != '')
        ]
        
        hmmer_annotated = hmmer_df[
            (hmmer_df['Preferred_name'].notna()) & 
            (hmmer_df['Preferred_name'] != '-') & 
            (hmmer_df['Preferred_name'] != '')
        ]
        
        print(f"🔹 Diamond成功注释: {len(diamond_annotated)} 个基因")
        print(f"🔹 HMMER成功注释: {len(hmmer_annotated)} 个基因")
        
        print()
        print("🔍 === 注释重叠分析 ===")
        
        # 获取注释的基因ID集合
        diamond_genes = set(diamond_annotated['query_name'])
        hmmer_genes = set(hmmer_annotated['query_name'])
        
        # 计算重叠情况
        diamond_only = diamond_genes - hmmer_genes
        hmmer_only = hmmer_genes - diamond_genes
        both_annotated = diamond_genes & hmmer_genes
        
        print(f"🔸 只有Diamond注释的基因: {len(diamond_only)} 个")
        print(f"🔸 只有HMMER注释的基因: {len(hmmer_only)} 个")
        print(f"🔸 两种方法都注释的基因: {len(both_annotated)} 个")
        
        print()
        print("📈 === 详细统计表格 ===")
        total_unique = len(diamond_genes | hmmer_genes)
        
        print(f"{'类别':<25} {'数量':<10} {'百分比':<10}")
        print("=" * 45)
        print(f"{'总的被注释基因':<25} {total_unique:<10} {100.0:<10.1f}%")
        print(f"{'Diamond独有注释':<25} {len(diamond_only):<10} {len(diamond_only)/total_unique*100:<10.1f}%")
        print(f"{'HMMER独有注释':<25} {len(hmmer_only):<10} {len(hmmer_only)/total_unique*100:<10.1f}%")
        print(f"{'两种方法共同注释':<25} {len(both_annotated):<10} {len(both_annotated)/total_unique*100:<10.1f}%")
        
        print()
        print("🎯 === 核心结论 ===")
        
        if len(hmmer_annotated) > len(diamond_annotated):
            diff = len(hmmer_annotated) - len(diamond_annotated)
            print(f"🏆 HMMER注释了更多基因，比Diamond多 {diff} 个 ({diff/len(diamond_annotated)*100:.1f}% 提升)")
        elif len(diamond_annotated) > len(hmmer_annotated):
            diff = len(diamond_annotated) - len(hmmer_annotated)
            print(f"🏆 Diamond注释了更多基因，比HMMER多 {diff} 个 ({diff/len(hmmer_annotated)*100:.1f}% 提升)")
        else:
            print(f"🏆 两种方法注释的基因数量完全相同")
        
        print(f"📊 注释一致性: {len(both_annotated)/total_unique*100:.1f}% 的基因被两种方法都注释")
        print(f"🔍 方法互补性: {(len(diamond_only) + len(hmmer_only))/total_unique*100:.1f}% 的基因只被一种方法注释")
        
        # 额外统计GO和KEGG注释
        print()
        print("🧬 === GO注释统计 ===")
        diamond_go = len(diamond_df[(diamond_df['GOs'].notna()) & (diamond_df['GOs'] != '-')])
        hmmer_go = len(hmmer_df[(hmmer_df['GOs'].notna()) & (hmmer_df['GOs'] != '-')])
        print(f"Diamond GO注释: {diamond_go} 个")
        print(f"HMMER GO注释: {hmmer_go} 个")
        print(f"GO注释差异: {hmmer_go - diamond_go} 个")
        
        print()
        print("🔬 === KEGG注释统计 ===")
        diamond_kegg = len(diamond_df[(diamond_df['KEGG_ko'].notna()) & (diamond_df['KEGG_ko'] != '-')])
        hmmer_kegg = len(hmmer_df[(hmmer_df['KEGG_ko'].notna()) & (hmmer_df['KEGG_ko'] != '-')])
        print(f"Diamond KEGG注释: {diamond_kegg} 个")
        print(f"HMMER KEGG注释: {hmmer_kegg} 个")
        print(f"KEGG注释差异: {hmmer_kegg - diamond_kegg} 个")
        
        # 保存详细对比数据
        print()
        print("💾 === 保存详细对比数据 ===")
        
        # 创建对比数据框
        comparison_df = diamond_df[['query_name', 'Preferred_name', 'GOs', 'KEGG_ko']].merge(
            hmmer_df[['query_name', 'Preferred_name', 'GOs', 'KEGG_ko']], 
            on='query_name', suffixes=('_diamond', '_hmmer'), how='outer'
        )
        
        # 保存到文件
        output_file = "Data_Organized/10_Annotation/annotation_comparison_detailed.csv"
        comparison_df.to_csv(output_file, index=False)
        print(f"✅ 详细对比数据已保存: {output_file}")
        
        return {
            'diamond_total': len(diamond_annotated),
            'hmmer_total': len(hmmer_annotated),
            'diamond_only': len(diamond_only),
            'hmmer_only': len(hmmer_only),
            'both': len(both_annotated),
            'total_unique': total_unique
        }
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    count_annotations()
