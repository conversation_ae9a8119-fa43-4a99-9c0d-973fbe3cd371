#!/bin/bash

# EggNOG-mapper HMM方法分析
# 与Diamond方法对比注释效果

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper HMM方法 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_hmm"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_hmm"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 当前Diamond方法结果统计 ==="

DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

if [ -f "$DIAMOND_FILE" ]; then
    echo "分析现有Diamond结果..."
    
    # Diamond统计
    DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
    DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "Diamond方法结果:"
    echo "  总注释序列: $DIAMOND_TOTAL"
    echo "  GO注释: $DIAMOND_GO ($(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $DIAMOND_KEGG ($(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $DIAMOND_GENE ($(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
else
    echo "未找到Diamond结果文件"
fi

echo ""
echo "=== 尝试HMM方法 ==="

# 方法1: 尝试hmmer参数
echo "尝试方法1: -m hmmer"
emapper.py \
    -m hmmer \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 4 \
    --override

HMM_EXIT_CODE=$?
echo "HMM方法退出码: $HMM_EXIT_CODE"

# 检查结果
HMM_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$HMM_FILE" ]; then
    echo "✅ HMM方法成功！"
    
    # HMM统计
    HMM_TOTAL=$(tail -n +5 "$HMM_FILE" | wc -l)
    HMM_GO=$(tail -n +5 "$HMM_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMM_KEGG=$(tail -n +5 "$HMM_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMM_GENE=$(tail -n +5 "$HMM_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "HMM方法结果:"
    echo "  总注释序列: $HMM_TOTAL"
    echo "  GO注释: $HMM_GO ($(echo "scale=1; $HMM_GO * 100 / $HMM_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $HMM_KEGG ($(echo "scale=1; $HMM_KEGG * 100 / $HMM_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $HMM_GENE ($(echo "scale=1; $HMM_GENE * 100 / $HMM_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    
    echo ""
    echo "=== Diamond vs HMM 对比 ==="
    echo "方法      总序列    GO注释    KEGG注释  基因名"
    echo "Diamond   $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE"
    echo "HMM       $HMM_TOTAL      $HMM_GO      $HMM_KEGG       $HMM_GENE"
    
    # 计算差异
    if [ $DIAMOND_TOTAL -gt 0 ] && [ $HMM_TOTAL -gt 0 ]; then
        echo ""
        echo "HMM相对于Diamond的改进:"
        GO_DIFF=$((HMM_GO - DIAMOND_GO))
        KEGG_DIFF=$((HMM_KEGG - DIAMOND_KEGG))
        GENE_DIFF=$((HMM_GENE - DIAMOND_GENE))
        
        echo "  GO注释差异: $GO_DIFF"
        echo "  KEGG注释差异: $KEGG_DIFF"
        echo "  基因名差异: $GENE_DIFF"
    fi
    
else
    echo "❌ HMM方法失败，尝试其他方法..."
    
    # 方法2: 尝试mmseqs2
    echo ""
    echo "尝试方法2: -m mmseqs2"
    emapper.py \
        -m mmseqs2 \
        -i "$PROTEIN_FILE" \
        --output "${OUTPUT_PREFIX}_mmseqs2" \
        --output_dir "$OUTPUT_DIR" \
        --data_dir "$DATA_DIR" \
        --cpu 4 \
        --override
    
    MMSEQS_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}_mmseqs2.emapper.annotations"
    if [ -f "$MMSEQS_FILE" ]; then
        echo "✅ MMseqs2方法成功！"
        mv "$MMSEQS_FILE" "$HMM_FILE"
    else
        echo "❌ MMseqs2方法也失败"
        
        # 方法3: 尝试不同的参数组合
        echo ""
        echo "尝试方法3: 基础参数"
        emapper.py \
            -i "$PROTEIN_FILE" \
            --output "${OUTPUT_PREFIX}_basic" \
            --output_dir "$OUTPUT_DIR" \
            --data_dir "$DATA_DIR" \
            --cpu 2 \
            --override
        
        BASIC_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}_basic.emapper.annotations"
        if [ -f "$BASIC_FILE" ]; then
            echo "✅ 基础方法成功！"
            mv "$BASIC_FILE" "$HMM_FILE"
        fi
    fi
fi

echo ""
echo "完成时间: $(date)"
echo "=== EggNOG-mapper HMM方法分析完成 ==="

echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"

# 如果成功，显示前几行结果
if [ -f "$HMM_FILE" ]; then
    echo ""
    echo "HMM方法注释结果预览:"
    head -10 "$HMM_FILE"
fi
