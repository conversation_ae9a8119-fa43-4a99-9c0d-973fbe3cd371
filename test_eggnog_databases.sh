#!/bin/bash

echo "=== 检查EggNOG-mapper数据库位置 ==="

# 创建一个小的测试文件
echo ">test_seq" > test_protein.fa
echo "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG" >> test_protein.fa

echo "测试序列已创建"

echo ""
echo "=== 尝试运行Diamond方法（看看数据库在哪里） ==="

# 运行emapper，看看它会报告什么错误或使用什么数据库
emapper.py -m diamond -i test_protein.fa -o test_output --cpu 1 --override 2>&1 | head -20

echo ""
echo "=== 尝试运行HMMER方法（看看数据库在哪里） ==="

emapper.py -m hmmer -i test_protein.fa -o test_hmmer --cpu 1 --override 2>&1 | head -20

echo ""
echo "=== 查找系统中的EggNOG数据库文件 ==="

echo "查找Diamond数据库文件(.dmnd):"
find / -name "*.dmnd" 2>/dev/null | grep -i eggnog | head -5

echo ""
echo "查找HMMER数据库文件(.hmm):"
find / -name "*.hmm" 2>/dev/null | grep -i eggnog | head -5

echo ""
echo "查找EggNOG数据库文件(.db):"
find / -name "*.db" 2>/dev/null | grep -i eggnog | head -5

echo ""
echo "查找可能的数据目录:"
find / -type d -name "*eggnog*" 2>/dev/null | head -10

# 清理测试文件
rm -f test_protein.fa test_output* test_hmmer*

echo ""
echo "=== 检查完成 ==="
