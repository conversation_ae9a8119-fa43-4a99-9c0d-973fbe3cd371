#!/bin/bash

cd /user/data/home/<USER>/RNA-seq

echo "🎯 === HMMER vs Diamond 注释结果统计 === 🎯"
echo

# 文件路径
DIAMOND_FILE="Data_Organized/10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
HMMER_FILE="Data_Organized/10_Annotation/eggnog_results_hmmer/hagfish_proteins_hmmer.emapper.annotations"

echo "📁 检查文件存在性:"
if [ -f "$DIAMOND_FILE" ]; then
    echo "✅ Diamond文件存在"
else
    echo "❌ Diamond文件不存在"
    exit 1
fi

if [ -f "$HMMER_FILE" ]; then
    echo "✅ HMMER文件存在"
else
    echo "❌ HMMER文件不存在"
    exit 1
fi

echo
echo "📊 === 注释数量统计 ==="

# 统计Diamond注释数量（有基因名的）
DIAMOND_COUNT=$(tail -n +5 "$DIAMOND_FILE" | awk -F'\t' '$9 != "-" && $9 != "" && $9 != "NA" {count++} END {print count+0}')
echo "🔹 Diamond成功注释: $DIAMOND_COUNT 个基因"

# 统计HMMER注释数量（有基因名的）
HMMER_COUNT=$(tail -n +5 "$HMMER_FILE" | awk -F'\t' '$9 != "-" && $9 != "" && $9 != "NA" {count++} END {print count+0}')
echo "🔹 HMMER成功注释: $HMMER_COUNT 个基因"

echo
echo "🔍 === 重叠分析 ==="

# 提取有注释的基因ID
tail -n +5 "$DIAMOND_FILE" | awk -F'\t' '$9 != "-" && $9 != "" && $9 != "NA" {print $1}' | sort > /tmp/diamond_genes.txt
tail -n +5 "$HMMER_FILE" | awk -F'\t' '$9 != "-" && $9 != "" && $9 != "NA" {print $1}' | sort > /tmp/hmmer_genes.txt

# 计算重叠
DIAMOND_ONLY=$(comm -23 /tmp/diamond_genes.txt /tmp/hmmer_genes.txt | wc -l)
HMMER_ONLY=$(comm -13 /tmp/diamond_genes.txt /tmp/hmmer_genes.txt | wc -l)
BOTH=$(comm -12 /tmp/diamond_genes.txt /tmp/hmmer_genes.txt | wc -l)
TOTAL_UNIQUE=$(cat /tmp/diamond_genes.txt /tmp/hmmer_genes.txt | sort -u | wc -l)

echo "🔸 只有Diamond注释的基因: $DIAMOND_ONLY 个"
echo "🔸 只有HMMER注释的基因: $HMMER_ONLY 个"
echo "🔸 两种方法都注释的基因: $BOTH 个"
echo "🔸 总计被注释的基因: $TOTAL_UNIQUE 个"

echo
echo "📈 === 百分比统计 ==="
if [ $TOTAL_UNIQUE -gt 0 ]; then
    DIAMOND_ONLY_PCT=$(echo "scale=1; $DIAMOND_ONLY * 100 / $TOTAL_UNIQUE" | bc -l)
    HMMER_ONLY_PCT=$(echo "scale=1; $HMMER_ONLY * 100 / $TOTAL_UNIQUE" | bc -l)
    BOTH_PCT=$(echo "scale=1; $BOTH * 100 / $TOTAL_UNIQUE" | bc -l)
    
    echo "Diamond独有: ${DIAMOND_ONLY_PCT}%"
    echo "HMMER独有: ${HMMER_ONLY_PCT}%"
    echo "共同注释: ${BOTH_PCT}%"
fi

echo
echo "🎯 === 结论 ==="
if [ $HMMER_COUNT -gt $DIAMOND_COUNT ]; then
    DIFF=$((HMMER_COUNT - DIAMOND_COUNT))
    echo "🏆 HMMER注释了更多基因，比Diamond多 $DIFF 个"
elif [ $DIAMOND_COUNT -gt $HMMER_COUNT ]; then
    DIFF=$((DIAMOND_COUNT - HMMER_COUNT))
    echo "🏆 Diamond注释了更多基因，比HMMER多 $DIFF 个"
else
    echo "🏆 两种方法注释的基因数量相同"
fi

echo
echo "📋 === 详细统计表格 ==="
printf "%-20s %10s\n" "类别" "数量"
echo "================================"
printf "%-20s %10s\n" "Diamond注释" "$DIAMOND_COUNT"
printf "%-20s %10s\n" "HMMER注释" "$HMMER_COUNT"
printf "%-20s %10s\n" "Diamond独有" "$DIAMOND_ONLY"
printf "%-20s %10s\n" "HMMER独有" "$HMMER_ONLY"
printf "%-20s %10s\n" "共同注释" "$BOTH"
printf "%-20s %10s\n" "总计唯一基因" "$TOTAL_UNIQUE"

# 清理临时文件
rm -f /tmp/diamond_genes.txt /tmp/hmmer_genes.txt

echo
echo "✅ 统计完成！"
