#!/usr/bin/env python3
"""
Extract FPKM and TPM values from StringTie GTF files
"""

import pandas as pd
import re
import os
from collections import defaultdict

def extract_fpkm_tpm_from_gtf(gtf_file):
    """Extract FPKM and TPM values from a single GTF file"""
    fpkm_data = {}
    tpm_data = {}
    
    with open(gtf_file, 'r') as f:
        for line in f:
            if line.startswith('#') or line.strip() == '':
                continue
            
            fields = line.strip().split('\t')
            if len(fields) < 9:
                continue
            
            # Only process transcript lines (not exon lines)
            if fields[2] != 'transcript':
                continue
            
            attributes = fields[8]
            
            # Extract gene_id, FPKM, and TPM using regex
            gene_id_match = re.search(r'gene_id "([^"]+)"', attributes)
            fpkm_match = re.search(r'FPKM "([^"]+)"', attributes)
            tpm_match = re.search(r'TPM "([^"]+)"', attributes)
            
            if gene_id_match and fpkm_match and tpm_match:
                gene_id = gene_id_match.group(1)
                fpkm = float(fpkm_match.group(1))
                tpm = float(tpm_match.group(1))
                
                # For genes with multiple transcripts, sum the values
                if gene_id in fpkm_data:
                    fpkm_data[gene_id] += fpkm
                    tpm_data[gene_id] += tpm
                else:
                    fpkm_data[gene_id] = fpkm
                    tpm_data[gene_id] = tpm
    
    return fpkm_data, tpm_data

def create_expression_matrices():
    """Create FPKM and TPM expression matrices for hagfish samples"""
    
    # Sample mapping
    sample_map = {
        'Unknown_CK259-004T0001_good': 'hg3L1',
        'Unknown_CK259-004T0002_good': 'hg3L2', 
        'Unknown_CK259-004T0003_good': 'hg3L3',
        'Unknown_CK259-004T0004_good': 'hg3L4',
        'Unknown_CK259-004T0005_good': 'hg3L5',
        'Unknown_CK259-004T0006_good': 'hg3L6',
        'Unknown_CK259-004T0007_good': 'hg3sex',
        'Unknown_CK259-004T0008_good': 'hg3intes',
        'Unknown_CK259-004T0009_good': 'hg3noto',
        'Unknown_CK259-004T0010_good': 'hg3axon',
        'Unknown_CK259-004T0011_good': 'hg3heart',
        'Unknown_CK259-004T0012_good': 'hg3muscl',
        'Unknown_CK259-004T0013_good': 'hg3gall',
        'Unknown_CK259-004T0014_good': 'hg3liver',
        'Unknown_CK259-004T0015_good': 'hg3brain',
        'Unknown_CK259-004T0016_good': 'hg3solo',
        'Unknown_CK259-004T0017_good': 'hg3skin1',
        'Unknown_CK259-004T0018_good': 'hg3ki',
        'Unknown_CK259-004T0019_good': 'hg3leu',
        'Unknown_CK259-004T0022_good': 'hg3skin2',
        'Unknown_CK259-004T0023_good': 'hgovary'
    }
    
    # Get hagfish samples only
    hagfish_samples = [k for k, v in sample_map.items() if v.startswith('hg')]
    
    print(f"Processing {len(hagfish_samples)} hagfish samples...")
    
    # Collect all data
    all_fpkm_data = {}
    all_tpm_data = {}
    all_genes = set()
    
    stringtie_dir = "04_Assembly/stringtie_individual"
    
    for sample in hagfish_samples:
        gtf_file = f"{stringtie_dir}/{sample}.gtf"
        
        if not os.path.exists(gtf_file):
            print(f"Warning: {gtf_file} not found, skipping...")
            continue
        
        print(f"Processing {sample}...")
        fpkm_data, tpm_data = extract_fpkm_tpm_from_gtf(gtf_file)
        
        sample_name = sample_map[sample]
        all_fpkm_data[sample_name] = fpkm_data
        all_tpm_data[sample_name] = tpm_data
        all_genes.update(fpkm_data.keys())
        
        print(f"  Found {len(fpkm_data)} genes")
    
    print(f"Total unique genes: {len(all_genes)}")
    
    # Create DataFrames
    all_genes = sorted(list(all_genes))
    sample_names = [sample_map[s] for s in hagfish_samples if sample_map[s] in all_fpkm_data]
    
    # FPKM matrix
    fpkm_matrix = []
    for gene in all_genes:
        row = [gene]
        for sample_name in sample_names:
            if gene in all_fpkm_data[sample_name]:
                row.append(all_fpkm_data[sample_name][gene])
            else:
                row.append(0.0)
        fpkm_matrix.append(row)
    
    # TPM matrix
    tpm_matrix = []
    for gene in all_genes:
        row = [gene]
        for sample_name in sample_names:
            if gene in all_tpm_data[sample_name]:
                row.append(all_tpm_data[sample_name][gene])
            else:
                row.append(0.0)
        tpm_matrix.append(row)
    
    # Create DataFrames
    columns = ['Geneid'] + sample_names
    fpkm_df = pd.DataFrame(fpkm_matrix, columns=columns)
    tpm_df = pd.DataFrame(tpm_matrix, columns=columns)
    
    # Save matrices
    fpkm_output = "08_Results/hagfish_fpkm_matrix.txt"
    tpm_output = "08_Results/hagfish_tpm_matrix.txt"
    
    fpkm_df.to_csv(fpkm_output, sep='\t', index=False, float_format='%.6f')
    tpm_df.to_csv(tpm_output, sep='\t', index=False, float_format='%.6f')
    
    print(f"\nFPKM matrix saved to: {fpkm_output}")
    print(f"TPM matrix saved to: {tpm_output}")
    print(f"Matrix dimensions: {len(all_genes)} genes x {len(sample_names)} samples")
    
    return fpkm_df, tpm_df

if __name__ == "__main__":
    create_expression_matrices()
