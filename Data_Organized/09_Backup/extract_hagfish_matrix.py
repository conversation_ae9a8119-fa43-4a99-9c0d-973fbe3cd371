#!/usr/bin/env python3
"""
Extract hagfish-only expression matrix from the full expression matrix
"""

import pandas as pd
import sys

def extract_hagfish_matrix():
    # Sample mapping from file names to sample IDs
    sample_map = {
        'Unknown_CK259-004T0001_good': 'hg3L1',
        'Unknown_CK259-004T0002_good': 'hg3L2', 
        'Unknown_CK259-004T0003_good': 'hg3L3',
        'Unknown_CK259-004T0004_good': 'hg3L4',
        'Unknown_CK259-004T0005_good': 'hg3L5',
        'Unknown_CK259-004T0006_good': 'hg3L6',
        'Unknown_CK259-004T0007_good': 'hg3sex',
        'Unknown_CK259-004T0008_good': 'hg3intes',
        'Unknown_CK259-004T0009_good': 'hg3noto',
        'Unknown_CK259-004T0010_good': 'hg3axon',
        'Unknown_CK259-004T0011_good': 'hg3heart',
        'Unknown_CK259-004T0012_good': 'hg3muscl',
        'Unknown_CK259-004T0013_good': 'hg3gall',
        'Unknown_CK259-004T0014_good': 'hg3liver',
        'Unknown_CK259-004T0015_good': 'hg3brain',
        'Unknown_CK259-004T0016_good': 'hg3solo',
        'Unknown_CK259-004T0017_good': 'hg3skin1',
        'Unknown_CK259-004T0018_good': 'hg3ki',
        'Unknown_CK259-004T0019_good': 'hg3leu',
        'Unknown_CK259-004T0020_good': 'LjB-24h',
        'Unknown_CK259-004T0021_good': 'LjI3-24h',
        'Unknown_CK259-004T0022_good': 'hg3skin2',
        'Unknown_CK259-004T0023_good': 'hgovary',
        'Unknown_CK259-004T0024_good': 'LjI3-8h'
    }
    
    # Get hagfish samples only
    hagfish_samples = [k for k, v in sample_map.items() if v.startswith('hg')]
    
    print(f"Processing {len(hagfish_samples)} hagfish samples...")
    
    # Read the expression matrix
    input_file = "08_Results/final_expression_matrix.txt"
    
    # Read header to identify column positions
    with open(input_file, 'r') as f:
        # Skip comment line
        f.readline()
        header = f.readline().strip().split('\t')
    
    # Find columns for hagfish samples
    hagfish_cols = ['Geneid']  # Always include gene ID column
    col_mapping = {}
    
    for i, col in enumerate(header):
        for sample in hagfish_samples:
            if sample in col:
                hagfish_cols.append(col)
                col_mapping[col] = sample_map[sample]
                break
    
    print(f"Found {len(hagfish_cols)-1} hagfish sample columns")
    
    # Read the full matrix and extract hagfish columns
    df = pd.read_csv(input_file, sep='\t', comment='#')
    
    # Select only hagfish columns
    hagfish_df = df[hagfish_cols].copy()
    
    # Rename columns to meaningful sample names
    new_columns = ['Geneid']
    for col in hagfish_cols[1:]:  # Skip Geneid column
        if col in col_mapping:
            new_columns.append(col_mapping[col])
        else:
            new_columns.append(col)
    
    hagfish_df.columns = new_columns
    
    # Save hagfish-only matrix
    output_file = "08_Results/hagfish_expression_matrix.txt"
    hagfish_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Hagfish expression matrix saved to: {output_file}")
    print(f"Matrix dimensions: {hagfish_df.shape[0]} genes x {hagfish_df.shape[1]-1} samples")
    
    return hagfish_df

if __name__ == "__main__":
    extract_hagfish_matrix()
