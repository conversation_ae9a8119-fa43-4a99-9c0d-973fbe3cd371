# 盲鳗RNA-seq数据备份信息

## 备份时间
2024年7月28日

## 备份内容

### 表达矩阵文件
1. final_expression_matrix.txt - 完整24样本Raw Counts矩阵
2. hagfish_expression_matrix.txt - 盲鳗21样本Raw Counts矩阵
3. hagfish_fpkm_matrix.txt - 盲鳗21样本FPKM矩阵
4. hagfish_tpm_matrix.txt - 盲鳗21样本TPM矩阵

### 注释文件
5. merged_transcriptome.gtf - 合并转录组注释
6. predicted_proteins.fa - 预测蛋白质序列

### 样本信息文件
7. sample.ID - 原始样本ID列表
8. sample_mapping.txt - 完整样本对应关系
9. hagfish_samples_only.txt - 盲鳗样本信息
10. hagfish_tissue_groups.txt - 盲鳗组织分类
11. data_md5.txt - MD5校验文件

### 分析脚本
12. extract_hagfish_matrix.py - 提取盲鳗表达矩阵脚本
13. extract_fpkm_tpm.py - 提取FPKM/TPM矩阵脚本
14. hagfish_data_summary.py - 数据统计脚本
15. compare_expression_data_types.py - 表达量类型对比脚本

### 项目文档
16. README.md - 项目说明文档

## 数据统计
- 盲鳗样本: 21个
- 基因数量: 23,576个 (Raw Counts), 45,972个 (FPKM/TPM)
- 总测序读数: 571,364,206
- 表达基因: 22,650个

## 备份目的
在进行基因注释和功能分析之前，保护已生成的表达矩阵数据。

## 注意事项
- 当前基因ID为XLOC格式，缺乏功能注释
- 需要进行基因功能注释以获得有意义的基因名称
- 备份文件可用于数据恢复或重新分析
