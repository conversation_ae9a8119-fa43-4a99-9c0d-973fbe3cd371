# RNA-seq 数据分析项目

## 项目概述
本项目包含24个样本的RNA-seq数据分析，主要研究盲鳗（hagfish）不同组织和发育阶段的基因表达模式。
- **主要研究对象**: 盲鳗（21个样本）
- **对照样本**: 七鳃鳗（3个样本，可忽略）
- **研究内容**: 从原始测序数据到基因表达定量的完整分析流程

## 目录结构说明

### 01_RawData/ - 原始数据
- `fastq/` - 原始FASTQ测序文件（24个样本，双端测序）
- `metadata/` - 样本信息和数据校验文件
  - `sample.ID` - 样本ID列表
  - `data_md5.txt` - MD5校验文件
  - `sample_mapping.txt` - 完整的样本名称对应表
  - `hagfish_samples_only.txt` - 盲鳗样本信息
  - `hagfish_tissue_groups.txt` - 盲鳗组织分类信息

### 02_QualityControl/ - 质量控制
- `fastqc_reports/` - FastQC质量评估报告
- `trimmed_data/` - Trim Galore质量修剪后的数据
- `multiqc_report/` - MultiQC汇总报告

### 03_Alignment/ - 序列比对
- `hisat2/` - HISAT2比对结果（BAM文件、索引、日志等）
- `gsnap/` - GSNAP比对结果（BAM文件、索引等）
- `reference/` - 参考基因组索引文件

### 04_Assembly/ - 转录本组装
- `stringtie_individual/` - StringTie单样本转录本组装结果（GTF文件）
- `stringtie_merged/` - StringTie合并转录本组装结果
  - `merged.gtf` - 合并的转录本注释文件
  - `transcripts.fa` - 转录本序列
  - TransDecoder预测的编码序列相关文件
- `transdecoder/` - 编码序列预测结果

### 05_Quantification/ - 表达定量
- `featurecounts/` - featureCounts基因计数原始结果
- `expression_matrix/` - 处理后的基因表达矩阵

### 06_Scripts/ - 分析脚本
- `alignment/` - 序列比对相关脚本
  - `Hisat2.sh` - HISAT2比对脚本
  - `Gsnap.sh` - GSNAP比对脚本
- `assembly/` - 转录本组装相关脚本
  - `Stringtie.sh` - StringTie组装脚本
  - `merge_GTF.sh` - GTF合并脚本
  - `TransDecoder.sh` - 编码序列预测脚本
- `quantification/` - 表达定量相关脚本
  - `featureCounts_batch.sh` - 批量基因计数脚本

### 07_Logs/ - 日志文件
包含所有分析步骤的日志文件，用于排错和质量检查。

### 08_Results/ - 最终结果
- `final_expression_matrix.txt` - 完整的基因表达矩阵（24个样本，Raw Counts）
- `hagfish_expression_matrix.txt` - 盲鳗专用基因表达矩阵（21个样本，Raw Counts）
- `hagfish_fpkm_matrix.txt` - 盲鳗FPKM表达矩阵（21个样本，45,972个基因）
- `hagfish_tpm_matrix.txt` - 盲鳗TPM表达矩阵（21个样本，45,972个基因）
- `merged_transcriptome.gtf` - 合并的转录组注释文件
- `predicted_proteins.fa` - 预测的蛋白质序列

## 分析流程
1. **质量控制**: FastQC评估 → Trim Galore修剪
2. **序列比对**: HISAT2/GSNAP比对到参考基因组
3. **转录本组装**: StringTie组装 → 合并转录本
4. **编码序列预测**: TransDecoder预测CDS
5. **表达定量**: featureCounts基因计数

## 样本信息
- **总样本数量**: 24个（盲鳗21个 + 七鳃鳗3个）
- **测序类型**: 双端测序
- **盲鳗样本**: 涵盖6个发育阶段 + 15个不同组织类型
  - 发育阶段: hg3L1-L6（幼体发育）
  - 神经系统: hg3brain（脑）、hg3axon（轴突）
  - 消化系统: hg3liver（肝脏）、hg3intes（肠道）、hg3gall（胆囊）
  - 循环系统: hg3heart（心脏）
  - 排泄系统: hg3ki（肾脏）
  - 免疫系统: hg3leu（白细胞）
  - 肌肉系统: hg3muscl（肌肉）
  - 结构系统: hg3noto（脊索）
  - 生殖系统: hg3sex（性器官）、hgovary（卵巢）
  - 皮肤系统: hg3skin1、hg3skin2（皮肤）
  - 其他: hg3solo（单独样本）

## 使用说明

### 表达量数据类型
- **Raw Counts**: `hagfish_expression_matrix.txt` - 适用于差异表达分析（DESeq2, edgeR）
- **FPKM**: `hagfish_fpkm_matrix.txt` - 基因长度标准化，适用于基因间比较
- **TPM**: `hagfish_tpm_matrix.txt` - 样本间标准化，适用于样本间比较和可视化

### 关键文件
- **样本信息**: `01_RawData/metadata/hagfish_tissue_groups.txt`（组织分类）
- **分析脚本**: `06_Scripts/` 目录（包含提取各种表达量数据的脚本）
- **分析日志**: `07_Logs/` 目录

## 后续分析建议
基于盲鳗专用数据，可以进行：
- **组织特异性表达分析**: 比较不同组织间的基因表达差异
- **发育阶段分析**: 研究幼体发育过程中的基因表达变化
- **功能富集分析**: 基于差异表达基因进行GO/KEGG分析
- **共表达网络分析**: 构建组织特异性基因共表达网络
- **进化分析**: 与其他脊椎动物比较，研究盲鳗的进化特征
- **新转录本功能注释**: 利用预测的蛋白质序列进行功能预测
