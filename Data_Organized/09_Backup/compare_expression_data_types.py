#!/usr/bin/env python3
"""
Compare different expression data types (Raw Counts, FPKM, TPM)
"""

import pandas as pd
import numpy as np

def compare_expression_data():
    print("=== 盲鳗RNA-seq表达量数据类型对比 ===\n")
    
    # Read all three types of data
    counts_df = pd.read_csv("08_Results/hagfish_expression_matrix.txt", sep='\t')
    fpkm_df = pd.read_csv("08_Results/hagfish_fpkm_matrix.txt", sep='\t')
    tpm_df = pd.read_csv("08_Results/hagfish_tpm_matrix.txt", sep='\t')
    
    print("1. 数据维度对比:")
    print(f"   Raw Counts: {counts_df.shape[0]:,} 基因 × {counts_df.shape[1]-1} 样本")
    print(f"   FPKM:       {fpkm_df.shape[0]:,} 基因 × {fpkm_df.shape[1]-1} 样本")
    print(f"   TPM:        {tpm_df.shape[0]:,} 基因 × {tpm_df.shape[1]-1} 样本")
    
    # Get expression data (excluding gene ID column)
    counts_data = counts_df.iloc[:, 1:].values
    fpkm_data = fpkm_df.iloc[:, 1:].values
    tpm_data = tpm_df.iloc[:, 1:].values
    
    print(f"\n2. 数据范围对比:")
    print(f"   Raw Counts:")
    print(f"     - 最小值: {counts_data.min():,.0f}")
    print(f"     - 最大值: {counts_data.max():,.0f}")
    print(f"     - 平均值: {counts_data.mean():,.1f}")
    print(f"     - 中位数: {np.median(counts_data):,.1f}")
    
    print(f"   FPKM:")
    print(f"     - 最小值: {fpkm_data.min():.6f}")
    print(f"     - 最大值: {fpkm_data.max():,.2f}")
    print(f"     - 平均值: {fpkm_data.mean():.3f}")
    print(f"     - 中位数: {np.median(fpkm_data):.6f}")
    
    print(f"   TPM:")
    print(f"     - 最小值: {tpm_data.min():.6f}")
    print(f"     - 最大值: {tpm_data.max():,.2f}")
    print(f"     - 平均值: {tpm_data.mean():.3f}")
    print(f"     - 中位数: {np.median(tpm_data):.6f}")
    
    print(f"\n3. 样本总和对比（用于验证标准化）:")
    sample_names = counts_df.columns[1:].tolist()
    
    print(f"   Raw Counts 样本总和:")
    counts_sums = counts_data.sum(axis=0)
    print(f"     - 最小: {counts_sums.min():,.0f}")
    print(f"     - 最大: {counts_sums.max():,.0f}")
    print(f"     - 平均: {counts_sums.mean():,.0f}")
    
    print(f"   FPKM 样本总和:")
    fpkm_sums = fpkm_data.sum(axis=0)
    print(f"     - 最小: {fpkm_sums.min():,.1f}")
    print(f"     - 最大: {fpkm_sums.max():,.1f}")
    print(f"     - 平均: {fpkm_sums.mean():,.1f}")
    
    print(f"   TPM 样本总和:")
    tpm_sums = tpm_data.sum(axis=0)
    print(f"     - 最小: {tpm_sums.min():,.1f}")
    print(f"     - 最大: {tpm_sums.max():,.1f}")
    print(f"     - 平均: {tpm_sums.mean():,.1f}")
    print(f"     - 理论值: 1,000,000 (TPM应该总和为1M)")
    
    print(f"\n4. 表达基因数量对比:")
    # Count expressed genes (> 0)
    counts_expressed = (counts_data > 0).sum(axis=0)
    fpkm_expressed = (fpkm_data > 0).sum(axis=0)
    tpm_expressed = (tpm_data > 0).sum(axis=0)
    
    print(f"   Raw Counts 表达基因数:")
    print(f"     - 最小: {counts_expressed.min():,}")
    print(f"     - 最大: {counts_expressed.max():,}")
    print(f"     - 平均: {counts_expressed.mean():,.0f}")
    
    print(f"   FPKM 表达基因数:")
    print(f"     - 最小: {fpkm_expressed.min():,}")
    print(f"     - 最大: {fpkm_expressed.max():,}")
    print(f"     - 平均: {fpkm_expressed.mean():,.0f}")
    
    print(f"   TPM 表达基因数:")
    print(f"     - 最小: {tpm_expressed.min():,}")
    print(f"     - 最大: {tpm_expressed.max():,}")
    print(f"     - 平均: {tpm_expressed.mean():,.0f}")
    
    print(f"\n5. 数据类型使用建议:")
    print(f"   📊 Raw Counts:")
    print(f"      - 用途: 差异表达分析")
    print(f"      - 工具: DESeq2, edgeR, limma-voom")
    print(f"      - 特点: 保留原始计数信息，适合统计建模")
    
    print(f"   📈 FPKM:")
    print(f"      - 用途: 基因间表达水平比较")
    print(f"      - 特点: 考虑基因长度，但不适合样本间比较")
    print(f"      - 注意: 不推荐用于差异表达分析")
    
    print(f"   📉 TPM:")
    print(f"      - 用途: 样本间比较、数据可视化")
    print(f"      - 特点: 样本总和标准化，便于比较")
    print(f"      - 推荐: 热图、PCA、聚类分析")
    
    print(f"\n=== 对比分析完成 ===")

if __name__ == "__main__":
    compare_expression_data()
