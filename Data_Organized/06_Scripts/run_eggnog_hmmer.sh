#!/bin/bash

# 基于HMMER方法的EggNOG-mapper分析
# 与Diamond方法对比

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper HMMER方法 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_hmmer"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_hmmer"

# 检查输入文件
if [ ! -f "$PROTEIN_FILE" ]; then
    echo "错误: 蛋白质文件不存在: $PROTEIN_FILE"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 步骤1: 运行EggNOG-mapper (HMMER方法) ==="
echo "注意: 使用HMMER方法，更精确但速度较慢"

# 运行EggNOG-mapper with HMMER
# 参数说明:
# -m hmmer: 使用HMMER方法，更精确的同源搜索
# --hmm_maxhits: HMMER最大命中数
# --hmm_evalue: HMMER E-value阈值
# --hmm_score: HMMER分数阈值
# --hmm_maxseqlen: 最大序列长度

emapper.py \
    -m hmmer \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 4 \
    --override \
    --hmm_maxhits 1 \
    --hmm_evalue 0.001 \
    --hmm_score 20 \
    --hmm_maxseqlen 5000

echo ""
echo "=== 步骤2: 检查结果 ==="

# 检查主要输出文件
ANNOTATION_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$ANNOTATION_FILE" ]; then
    echo "✅ HMMER注释完成！"
    echo "注释文件: $ANNOTATION_FILE"
    
    # 统计注释结果
    TOTAL_LINES=$(wc -l < "$ANNOTATION_FILE")
    HEADER_LINES=$(grep -c "^#" "$ANNOTATION_FILE")
    ANNOTATED_SEQS=$((TOTAL_LINES - HEADER_LINES))
    
    echo "总行数: $TOTAL_LINES"
    echo "注释序列数: $ANNOTATED_SEQS"
    
    # 统计有功能注释的序列
    echo ""
    echo "=== HMMER注释统计 ==="
    
    # 有GO注释的序列
    GO_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有GO注释的序列: $GO_COUNT"
    
    # 有KEGG注释的序列
    KEGG_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有KEGG注释的序列: $KEGG_COUNT"
    
    # 有COG注释的序列
    COG_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f7 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有COG注释的序列: $COG_COUNT"
    
    # 有基因名的序列
    GENE_NAME_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有基因名的序列: $GENE_NAME_COUNT"
    
    echo ""
    echo "前5行注释结果预览:"
    head -n 10 "$ANNOTATION_FILE"
    
else
    echo "❌ HMMER注释失败，请检查错误信息"
    exit 1
fi

echo ""
echo "=== 步骤3: 与Diamond方法对比 ==="

DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

if [ -f "$DIAMOND_FILE" ]; then
    echo "对比Diamond和HMMER方法的注释结果..."
    
    # Diamond统计
    DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
    DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo ""
    echo "方法对比:"
    echo "                Diamond    HMMER"
    echo "总注释序列:     $DIAMOND_TOTAL       $ANNOTATED_SEQS"
    echo "GO注释:         $DIAMOND_GO       $GO_COUNT"
    echo "KEGG注释:       $DIAMOND_KEGG       $KEGG_COUNT"
    echo "基因名:         $DIAMOND_GENE       $GENE_NAME_COUNT"
    
else
    echo "未找到Diamond结果文件，无法对比"
fi

echo ""
echo "完成时间: $(date)"
echo "=== EggNOG-mapper HMMER方法完成 ==="

# 列出所有输出文件
echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"
