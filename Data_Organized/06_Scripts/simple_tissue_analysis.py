#!/usr/bin/env python3
"""
盲鳗组织特异性表达分析 - 简化版
"""

import pandas as pd
import numpy as np

def main():
    print("=== 盲鳗组织特异性表达分析 ===\n")
    
    # 1. 读取数据
    print("1. 读取表达数据...")
    
    try:
        # 读取表达矩阵 (Count数据)
        expr_data = pd.read_csv("../08_Results/hagfish_expression_matrix.txt", 
                               sep='\t', index_col=0)
        print(f"   - 表达矩阵: {expr_data.shape[0]} 基因 x {expr_data.shape[1]} 样本")
        
        # 显示样本名称
        print("   - 样本名称:", list(expr_data.columns))
        
    except Exception as e:
        print(f"   - 错误: 无法读取表达数据 - {e}")
        return
    
    # 2. 样本信息
    samples = expr_data.columns.tolist()
    
    # 根据样本名称推断组织类型
    sample_groups = []
    for sample in samples:
        if 'L1' in sample or 'L2' in sample or 'L3' in sample or 'L4' in sample or 'L5' in sample or 'L6' in sample:
            sample_groups.append('gill')
        elif 'intes' in sample:
            sample_groups.append('intestine')
        elif 'ki' in sample:
            sample_groups.append('kidney')
        elif 'leu' in sample:
            sample_groups.append('leukocyte')
        else:
            sample_groups.append('other')
    
    sample_info = pd.DataFrame({
        'sample': samples,
        'group': sample_groups
    })
    
    print("\n2. 样本分组:")
    print(sample_info.to_string(index=False))
    
    # 统计各组样本数
    group_counts = sample_info['group'].value_counts()
    print("\n   各组样本数:")
    for group, count in group_counts.items():
        print(f"   - {group}: {count}")
    
    # 3. 数据预处理
    print("\n3. 数据预处理...")
    
    # 过滤低表达基因
    keep_genes = (expr_data >= 5).sum(axis=1) >= 2  # 至少2个样本中表达量>=5
    expr_filtered = expr_data[keep_genes]
    print(f"   - 过滤前: {expr_data.shape[0]} 基因")
    print(f"   - 过滤后: {expr_filtered.shape[0]} 基因")
    
    # 4. 组织特异性分析
    def analyze_tissue(target_group, expr_data, sample_info):
        print(f"\n=== {target_group.capitalize()} vs 其他组织分析 ===")
        
        # 获取样本
        target_samples = sample_info[sample_info['group'] == target_group]['sample'].tolist()
        other_samples = sample_info[sample_info['group'] != target_group]['sample'].tolist()
        
        print(f"   - {target_group}组样本: {target_samples}")
        print(f"   - 其他组样本数: {len(other_samples)}")
        
        if len(target_samples) == 0:
            print("   - 跳过: 无目标组样本")
            return None
        
        # 计算表达量
        target_expr = expr_data[target_samples]
        other_expr = expr_data[other_samples]
        
        # 计算均值
        target_mean = target_expr.mean(axis=1) if len(target_samples) > 1 else target_expr.iloc[:, 0]
        other_mean = other_expr.mean(axis=1)
        
        # 计算fold change
        log2fc = np.log2((target_mean + 1) / (other_mean + 1))
        
        # 创建结果
        results = pd.DataFrame({
            'gene_id': expr_data.index,
            'target_mean': target_mean,
            'other_mean': other_mean,
            'log2FoldChange': log2fc,
            'fold_change': (target_mean + 1) / (other_mean + 1)
        })
        
        # 筛选高表达差异基因
        # 条件：在目标组织中高表达 且 fold change > 4
        high_expr_threshold = 20  # 表达量阈值
        fc_threshold = 2  # log2(4) = 2
        
        high_expr_in_target = results['target_mean'] > high_expr_threshold
        high_fold_change = abs(results['log2FoldChange']) > fc_threshold
        
        significant = results[high_expr_in_target & high_fold_change].copy()
        significant = significant.sort_values('log2FoldChange', ascending=False)
        
        up_regulated = significant[significant['log2FoldChange'] > 0]
        down_regulated = significant[significant['log2FoldChange'] < 0]
        
        print(f"   - 高表达基因 (>{high_expr_threshold}): {high_expr_in_target.sum()}")
        print(f"   - 高倍数差异基因 (>4倍): {high_fold_change.sum()}")
        print(f"   - 显著差异基因: {len(significant)} (上调: {len(up_regulated)}, 下调: {len(down_regulated)})")
        
        # 显示top基因
        if len(up_regulated) > 0:
            print(f"   - Top 5 上调基因:")
            for i, (_, row) in enumerate(up_regulated.head(5).iterrows()):
                print(f"     {i+1}. {row['gene_id']}: {row['fold_change']:.1f}倍 (target: {row['target_mean']:.1f}, other: {row['other_mean']:.1f})")
        
        # 保存结果
        output_file = f"../10_Annotation/{target_group}_vs_others_results.txt"
        results.to_csv(output_file, sep='\t', index=False)
        print(f"   - 完整结果已保存: {output_file}")
        
        if len(significant) > 0:
            sig_file = f"../10_Annotation/{target_group}_significant_genes.txt"
            significant.to_csv(sig_file, sep='\t', index=False)
            print(f"   - 显著基因已保存: {sig_file}")
        
        return {
            'total': len(results),
            'significant': len(significant),
            'up_regulated': len(up_regulated),
            'down_regulated': len(down_regulated),
            'top_up': up_regulated.head(10) if len(up_regulated) > 0 else pd.DataFrame()
        }
    
    # 5. 执行分析
    print("\n4. 执行组织特异性分析...")
    
    target_tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    results_summary = {}
    
    for tissue in target_tissues:
        if tissue in sample_info['group'].values:
            results_summary[tissue] = analyze_tissue(tissue, expr_filtered, sample_info)
        else:
            print(f"\n=== {tissue.capitalize()} ===")
            print(f"   - 跳过: 无{tissue}样本")
    
    # 6. 汇总结果
    print("\n5. 分析结果汇总:")
    summary_data = []
    
    for tissue, result in results_summary.items():
        if result:
            print(f"   - {tissue.capitalize()}: {result['significant']}个差异基因 (↑{result['up_regulated']}, ↓{result['down_regulated']})")
            summary_data.append({
                'tissue': tissue,
                'total_genes': result['total'],
                'significant_genes': result['significant'],
                'up_regulated': result['up_regulated'],
                'down_regulated': result['down_regulated']
            })
    
    # 保存汇总
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv("../10_Annotation/tissue_analysis_summary.txt", sep='\t', index=False)
        print(f"\n   - 汇总结果已保存: ../10_Annotation/tissue_analysis_summary.txt")
    
    print("\n=== 分析完成 ===")
    print("下一步可以:")
    print("1. 结合EggNOG注释查看基因功能")
    print("2. 分析免疫相关基因")
    print("3. 进行GO/KEGG富集分析")

if __name__ == "__main__":
    main()
