#!/bin/bash
# 批量处理所有 Unknown 开头的 FASTQ 文件，进行截短与质控

# 定义原始数据目录和输出目录（请根据实际路径修改）
rawdata="/public/home/<USER>/2025hagfish/NGS/Data"
cleandata="/public/home/<USER>/2025hagfish/NGS/Data/trim_galore"

# 如果输出目录不存在则创建
mkdir -p "$cleandata"

# 提取所有样本的唯一 ID（假定文件命名格式为 Unknownxxx.fq.gz）
samples=$(ls "$rawdata"/Unknown*_1.fq.gz 2>/dev/null | sed 's/_1\.fq\.gz//' | xargs -n1 basename | sort | uniq)

echo "检测到以下样本："
echo "$samples"

# 对每个样本进行处理（添加引号避免空格）
for id in $samples; do
    echo "正在处理样本 $id ..."
    trim_galore \
        --phred33 \
        -q 20 \
        --length 36 \
        --stringency 5 \
        --fastqc \
        --paired \
        --max_n 3 \
        -o "$cleandata" \
        "${rawdata}/${id}_1.fq.gz" "${rawdata}/${id}_2.fq.gz"
done

echo "所有样本处理完成！"
