#!/usr/bin/env Rscript

# 盲鳗免疫组织DESeq2差异表达分析
# 鳃(L1-L6) vs 其他组织, 肠 vs 其他组织, 肾 vs 其他组织, 白细胞 vs 其他组织

# 检查并安装必要的包
required_packages <- c("DESeq2", "ggplot2", "pheatmap", "RColorBrewer", "dplyr")
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]

if(length(missing_packages) > 0) {
  cat("安装缺失的R包...\n")
  if (!requireNamespace("BiocManager", quietly = TRUE))
    install.packages("BiocManager")
  BiocManager::install(missing_packages)
}

# 加载包
suppressMessages({
  library(DESeq2)
  library(ggplot2)
  library(pheatmap)
  library(RColorBrewer)
  library(dplyr)
})

cat("=== 盲鳗免疫组织DESeq2差异表达分析 ===\n\n")

# 1. 读取Count数据
cat("1. 读取Count表达数据...\n")
count_data <- read.table("../08_Results/hagfish_expression_matrix.txt", 
                        header = TRUE, row.names = 1, sep = "\t")

cat(sprintf("   - Count矩阵: %d 基因 x %d 样本\n", nrow(count_data), ncol(count_data)))

# 确保数据为整数
count_data <- round(count_data)

# 2. 创建样本信息
cat("2. 创建样本信息...\n")
sample_names <- colnames(count_data)
cat("   - 样本名称:", paste(sample_names, collapse = ", "), "\n")

# 根据样本名称定义组织类型
sample_info <- data.frame(
  sample = sample_names,
  tissue = c("gill", "gill", "gill", "gill", "gill", "gill",        # hg3L1-L6 (鳃)
            "other", "intestine", "other", "other",                 # hg3sex, hg3intes, hg3noto, hg3axon
            "other", "other", "other", "other",                     # hg3heart, hg3muscl, hg3gall, hg3liver
            "other", "other", "other", "kidney",                    # hg3brain, hg3solo, hg3skin1, hg3ki
            "leukocyte", "other", "other"),                         # hg3leu, hg3skin2, hg3ovary
  stringsAsFactors = FALSE
)

print(sample_info)

# 3. 数据预处理
cat("\n3. 数据预处理...\n")

# 过滤低表达基因
keep_genes <- rowSums(count_data >= 10) >= 3  # 至少3个样本中表达量>=10
count_filtered <- count_data[keep_genes, ]
cat(sprintf("   - 过滤前: %d 基因\n", nrow(count_data)))
cat(sprintf("   - 过滤后: %d 基因\n", nrow(count_filtered)))

# 4. DESeq2差异表达分析函数
perform_deseq2_analysis <- function(target_tissue, count_data, sample_info, output_prefix) {
  cat(sprintf("\n=== %s vs 其他组织 DESeq2分析 ===\n", target_tissue))
  
  # 创建分组信息
  sample_info$group <- ifelse(sample_info$tissue == target_tissue, target_tissue, "other")
  
  # 检查样本数
  target_n <- sum(sample_info$group == target_tissue)
  other_n <- sum(sample_info$group == "other")
  cat(sprintf("   - %s组: %d 样本\n", target_tissue, target_n))
  cat(sprintf("   - 其他组: %d 样本\n", other_n))
  
  if(target_n < 2) {
    cat("   - 警告: 目标组样本数少于2，无法进行DESeq2分析\n")
    return(NULL)
  }
  
  # 创建DESeq2对象
  dds <- DESeqDataSetFromMatrix(countData = count_data,
                               colData = sample_info,
                               design = ~ group)
  
  # 设置参考水平
  dds$group <- relevel(dds$group, ref = "other")
  
  # 运行DESeq2
  cat("   - 运行DESeq2分析...\n")
  dds <- DESeq(dds)
  
  # 获取结果
  res <- results(dds, contrast = c("group", target_tissue, "other"))
  
  # 整理结果
  res_df <- as.data.frame(res)
  res_df$gene_id <- rownames(res_df)
  res_df <- res_df[!is.na(res_df$padj), ]
  
  # 添加标准化表达量
  normalized_counts <- counts(dds, normalized = TRUE)
  target_samples <- sample_info$sample[sample_info$group == target_tissue]
  other_samples <- sample_info$sample[sample_info$group == "other"]
  
  res_df$target_mean <- rowMeans(normalized_counts[, target_samples, drop = FALSE])
  res_df$other_mean <- rowMeans(normalized_counts[, other_samples, drop = FALSE])
  
  # 筛选显著差异基因
  sig_genes <- res_df[res_df$padj < 0.05 & abs(res_df$log2FoldChange) > 1, ]
  up_genes <- sig_genes[sig_genes$log2FoldChange > 0, ]
  down_genes <- sig_genes[sig_genes$log2FoldChange < 0, ]
  
  cat(sprintf("   - 显著差异基因: %d (上调: %d, 下调: %d)\n", 
              nrow(sig_genes), nrow(up_genes), nrow(down_genes)))
  
  # 保存结果
  output_file <- sprintf("../10_Annotation/%s_vs_others_DESeq2.txt", output_prefix)
  write.table(res_df, output_file, sep = "\t", row.names = FALSE, quote = FALSE)
  cat(sprintf("   - 完整结果已保存: %s\n", output_file))
  
  if(nrow(sig_genes) > 0) {
    sig_file <- sprintf("../10_Annotation/%s_significant_DEGs.txt", output_prefix)
    write.table(sig_genes, sig_file, sep = "\t", row.names = FALSE, quote = FALSE)
    cat(sprintf("   - 显著基因已保存: %s\n", sig_file))
  }
  
  return(list(
    dds = dds,
    results = res_df,
    significant = sig_genes,
    up_regulated = up_genes,
    down_regulated = down_genes,
    normalized_counts = normalized_counts
  ))
}

# 5. 执行分析
cat("\n4. 执行DESeq2分析...\n")

analyses <- list()

# 鳃 (L1-L6合并) vs 其他组织
analyses$gill <- perform_deseq2_analysis("gill", count_filtered, sample_info, "gill")

# 肠 vs 其他组织
analyses$intestine <- perform_deseq2_analysis("intestine", count_filtered, sample_info, "intestine")

# 肾 vs 其他组织
analyses$kidney <- perform_deseq2_analysis("kidney", count_filtered, sample_info, "kidney")

# 白细胞 vs 其他组织
analyses$leukocyte <- perform_deseq2_analysis("leukocyte", count_filtered, sample_info, "leukocyte")

# 6. 结果汇总
cat("\n5. 分析结果汇总:\n")
summary_data <- data.frame(
  tissue = character(),
  total_genes = integer(),
  significant_genes = integer(),
  up_regulated = integer(),
  down_regulated = integer(),
  stringsAsFactors = FALSE
)

for(tissue in names(analyses)) {
  if(!is.null(analyses[[tissue]])) {
    n_total <- nrow(analyses[[tissue]]$results)
    n_sig <- nrow(analyses[[tissue]]$significant)
    n_up <- nrow(analyses[[tissue]]$up_regulated)
    n_down <- nrow(analyses[[tissue]]$down_regulated)
    
    cat(sprintf("   - %s: %d个差异基因 (↑%d, ↓%d)\n", 
                tissue, n_sig, n_up, n_down))
    
    summary_data <- rbind(summary_data, data.frame(
      tissue = tissue,
      total_genes = n_total,
      significant_genes = n_sig,
      up_regulated = n_up,
      down_regulated = n_down
    ))
  }
}

# 保存汇总结果
write.table(summary_data, "../10_Annotation/DESeq2_analysis_summary.txt", 
           sep = "\t", row.names = FALSE, quote = FALSE)

cat("\n=== DESeq2分析完成 ===\n")
cat("结果文件已保存到 ../10_Annotation/ 目录\n")
cat("接下来可以:\n")
cat("1. 创建火山图和热图\n")
cat("2. 进行GO/KEGG富集分析\n")
cat("3. 结合EggNOG注释分析基因功能\n")
