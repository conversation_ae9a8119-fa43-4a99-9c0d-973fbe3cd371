#!/bin/bash

# EggNOG-mapper annotation script for hagfish proteins
# This script will annotate the predicted proteins with functional information

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper ==="
echo "开始时间: $(date)"

# Set up directories
INPUT_PROTEINS="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results"
OUTPUT_PREFIX="hagfish_proteins"

# Check if input file exists
if [ ! -f "$INPUT_PROTEINS" ]; then
    echo "错误: 输入蛋白质文件不存在: $INPUT_PROTEINS"
    exit 1
fi

echo "输入文件: $INPUT_PROTEINS"
echo "输出目录: $OUTPUT_DIR"

# Count sequences
SEQ_COUNT=$(grep -c "^>" "$INPUT_PROTEINS")
echo "蛋白质序列数量: $SEQ_COUNT"

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo ""
echo "=== 步骤1: 下载EggNOG数据库 ==="
echo "注意: 首次运行需要下载数据库，可能需要较长时间..."

# Download database (only if not already present)
download_eggnog_data.py

echo ""
echo "=== 步骤2: 运行EggNOG-mapper注释 ==="

# Run EggNOG-mapper
emapper.py \
    -i "$INPUT_PROTEINS" \
    --output_dir "$OUTPUT_DIR" \
    --output "$OUTPUT_PREFIX" \
    --override \
    --resume \
    --cpu 4 \
    --data_dir ~/.eggnog_data \
    --temp_dir "$OUTPUT_DIR/temp" \
    --decorate_gff yes \
    --excel

echo ""
echo "=== 步骤3: 检查输出文件 ==="

# Check output files
if [ -f "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations" ]; then
    echo "✅ 注释文件生成成功: ${OUTPUT_PREFIX}.emapper.annotations"
    
    # Count annotated sequences
    ANNOTATED_COUNT=$(tail -n +5 "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations" | wc -l)
    echo "   注释序列数量: $ANNOTATED_COUNT"
    
    # Show some statistics
    echo ""
    echo "=== 注释统计 ==="
    echo "有GO注释的序列:"
    tail -n +5 "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations" | cut -f6 | grep -v "^-$" | wc -l
    
    echo "有KEGG注释的序列:"
    tail -n +5 "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations" | cut -f12 | grep -v "^-$" | wc -l
    
    echo "有COG注释的序列:"
    tail -n +5 "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations" | cut -f7 | grep -v "^-$" | wc -l
    
else
    echo "❌ 注释失败，请检查错误信息"
    exit 1
fi

echo ""
echo "完成时间: $(date)"
echo "=== EggNOG-mapper注释完成 ==="

# List all output files
echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"
