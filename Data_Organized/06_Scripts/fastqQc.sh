#!/bin/bash
# qc_multiple.sh
# 该脚本对目录中的所有 FASTQ 文件进行质量控制，并整合生成 MultiQC 报告

# 定义输出质控报告的目录
qcdir="/public/home/<USER>/2025hagfish/NGS/Data/fastq_report"
# 定义 FASTQ 文件所在目录
fqdir="/public/home/<USER>/2025hagfish/NGS/Data"

# 如果输出目录不存在，则创建它
if [ ! -d "$qcdir" ]; then
    mkdir -p "$qcdir"
fi

echo "======================================"
echo "开始对所有 FASTQ 文件进行质量控制..."
# 使用 FastQC 对所有以 Unknown 开头的 .fastq.gz 文件进行质控，使用 10 个线程
fastqc -t 10 -o "$qcdir" "$fqdir"/Unknown*.fq.gz

echo "======================================"
echo "整合 FastQC 报告生成 MultiQC 报告..."
# 在质控目录下运行 MultiQC，生成综合报告
multiqc "$qcdir" -o "$qcdir"

echo "======================================"
echo "质控流程完成，请查看报告目录: $qcdir"