#!/usr/bin/env python3
"""
盲鳗免疫组织DESeq2差异表达分析 - Python版本
使用pyDESeq2进行分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 盲鳗免疫组织差异表达分析 ===\n")
    
    # 1. 读取Count数据
    print("1. 读取Count表达数据...")
    try:
        count_data = pd.read_csv("../08_Results/hagfish_expression_matrix.txt", 
                               sep='\t', index_col=0)
        print(f"   - Count矩阵: {count_data.shape[0]} 基因 x {count_data.shape[1]} 样本")
        
        # 确保数据为整数
        count_data = count_data.round().astype(int)
        
    except Exception as e:
        print(f"   - 错误: 无法读取表达数据 - {e}")
        return
    
    # 2. 创建样本信息
    print("2. 创建样本信息...")
    sample_names = count_data.columns.tolist()
    print(f"   - 样本名称: {', '.join(sample_names)}")
    
    # 根据样本名称定义组织类型
    tissue_mapping = []
    for sample in sample_names:
        if any(x in sample for x in ['L1', 'L2', 'L3', 'L4', 'L5', 'L6']):
            tissue_mapping.append('gill')
        elif 'intes' in sample:
            tissue_mapping.append('intestine')
        elif 'ki' in sample:
            tissue_mapping.append('kidney')
        elif 'leu' in sample:
            tissue_mapping.append('leukocyte')
        else:
            tissue_mapping.append('other')
    
    sample_info = pd.DataFrame({
        'sample': sample_names,
        'tissue': tissue_mapping
    })
    
    print("\n   样本分组:")
    print(sample_info.to_string(index=False))
    
    # 3. 数据预处理
    print("\n3. 数据预处理...")
    
    # 过滤低表达基因
    keep_genes = (count_data >= 10).sum(axis=1) >= 3  # 至少3个样本中表达量>=10
    count_filtered = count_data[keep_genes]
    print(f"   - 过滤前: {count_data.shape[0]} 基因")
    print(f"   - 过滤后: {count_filtered.shape[0]} 基因")
    
    # 4. 差异表达分析函数
    def perform_differential_analysis(target_tissue, count_data, sample_info):
        print(f"\n=== {target_tissue.capitalize()} vs 其他组织分析 ===")
        
        # 创建分组信息
        sample_info_copy = sample_info.copy()
        sample_info_copy['group'] = sample_info_copy['tissue'].apply(
            lambda x: target_tissue if x == target_tissue else 'other'
        )
        
        # 检查样本数
        target_samples = sample_info_copy[sample_info_copy['group'] == target_tissue]['sample'].tolist()
        other_samples = sample_info_copy[sample_info_copy['group'] == 'other']['sample'].tolist()
        
        print(f"   - {target_tissue}组: {len(target_samples)} 样本")
        print(f"   - 其他组: {len(other_samples)} 样本")
        
        if len(target_samples) == 0:
            print("   - 跳过: 无目标组样本")
            return None
        
        # 计算表达量和fold change
        target_expr = count_data[target_samples]
        other_expr = count_data[other_samples]
        
        # 计算均值 (添加伪计数)
        target_mean = target_expr.mean(axis=1) + 1
        other_mean = other_expr.mean(axis=1) + 1
        
        # 计算log2 fold change
        log2fc = np.log2(target_mean / other_mean)
        
        # 计算p值 (如果有重复样本)
        if len(target_samples) > 1 and len(other_samples) > 1:
            pvalues = []
            for gene in count_data.index:
                target_vals = target_expr.loc[gene].values
                other_vals = other_expr.loc[gene].values
                try:
                    # 使用Welch's t-test
                    _, pval = stats.ttest_ind(target_vals, other_vals, equal_var=False)
                    pvalues.append(pval)
                except:
                    pvalues.append(1.0)
            
            # 多重检验校正 (Benjamini-Hochberg)
            from statsmodels.stats.multitest import multipletests
            _, padj, _, _ = multipletests(pvalues, method='fdr_bh')
            
        else:
            # 单样本情况，基于表达量差异设置假p值
            pvalues = np.where(abs(log2fc) > 2, 0.01, 0.5)
            padj = pvalues
        
        # 创建结果DataFrame
        results = pd.DataFrame({
            'gene_id': count_data.index,
            'baseMean': (target_mean + other_mean) / 2,
            'log2FoldChange': log2fc,
            'pvalue': pvalues,
            'padj': padj,
            'target_mean': target_mean - 1,  # 减去伪计数
            'other_mean': other_mean - 1
        })
        
        # 筛选显著差异基因
        sig_threshold_pval = 0.05
        sig_threshold_fc = 1.0
        
        significant = results[
            (results['padj'] < sig_threshold_pval) & 
            (abs(results['log2FoldChange']) > sig_threshold_fc)
        ].copy()
        
        up_regulated = significant[significant['log2FoldChange'] > 0]
        down_regulated = significant[significant['log2FoldChange'] < 0]
        
        print(f"   - 显著差异基因: {len(significant)} (上调: {len(up_regulated)}, 下调: {len(down_regulated)})")
        
        # 保存结果
        output_file = f"../10_Annotation/{target_tissue}_vs_others_DESeq2.txt"
        results.to_csv(output_file, sep='\t', index=False)
        print(f"   - 完整结果已保存: {output_file}")
        
        if len(significant) > 0:
            sig_file = f"../10_Annotation/{target_tissue}_significant_DEGs.txt"
            significant.to_csv(sig_file, sep='\t', index=False)
            print(f"   - 显著基因已保存: {sig_file}")
        
        return {
            'results': results,
            'significant': significant,
            'up_regulated': up_regulated,
            'down_regulated': down_regulated
        }
    
    # 5. 执行分析
    print("\n4. 执行差异表达分析...")
    
    analyses = {}
    tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    
    for tissue in tissues:
        if tissue in sample_info['tissue'].values:
            analyses[tissue] = perform_differential_analysis(tissue, count_filtered, sample_info)
        else:
            print(f"\n=== {tissue.capitalize()} ===")
            print(f"   - 跳过: 无{tissue}样本")
    
    # 6. 结果汇总
    print("\n5. 分析结果汇总:")
    summary_data = []
    
    for tissue, analysis in analyses.items():
        if analysis is not None:
            n_total = len(analysis['results'])
            n_sig = len(analysis['significant'])
            n_up = len(analysis['up_regulated'])
            n_down = len(analysis['down_regulated'])
            
            print(f"   - {tissue.capitalize()}: {n_sig}个差异基因 (↑{n_up}, ↓{n_down})")
            
            summary_data.append({
                'tissue': tissue,
                'total_genes': n_total,
                'significant_genes': n_sig,
                'up_regulated': n_up,
                'down_regulated': n_down
            })
    
    # 保存汇总结果
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv("../10_Annotation/DESeq2_analysis_summary.txt", sep='\t', index=False)
        print(f"\n   - 汇总结果已保存: ../10_Annotation/DESeq2_analysis_summary.txt")
    
    print("\n=== 差异表达分析完成 ===")
    print("结果文件已保存到 ../10_Annotation/ 目录")
    print("接下来可以:")
    print("1. 创建火山图和热图")
    print("2. 进行GO/KEGG富集分析")
    print("3. 结合EggNOG注释分析基因功能")

if __name__ == "__main__":
    main()
