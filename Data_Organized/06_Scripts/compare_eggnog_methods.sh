#!/bin/bash

# 比较不同EggNOG-mapper参数的注释效果
# 针对盲鳗（真核生物）优化参数

echo "=== EggNOG-mapper方法对比分析 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_comparison"
DATA_DIR="../10_Annotation/eggnog_data"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 当前Diamond方法结果统计 ==="

DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

if [ -f "$DIAMOND_FILE" ]; then
    echo "分析现有Diamond结果..."
    
    # Diamond统计
    DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
    DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_COG=$(tail -n +5 "$DIAMOND_FILE" | cut -f7 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_PFAM=$(tail -n +5 "$DIAMOND_FILE" | cut -f21 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "Diamond方法结果:"
    echo "  总注释序列: $DIAMOND_TOTAL"
    echo "  GO注释: $DIAMOND_GO ($(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l)%)"
    echo "  KEGG注释: $DIAMOND_KEGG ($(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l)%)"
    echo "  基因名: $DIAMOND_GENE ($(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l)%)"
    echo "  COG分类: $DIAMOND_COG ($(echo "scale=1; $DIAMOND_COG * 100 / $DIAMOND_TOTAL" | bc -l)%)"
    echo "  PFAM结构域: $DIAMOND_PFAM ($(echo "scale=1; $DIAMOND_PFAM * 100 / $DIAMOND_TOTAL" | bc -l)%)"
else
    echo "未找到Diamond结果文件"
    exit 1
fi

echo ""
echo "=== 检查EggNOG-mapper可用选项 ==="

# 检查版本和帮助
echo "EggNOG-mapper版本:"
emapper.py --version

echo ""
echo "=== 尝试优化的真核生物参数 ==="

# 方法1: 使用更严格的E-value阈值
echo "方法1: 严格E-value阈值 (1e-10)"
emapper.py \
    -i "$PROTEIN_FILE" \
    --output "hagfish_strict_eval" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --evalue 1e-10 \
    --score 60 \
    --cpu 4 \
    --override

# 检查结果
STRICT_FILE="$OUTPUT_DIR/hagfish_strict_eval.emapper.annotations"
if [ -f "$STRICT_FILE" ]; then
    echo "✅ 严格E-value方法完成"
    
    STRICT_TOTAL=$(tail -n +5 "$STRICT_FILE" | wc -l)
    STRICT_GO=$(tail -n +5 "$STRICT_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    STRICT_KEGG=$(tail -n +5 "$STRICT_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    STRICT_GENE=$(tail -n +5 "$STRICT_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "  总注释序列: $STRICT_TOTAL"
    echo "  GO注释: $STRICT_GO"
    echo "  KEGG注释: $STRICT_KEGG"
    echo "  基因名: $STRICT_GENE"
else
    echo "❌ 严格E-value方法失败"
fi

echo ""
echo "方法2: 宽松参数增加覆盖度"
emapper.py \
    -i "$PROTEIN_FILE" \
    --output "hagfish_relaxed" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --evalue 1e-3 \
    --score 40 \
    --cpu 4 \
    --override

# 检查结果
RELAXED_FILE="$OUTPUT_DIR/hagfish_relaxed.emapper.annotations"
if [ -f "$RELAXED_FILE" ]; then
    echo "✅ 宽松参数方法完成"
    
    RELAXED_TOTAL=$(tail -n +5 "$RELAXED_FILE" | wc -l)
    RELAXED_GO=$(tail -n +5 "$RELAXED_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    RELAXED_KEGG=$(tail -n +5 "$RELAXED_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    RELAXED_GENE=$(tail -n +5 "$RELAXED_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "  总注释序列: $RELAXED_TOTAL"
    echo "  GO注释: $RELAXED_GO"
    echo "  KEGG注释: $RELAXED_KEGG"
    echo "  基因名: $RELAXED_GENE"
else
    echo "❌ 宽松参数方法失败"
fi

echo ""
echo "=== 方法对比汇总 ==="

echo ""
echo "方法对比表:"
echo "方法          总序列    GO注释    KEGG注释  基因名"
echo "Diamond       $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE"

if [ -f "$STRICT_FILE" ]; then
    echo "严格E-value   $STRICT_TOTAL      $STRICT_GO      $STRICT_KEGG       $STRICT_GENE"
fi

if [ -f "$RELAXED_FILE" ]; then
    echo "宽松参数      $RELAXED_TOTAL      $RELAXED_GO      $RELAXED_KEGG       $RELAXED_GENE"
fi

echo ""
echo "=== 结论和建议 ==="

echo "分析完成时间: $(date)"

# 生成建议
echo ""
echo "基于盲鳗作为脊椎动物的特点:"
echo "1. 当前Diamond方法已经使用了真核生物数据库"
echo "2. 注释结果包含了完整的分类层级信息"
echo "3. 可以根据需要调整E-value和score阈值"

echo ""
echo "生成的对比文件:"
ls -la "$OUTPUT_DIR"
