#!/bin/bash

# 设置路径
GENOME_DIR="/public/home/<USER>/2025hagfish/NGS/Data/trim_galore"   # GSNAP 索引目录
GENOME_NAME="hagfish_genome"                                         # GSNAP 索引名称
FQ_DIR="/public/home/<USER>/2025hagfish/NGS/Data/trim_galore"        # FASTQ 文件目录
OUT_DIR="/public/home/<USER>/2025hagfish/NGS/Data/GSNAP"             # 输出目录
THREADS=8                                                          # 使用 8 线程

# 创建输出目录
mkdir -p "$OUT_DIR"

# 检查并列出文件
echo "检查是否存在 FASTQ 文件:"
ls "$FQ_DIR"/*_1_val_1.fq

# 如果路径中没有文件，停止脚本执行
if [ $? -ne 0 ]; then
    echo "没有找到相关的 FASTQ 文件，脚本停止执行！"
    exit 1
fi

# 生成样本 ID 列表（获取 _1_val_1.fq 文件名前缀）
ls "$FQ_DIR"/*_1_val_1.fq | sed 's/_1_val_1\.fq//' | xargs -n1 basename > sample.ID

# 逐个处理样本
while read SAMPLE_ID; do
    echo "正在处理样本: $SAMPLE_ID"

    # 运行 GSNAP 进行比对
    gsnap \
      -D "$GENOME_DIR" \
      -d "$GENOME_NAME" \
      --novelsplicing=1 \
      -N 1 \
      -B 5 \
      -t "$THREADS" \
      -A sam \
      "${FQ_DIR}/${SAMPLE_ID}_1_val_1.fq" \
      "${FQ_DIR}/${SAMPLE_ID}_2_val_2.fq" \
    > "${OUT_DIR}/${SAMPLE_ID}.sam"

    # 转换为 BAM 格式，并进行排序
    samtools view -@ 8 -Sb "${OUT_DIR}/${SAMPLE_ID}.sam" \
    | samtools sort -@ 8 -o "${OUT_DIR}/${SAMPLE_ID}.sorted.bam"

    # 创建 BAM 索引
    samtools index "${OUT_DIR}/${SAMPLE_ID}.sorted.bam"

    # 删除中间 SAM 文件释放空间
    rm "${OUT_DIR}/${SAMPLE_ID}.sam"

    echo "样本 $SAMPLE_ID 处理完成。"

done < sample.ID

echo "所有样本比对完成"
