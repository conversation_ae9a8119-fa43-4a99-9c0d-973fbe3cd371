#!/bin/bash
# HISAT2 批量比对脚本（用于 RNA-seq 注释 + 可变剪接）

# 设置路径
fqdir="/public/home/<USER>/2025hagfish/NGS/Data/trim_galore"
index="/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/hagfish_genome"
outdir="/public/home/<USER>/2025hagfish/NGS/Data/Hisat2"
mkdir -p "$outdir"

# 创建样本 ID 列表（从 _1_val_1.fq 中提取前缀）
ls "$fqdir"/*_1_val_1.fq | sed 's/_1_val_1\.fq//' | xargs -n1 basename > sample.ID

# 遍历样本
while read id; do
    echo "🧬 正在处理样本：$id"

    # 比对命令
    hisat2 -p 10 \
        --dta \
        --novel-splicesite-outfile "${outdir}/${id}.novel_splice.txt" \
        --summary-file "${outdir}/${id}.hisat2.summary" \
        -x "$index" \
        -1 "${fqdir}/${id}_1_val_1.fq" \
        -2 "${fqdir}/${id}_2_val_2.fq" \
        -S "${outdir}/${id}.sam" \
        2> "${outdir}/${id}.hisat2.log"

    # SAM 转 BAM 并排序
    samtools view -@ 4 -Sb "${outdir}/${id}.sam" | \
        samtools sort -@ 4 -o "${outdir}/${id}.sorted.bam"

    samtools index "${outdir}/${id}.sorted.bam"
    rm "${outdir}/${id}.sam"

    echo "样本 $id 处理完成！"
done < sample.ID

echo "所有样本比对完成！"
