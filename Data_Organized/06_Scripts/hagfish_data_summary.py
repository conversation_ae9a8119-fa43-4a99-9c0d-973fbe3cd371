#!/usr/bin/env python3
"""
Generate summary statistics for hagfish RNA-seq data
"""

import pandas as pd
import numpy as np

def generate_summary():
    print("=== 盲鳗RNA-seq数据统计报告 ===\n")
    
    # Read hagfish expression matrix
    df = pd.read_csv("08_Results/hagfish_expression_matrix.txt", sep='\t')
    
    print(f"1. 基本信息:")
    print(f"   - 基因数量: {df.shape[0]:,}")
    print(f"   - 样本数量: {df.shape[1]-1}")
    print(f"   - 数据矩阵大小: {df.shape[0]} × {df.shape[1]-1}")
    
    # Sample information
    samples = df.columns[1:].tolist()
    print(f"\n2. 样本列表:")
    
    # Group samples by tissue type
    tissue_groups = {
        'development': [s for s in samples if s.startswith('hg3L')],
        'nervous': [s for s in samples if s in ['hg3brain', 'hg3axon']],
        'digestive': [s for s in samples if s in ['hg3liver', 'hg3intes', 'hg3gall']],
        'circulatory': [s for s in samples if s in ['hg3heart']],
        'excretory': [s for s in samples if s in ['hg3ki']],
        'immune': [s for s in samples if s in ['hg3leu']],
        'muscular': [s for s in samples if s in ['hg3muscl']],
        'structural': [s for s in samples if s in ['hg3noto']],
        'reproductive': [s for s in samples if s in ['hg3sex', 'hgovary']],
        'integumentary': [s for s in samples if s in ['hg3skin1', 'hg3skin2']],
        'other': [s for s in samples if s in ['hg3solo']]
    }
    
    for group, group_samples in tissue_groups.items():
        if group_samples:
            print(f"   - {group.capitalize()}: {len(group_samples)}个样本 ({', '.join(group_samples)})")
    
    # Expression statistics
    expression_data = df.iloc[:, 1:].values
    
    print(f"\n3. 表达量统计:")
    print(f"   - 总读数: {expression_data.sum():,.0f}")
    print(f"   - 平均每个基因的读数: {expression_data.mean():.1f}")
    print(f"   - 表达量中位数: {np.median(expression_data):.1f}")
    print(f"   - 最高表达基因读数: {expression_data.max():,.0f}")
    
    # Genes with expression
    expressed_genes = (expression_data > 0).sum(axis=1)
    print(f"\n4. 基因表达情况:")
    print(f"   - 在所有样本中表达的基因: {(expressed_genes == len(samples)).sum():,}")
    print(f"   - 在至少一个样本中表达的基因: {(expressed_genes > 0).sum():,}")
    print(f"   - 在至少10个样本中表达的基因: {(expressed_genes >= 10).sum():,}")
    print(f"   - 完全不表达的基因: {(expressed_genes == 0).sum():,}")
    
    # Sample-wise statistics
    sample_totals = expression_data.sum(axis=0)
    print(f"\n5. 样本测序深度:")
    print(f"   - 平均每样本读数: {sample_totals.mean():,.0f}")
    print(f"   - 最高样本读数: {sample_totals.max():,.0f} ({samples[sample_totals.argmax()]})")
    print(f"   - 最低样本读数: {sample_totals.min():,.0f} ({samples[sample_totals.argmin()]})")
    
    print(f"\n6. 数据质量评估:")
    # Calculate coefficient of variation for each gene
    gene_means = expression_data.mean(axis=1)
    gene_stds = expression_data.std(axis=1)
    cv = gene_stds / (gene_means + 1)  # Add 1 to avoid division by zero
    
    print(f"   - 基因表达变异系数中位数: {np.median(cv):.2f}")
    print(f"   - 高变异基因数量 (CV > 1): {(cv > 1).sum():,}")
    print(f"   - 低变异基因数量 (CV < 0.5): {(cv < 0.5).sum():,}")
    
    print(f"\n=== 报告生成完成 ===")

if __name__ == "__main__":
    generate_summary()
