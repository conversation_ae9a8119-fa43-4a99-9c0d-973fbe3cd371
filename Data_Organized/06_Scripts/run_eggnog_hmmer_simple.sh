#!/bin/bash

# 简化版HMMER方法的EggNOG-mapper分析

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper HMMER方法 (简化版) ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_hmmer"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_hmmer"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 检查EggNOG-mapper版本和支持的方法 ==="
emapper.py --version

echo ""
echo "=== 尝试运行HMMER方法 ==="

# 先尝试最简单的HMMER命令
emapper.py \
    -m hmmer \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 2 \
    --override

echo "HMMER方法退出码: $?"

# 检查是否生成了结果文件
ANNOTATION_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$ANNOTATION_FILE" ]; then
    echo "✅ HMMER注释成功！"
    
    # 统计结果
    TOTAL_LINES=$(wc -l < "$ANNOTATION_FILE")
    HEADER_LINES=$(grep -c "^#" "$ANNOTATION_FILE")
    ANNOTATED_SEQS=$((TOTAL_LINES - HEADER_LINES))
    
    echo "HMMER注释序列数: $ANNOTATED_SEQS"
    
    # 与Diamond对比
    DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
    if [ -f "$DIAMOND_FILE" ]; then
        DIAMOND_SEQS=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
        echo "Diamond注释序列数: $DIAMOND_SEQS"
        echo "差异: $((ANNOTATED_SEQS - DIAMOND_SEQS))"
    fi
    
else
    echo "❌ HMMER注释失败"
    echo "尝试其他搜索方法..."
    
    # 尝试no_search + hmmer_search
    echo ""
    echo "=== 尝试替代方法: no_search + hmmer_search ==="
    
    emapper.py \
        -m no_search \
        --hmmer_search \
        -i "$PROTEIN_FILE" \
        --output "${OUTPUT_PREFIX}_alt" \
        --output_dir "$OUTPUT_DIR" \
        --data_dir "$DATA_DIR" \
        --cpu 2 \
        --override
    
    ALT_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}_alt.emapper.annotations"
    if [ -f "$ALT_FILE" ]; then
        echo "✅ 替代方法成功！"
        mv "$ALT_FILE" "$ANNOTATION_FILE"
    else
        echo "❌ 替代方法也失败"
    fi
fi

echo ""
echo "完成时间: $(date)"
echo "生成的文件:"
ls -la "$OUTPUT_DIR"
