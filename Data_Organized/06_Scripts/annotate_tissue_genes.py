#!/usr/bin/env python3
"""
为组织特异性基因添加功能注释
"""

import pandas as pd
import re

def main():
    print("=== 组织特异性基因功能注释 ===\n")
    
    # 1. 读取EggNOG注释
    print("1. 读取EggNOG注释...")
    
    try:
        # 读取EggNOG注释文件
        eggnog_file = "../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
        
        # 跳过注释行，读取数据
        eggnog_data = pd.read_csv(eggnog_file, sep='\t', comment='#', header=None, skiprows=4)
        
        # 设置列名
        eggnog_cols = ['query', 'seed_ortholog', 'evalue', 'score', 'eggNOG_OGs', 
                      'max_annot_lvl', 'COG_category', 'Description', 'Preferred_name', 
                      'GOs', 'EC', 'KEGG_ko', 'KEGG_Pathway', 'KEGG_Module', 
                      'KEGG_Reaction', 'KEGG_rclass', 'BRITE', 'KEGG_TC', 'CAZy', 
                      'BiGG_Reaction', 'PFAMs']
        
        # 只保留存在的列
        eggnog_data.columns = eggnog_cols[:len(eggnog_data.columns)]
        
        print(f"   - EggNOG注释: {len(eggnog_data)} 条记录")
        
        # 创建基因ID映射 (从蛋白质ID到转录本ID)
        # 蛋白质ID格式: MSTRG.X.Y.p1 -> 转录本ID: XLOC_XXXXXX
        gene_mapping = {}
        for _, row in eggnog_data.iterrows():
            protein_id = row['query']
            # 尝试提取MSTRG号码
            match = re.search(r'MSTRG\.(\d+)\.', protein_id)
            if match:
                mstrg_num = int(match.group(1))
                transcript_id = f"XLOC_{mstrg_num:06d}"
                gene_mapping[transcript_id] = protein_id
        
        print(f"   - 成功映射: {len(gene_mapping)} 个基因ID")
        
    except Exception as e:
        print(f"   - 错误: {e}")
        return
    
    # 2. 处理各组织的差异基因
    tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    
    for tissue in tissues:
        print(f"\n2. 处理{tissue}特异性基因...")
        
        # 读取差异基因
        sig_file = f"../10_Annotation/{tissue}_significant_genes.txt"
        try:
            sig_genes = pd.read_csv(sig_file, sep='\t')
            print(f"   - {tissue}特异性基因: {len(sig_genes)}")
            
            # 添加注释信息
            annotated_genes = []
            
            for _, gene_row in sig_genes.iterrows():
                gene_id = gene_row['gene_id']
                
                # 查找对应的蛋白质ID
                if gene_id in gene_mapping:
                    protein_id = gene_mapping[gene_id]
                    
                    # 查找注释信息
                    annotation = eggnog_data[eggnog_data['query'] == protein_id]
                    
                    if len(annotation) > 0:
                        ann = annotation.iloc[0]
                        gene_info = {
                            'gene_id': gene_id,
                            'protein_id': protein_id,
                            f'{tissue}_expr': gene_row[f'{tissue}_expr'] if f'{tissue}_expr' in gene_row else gene_row.iloc[1],
                            'other_mean': gene_row['other_mean'],
                            'fold_change': gene_row['fold_change'],
                            'gene_name': ann['Preferred_name'] if 'Preferred_name' in ann and pd.notna(ann['Preferred_name']) and ann['Preferred_name'] != '-' else 'Unknown',
                            'description': ann['Description'] if 'Description' in ann and pd.notna(ann['Description']) and ann['Description'] != '-' else 'No description',
                            'GO_terms': ann['GOs'] if 'GOs' in ann and pd.notna(ann['GOs']) and ann['GOs'] != '-' else 'None',
                            'KEGG_ko': ann['KEGG_ko'] if 'KEGG_ko' in ann and pd.notna(ann['KEGG_ko']) and ann['KEGG_ko'] != '-' else 'None',
                            'COG_category': ann['COG_category'] if 'COG_category' in ann and pd.notna(ann['COG_category']) and ann['COG_category'] != '-' else 'None'
                        }
                    else:
                        gene_info = {
                            'gene_id': gene_id,
                            'protein_id': protein_id,
                            f'{tissue}_expr': gene_row[f'{tissue}_expr'] if f'{tissue}_expr' in gene_row else gene_row.iloc[1],
                            'other_mean': gene_row['other_mean'],
                            'fold_change': gene_row['fold_change'],
                            'gene_name': 'Unknown',
                            'description': 'No annotation found',
                            'GO_terms': 'None',
                            'KEGG_ko': 'None',
                            'COG_category': 'None'
                        }
                else:
                    gene_info = {
                        'gene_id': gene_id,
                        'protein_id': 'Not found',
                        f'{tissue}_expr': gene_row[f'{tissue}_expr'] if f'{tissue}_expr' in gene_row else gene_row.iloc[1],
                        'other_mean': gene_row['other_mean'],
                        'fold_change': gene_row['fold_change'],
                        'gene_name': 'Unknown',
                        'description': 'No mapping found',
                        'GO_terms': 'None',
                        'KEGG_ko': 'None',
                        'COG_category': 'None'
                    }
                
                annotated_genes.append(gene_info)
            
            # 保存注释结果
            if annotated_genes:
                annotated_df = pd.DataFrame(annotated_genes)
                
                # 按fold change排序
                annotated_df = annotated_df.sort_values('fold_change', ascending=False)
                
                output_file = f"../10_Annotation/{tissue}_annotated_genes.txt"
                annotated_df.to_csv(output_file, sep='\t', index=False)
                print(f"   - 注释结果已保存: {output_file}")
                
                # 统计注释情况
                has_name = (annotated_df['gene_name'] != 'Unknown').sum()
                has_desc = (annotated_df['description'] != 'No description') & (annotated_df['description'] != 'No mapping found')
                has_go = (annotated_df['GO_terms'] != 'None').sum()
                has_kegg = (annotated_df['KEGG_ko'] != 'None').sum()
                
                print(f"   - 有基因名: {has_name}/{len(annotated_df)} ({has_name/len(annotated_df)*100:.1f}%)")
                print(f"   - 有功能描述: {has_desc.sum()}/{len(annotated_df)} ({has_desc.sum()/len(annotated_df)*100:.1f}%)")
                print(f"   - 有GO注释: {has_go}/{len(annotated_df)} ({has_go/len(annotated_df)*100:.1f}%)")
                print(f"   - 有KEGG注释: {has_kegg}/{len(annotated_df)} ({has_kegg/len(annotated_df)*100:.1f}%)")
                
                # 显示top基因
                print(f"   - Top 5 {tissue}特异性基因:")
                for i, (_, row) in enumerate(annotated_df.head(5).iterrows()):
                    print(f"     {i+1}. {row['gene_name']} ({row['gene_id']}): {row['fold_change']:.1f}倍")
                    print(f"        功能: {row['description'][:80]}...")
        
        except Exception as e:
            print(f"   - 错误: 无法处理{tissue}数据 - {e}")
    
    print("\n=== 注释完成 ===")
    print("已为所有组织特异性基因添加功能注释")

if __name__ == "__main__":
    main()
