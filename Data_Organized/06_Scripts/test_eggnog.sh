#!/bin/bash

# Test EggNOG-mapper with a small subset of proteins

echo "=== EggNOG-mapper 测试运行 ==="
echo "开始时间: $(date)"

# Set up directories
INPUT_PROTEINS="../10_Annotation/test_proteins.fa"
OUTPUT_DIR="../10_Annotation/test_results"
OUTPUT_PREFIX="test_proteins"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR/temp"

echo "输入文件: $INPUT_PROTEINS"
echo "输出目录: $OUTPUT_DIR"

# Count sequences
SEQ_COUNT=$(grep -c "^>" "$INPUT_PROTEINS")
echo "测试蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 运行EggNOG-mapper测试 ==="

# Run EggNOG-mapper on test data
emapper.py \
    -i "$INPUT_PROTEINS" \
    --output_dir "$OUTPUT_DIR" \
    --output "$OUTPUT_PREFIX" \
    --override \
    --cpu 2 \
    --temp_dir "$OUTPUT_DIR/temp"

echo ""
echo "=== 检查测试结果 ==="

if [ -f "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations" ]; then
    echo "✅ 测试成功！注释文件生成"
    echo "注释文件大小: $(wc -l < "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations") 行"
    
    echo ""
    echo "前几行注释结果:"
    head -n 10 "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"
    
    echo ""
    echo "✅ EggNOG-mapper工作正常，可以运行完整注释"
else
    echo "❌ 测试失败，请检查安装"
    exit 1
fi

echo ""
echo "完成时间: $(date)"
