#!/bin/bash

# 盲鳗组织特异性表达分析 - Bash版本
echo "=== 盲鳗组织特异性表达分析 ==="

# 输入文件
EXPR_FILE="../08_Results/hagfish_expression_matrix.txt"
OUTPUT_DIR="../10_Annotation"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo "1. 数据概览..."

# 检查数据文件
if [ ! -f "$EXPR_FILE" ]; then
    echo "错误: 表达矩阵文件不存在: $EXPR_FILE"
    exit 1
fi

# 获取基本信息
TOTAL_GENES=$(tail -n +2 "$EXPR_FILE" | wc -l)
TOTAL_SAMPLES=$(head -1 "$EXPR_FILE" | tr '\t' '\n' | tail -n +2 | wc -l)

echo "   - 总基因数: $TOTAL_GENES"
echo "   - 总样本数: $TOTAL_SAMPLES"

# 显示样本名称
echo "   - 样本名称:"
head -1 "$EXPR_FILE" | tr '\t' '\n' | tail -n +2 | nl

echo ""
echo "2. 样本分组..."

# 定义样本分组
# 鳃: hg3L1-L6 (列2-7)
# 肠: hg3intes (列8)  
# 肾: hg3ki (列18)
# 白细胞: hg3leu (列19)
# 其他: 剩余样本

echo "   - 鳃组 (L1-L6): 列2-7"
echo "   - 肠组 (intes): 列8"
echo "   - 肾组 (ki): 列18"
echo "   - 白细胞组 (leu): 列19"
echo "   - 其他组织: 剩余列"

echo ""
echo "3. 鳃 vs 其他组织分析..."

# 鳃 vs 其他组织
# 鳃: 列2-7, 其他: 列8-21 (除了列8,18,19)
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print "gene_id", "gill_mean", "other_mean", "log2_fold_change", "fold_change"} 
NR>1 {
    # 计算鳃的平均值 (列2-7)
    gill_sum = $2 + $3 + $4 + $5 + $6 + $7
    gill_mean = gill_sum / 6
    
    # 计算其他组织的平均值 (排除肠、肾、白细胞)
    other_sum = $9 + $10 + $11 + $12 + $13 + $14 + $15 + $16 + $17 + $20 + $21
    other_mean = other_sum / 11
    
    # 计算fold change
    if (other_mean > 0) {
        fold_change = (gill_mean + 1) / (other_mean + 1)
        log2_fc = log(fold_change) / log(2)
    } else {
        fold_change = "Inf"
        log2_fc = "Inf"
    }
    
    print $1, gill_mean, other_mean, log2_fc, fold_change
}' "$EXPR_FILE" > "$OUTPUT_DIR/gill_vs_others_results.txt"

echo "   - 结果已保存: $OUTPUT_DIR/gill_vs_others_results.txt"

# 筛选高表达差异基因 (鳃中表达>20 且 fold change > 4)
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print $0, "category"} 
NR>1 && $2 > 20 && $5 > 4 {print $0, "gill_specific"}' \
"$OUTPUT_DIR/gill_vs_others_results.txt" > "$OUTPUT_DIR/gill_significant_genes.txt"

GILL_SIG=$(tail -n +2 "$OUTPUT_DIR/gill_significant_genes.txt" | wc -l)
echo "   - 鳃特异性高表达基因: $GILL_SIG"

echo ""
echo "4. 肠 vs 其他组织分析..."

# 肠 vs 其他组织
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print "gene_id", "intestine_expr", "other_mean", "log2_fold_change", "fold_change"} 
NR>1 {
    # 肠的表达值 (列8)
    intestine_expr = $8
    
    # 其他组织的平均值 (排除肠)
    other_sum = $2 + $3 + $4 + $5 + $6 + $7 + $9 + $10 + $11 + $12 + $13 + $14 + $15 + $16 + $17 + $18 + $19 + $20 + $21
    other_mean = other_sum / 19
    
    # 计算fold change
    if (other_mean > 0) {
        fold_change = (intestine_expr + 1) / (other_mean + 1)
        log2_fc = log(fold_change) / log(2)
    } else {
        fold_change = "Inf"
        log2_fc = "Inf"
    }
    
    print $1, intestine_expr, other_mean, log2_fc, fold_change
}' "$EXPR_FILE" > "$OUTPUT_DIR/intestine_vs_others_results.txt"

echo "   - 结果已保存: $OUTPUT_DIR/intestine_vs_others_results.txt"

# 筛选肠特异性基因
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print $0, "category"} 
NR>1 && $2 > 50 && $5 > 4 {print $0, "intestine_specific"}' \
"$OUTPUT_DIR/intestine_vs_others_results.txt" > "$OUTPUT_DIR/intestine_significant_genes.txt"

INTESTINE_SIG=$(tail -n +2 "$OUTPUT_DIR/intestine_significant_genes.txt" | wc -l)
echo "   - 肠特异性高表达基因: $INTESTINE_SIG"

echo ""
echo "5. 肾 vs 其他组织分析..."

# 肾 vs 其他组织
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print "gene_id", "kidney_expr", "other_mean", "log2_fold_change", "fold_change"} 
NR>1 {
    # 肾的表达值 (列18)
    kidney_expr = $18
    
    # 其他组织的平均值 (排除肾)
    other_sum = $2 + $3 + $4 + $5 + $6 + $7 + $8 + $9 + $10 + $11 + $12 + $13 + $14 + $15 + $16 + $17 + $19 + $20 + $21
    other_mean = other_sum / 19
    
    # 计算fold change
    if (other_mean > 0) {
        fold_change = (kidney_expr + 1) / (other_mean + 1)
        log2_fc = log(fold_change) / log(2)
    } else {
        fold_change = "Inf"
        log2_fc = "Inf"
    }
    
    print $1, kidney_expr, other_mean, log2_fc, fold_change
}' "$EXPR_FILE" > "$OUTPUT_DIR/kidney_vs_others_results.txt"

echo "   - 结果已保存: $OUTPUT_DIR/kidney_vs_others_results.txt"

# 筛选肾特异性基因
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print $0, "category"} 
NR>1 && $2 > 50 && $5 > 4 {print $0, "kidney_specific"}' \
"$OUTPUT_DIR/kidney_vs_others_results.txt" > "$OUTPUT_DIR/kidney_significant_genes.txt"

KIDNEY_SIG=$(tail -n +2 "$OUTPUT_DIR/kidney_significant_genes.txt" | wc -l)
echo "   - 肾特异性高表达基因: $KIDNEY_SIG"

echo ""
echo "6. 白细胞 vs 其他组织分析..."

# 白细胞 vs 其他组织
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print "gene_id", "leukocyte_expr", "other_mean", "log2_fold_change", "fold_change"} 
NR>1 {
    # 白细胞的表达值 (列19)
    leukocyte_expr = $19
    
    # 其他组织的平均值 (排除白细胞)
    other_sum = $2 + $3 + $4 + $5 + $6 + $7 + $8 + $9 + $10 + $11 + $12 + $13 + $14 + $15 + $16 + $17 + $18 + $20 + $21
    other_mean = other_sum / 19
    
    # 计算fold change
    if (other_mean > 0) {
        fold_change = (leukocyte_expr + 1) / (other_mean + 1)
        log2_fc = log(fold_change) / log(2)
    } else {
        fold_change = "Inf"
        log2_fc = "Inf"
    }
    
    print $1, leukocyte_expr, other_mean, log2_fc, fold_change
}' "$EXPR_FILE" > "$OUTPUT_DIR/leukocyte_vs_others_results.txt"

echo "   - 结果已保存: $OUTPUT_DIR/leukocyte_vs_others_results.txt"

# 筛选白细胞特异性基因
awk 'BEGIN{FS=OFS="\t"} 
NR==1 {print $0, "category"} 
NR>1 && $2 > 50 && $5 > 4 {print $0, "leukocyte_specific"}' \
"$OUTPUT_DIR/leukocyte_vs_others_results.txt" > "$OUTPUT_DIR/leukocyte_significant_genes.txt"

LEUKOCYTE_SIG=$(tail -n +2 "$OUTPUT_DIR/leukocyte_significant_genes.txt" | wc -l)
echo "   - 白细胞特异性高表达基因: $LEUKOCYTE_SIG"

echo ""
echo "7. 结果汇总..."

# 创建汇总文件
echo -e "tissue\ttotal_genes\tsignificant_genes" > "$OUTPUT_DIR/tissue_analysis_summary.txt"
echo -e "gill\t$TOTAL_GENES\t$GILL_SIG" >> "$OUTPUT_DIR/tissue_analysis_summary.txt"
echo -e "intestine\t$TOTAL_GENES\t$INTESTINE_SIG" >> "$OUTPUT_DIR/tissue_analysis_summary.txt"
echo -e "kidney\t$TOTAL_GENES\t$KIDNEY_SIG" >> "$OUTPUT_DIR/tissue_analysis_summary.txt"
echo -e "leukocyte\t$TOTAL_GENES\t$LEUKOCYTE_SIG" >> "$OUTPUT_DIR/tissue_analysis_summary.txt"

echo "分析结果汇总:"
echo "   - 鳃特异性基因: $GILL_SIG"
echo "   - 肠特异性基因: $INTESTINE_SIG"
echo "   - 肾特异性基因: $KIDNEY_SIG"
echo "   - 白细胞特异性基因: $LEUKOCYTE_SIG"

echo ""
echo "=== 分析完成 ==="
echo "所有结果文件已保存到: $OUTPUT_DIR"
echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"/*.txt | grep -E "(gill|intestine|kidney|leukocyte|summary)"
