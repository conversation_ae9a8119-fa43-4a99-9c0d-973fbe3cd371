#!/usr/bin/env python3
"""
盲鳗组织特异性表达分析 - Python版本
重点关注免疫相关组织：鳃(L1-L6)、肠(intes)、肾(ki)、白细胞(leu)
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 盲鳗组织特异性表达分析 ===\n")
    
    # 1. 读取数据
    print("1. 读取表达数据...")
    
    # 读取表达矩阵 (Count数据)
    expr_data = pd.read_csv("../08_Results/hagfish_expression_matrix.txt", 
                           sep='\t', index_col=0)
    
    print(f"   - 表达矩阵: {expr_data.shape[0]} 基因 x {expr_data.shape[1]} 样本")
    
    # 样本信息
    samples = expr_data.columns.tolist()
    sample_info = pd.DataFrame({
        'sample': samples,
        'tissue': ['gill', 'gill', 'gill', 'gill', 'gill', 'gill',        # hg3L1-L6 (鳃)
                  'reproductive', 'intestine', 'structural', 'nervous',    # hg3sex, hg3intes, hg3noto, hg3axon
                  'circulatory', 'muscular', 'digestive', 'digestive',     # hg3heart, hg3muscl, hg3gall, hg3liver
                  'nervous', 'unknown', 'integumentary', 'kidney',         # hg3brain, hg3solo, hg3skin1, hg3ki
                  'leukocyte', 'integumentary', 'reproductive'],           # hg3leu, hg3skin2, hg3ovary
        'group': ['gill'] * 6 + ['other', 'intestine', 'other', 'other',
                 'other', 'other', 'other', 'other', 'other', 'other', 
                 'other', 'kidney', 'leukocyte', 'other', 'other']
    })
    
    print("\n2. 样本信息:")
    print(sample_info.to_string(index=False))
    
    # 3. 数据预处理
    print("\n3. 数据预处理...")
    
    # 过滤低表达基因
    keep_genes = (expr_data >= 10).sum(axis=1) >= 3  # 至少3个样本中表达量>=10
    expr_filtered = expr_data[keep_genes]
    print(f"   - 过滤后保留: {expr_filtered.shape[0]} 基因")
    
    # 4. 组织特异性分析函数
    def perform_tissue_analysis(target_group, group_name, expr_data, sample_info):
        print(f"\n=== {group_name} vs 其他组织分析 ===")
        
        # 创建分组信息
        target_samples = sample_info[sample_info['group'] == target_group]['sample'].tolist()
        other_samples = sample_info[sample_info['group'] != target_group]['sample'].tolist()
        
        print(f"   - {group_name}组: {len(target_samples)} 样本")
        print(f"   - 其他组: {len(other_samples)} 样本")
        
        if len(target_samples) == 0:
            print("   - 错误: 目标组无样本")
            return None
        
        # 计算表达量
        target_expr = expr_data[target_samples]
        other_expr = expr_data[other_samples]
        
        # 计算均值
        target_mean = target_expr.mean(axis=1)
        other_mean = other_expr.mean(axis=1)
        
        # 计算fold change
        log2fc = np.log2((target_mean + 1) / (other_mean + 1))
        
        # 统计检验
        if len(target_samples) > 1 and len(other_samples) > 1:
            # 使用t检验
            pvalues = []
            for gene in expr_data.index:
                target_vals = target_expr.loc[gene].values
                other_vals = other_expr.loc[gene].values
                try:
                    _, pval = stats.ttest_ind(target_vals, other_vals)
                    pvalues.append(pval)
                except:
                    pvalues.append(1.0)
            
            # 多重检验校正 (Benjamini-Hochberg)
            from statsmodels.stats.multitest import multipletests
            _, padj, _, _ = multipletests(pvalues, method='fdr_bh')
            
        else:
            # 单样本情况，无法进行统计检验
            pvalues = [1.0] * len(expr_data)
            padj = [1.0] * len(expr_data)
        
        # 创建结果DataFrame
        results = pd.DataFrame({
            'gene_id': expr_data.index,
            'target_mean': target_mean,
            'other_mean': other_mean,
            'log2FoldChange': log2fc,
            'pvalue': pvalues,
            'padj': padj
        })
        
        # 筛选差异基因
        if len(target_samples) > 1:
            # 多样本：使用统计显著性
            sig_genes = results[(results['padj'] < 0.05) & (abs(results['log2FoldChange']) > 1)]
        else:
            # 单样本：使用表达量和fold change阈值
            high_expr = results['target_mean'] > 50
            high_fc = abs(results['log2FoldChange']) > 2
            sig_genes = results[high_expr & high_fc]
        
        up_genes = sig_genes[sig_genes['log2FoldChange'] > 0]
        down_genes = sig_genes[sig_genes['log2FoldChange'] < 0]
        
        print(f"   - 显著差异基因: {len(sig_genes)} (上调: {len(up_genes)}, 下调: {len(down_genes)})")
        
        # 保存结果
        output_file = f"../10_Annotation/{group_name.lower()}_vs_others_results.txt"
        results.to_csv(output_file, sep='\t', index=False)
        print(f"   - 结果已保存: {output_file}")
        
        # 保存显著差异基因
        if len(sig_genes) > 0:
            sig_file = f"../10_Annotation/{group_name.lower()}_significant_genes.txt"
            sig_genes.to_csv(sig_file, sep='\t', index=False)
            print(f"   - 显著基因已保存: {sig_file}")
        
        return {
            'results': results,
            'significant': sig_genes,
            'up_regulated': up_genes,
            'down_regulated': down_genes
        }
    
    # 5. 执行分析
    print("\n4. 执行组织特异性分析...")
    
    analyses = {}
    
    # 鳃 (L1-L6合并)
    analyses['gill'] = perform_tissue_analysis("gill", "Gill", expr_filtered, sample_info)
    
    # 肠道
    analyses['intestine'] = perform_tissue_analysis("intestine", "Intestine", expr_filtered, sample_info)
    
    # 肾脏
    analyses['kidney'] = perform_tissue_analysis("kidney", "Kidney", expr_filtered, sample_info)
    
    # 白细胞
    analyses['leukocyte'] = perform_tissue_analysis("leukocyte", "Leukocyte", expr_filtered, sample_info)
    
    # 6. 结果汇总
    print("\n5. 分析结果汇总:")
    summary_data = []
    
    for tissue, analysis in analyses.items():
        if analysis is not None:
            n_sig = len(analysis['significant'])
            n_up = len(analysis['up_regulated'])
            n_down = len(analysis['down_regulated'])
            print(f"   - {tissue.capitalize()}: {n_sig}个差异基因 (↑{n_up}, ↓{n_down})")
            
            summary_data.append({
                'tissue': tissue,
                'total_significant': n_sig,
                'up_regulated': n_up,
                'down_regulated': n_down
            })
    
    # 保存汇总结果
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv("../10_Annotation/tissue_analysis_summary.txt", sep='\t', index=False)
    
    print("\n=== 分析完成 ===")
    print("结果文件已保存到 ../10_Annotation/ 目录")
    print("\n后续可进行:")
    print("1. 结合EggNOG注释进行功能分析")
    print("2. GO/KEGG富集分析")
    print("3. 免疫相关基因筛选")
    print("4. 组织间共同/特异基因分析")

if __name__ == "__main__":
    main()
