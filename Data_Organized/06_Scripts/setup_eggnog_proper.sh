#!/bin/bash

# 按照标准教程设置EggNOG-mapper
# 参考: https://blog.csdn.net/woodcorpse/article/details/83144768

echo "=== EggNOG-mapper 标准设置流程 ==="
echo "开始时间: $(date)"

# 创建数据目录
mkdir -p ../10_Annotation/eggnog_data
mkdir -p ../10_Annotation/eggnog_results

echo ""
echo "=== 步骤1: 下载EggNOG数据库 ==="
echo "注意: 这将下载约20GB的数据，请确保有足够空间和网络"

# 下载数据库到指定目录
download_eggnog_data.py --data_dir ../10_Annotation/eggnog_data -y

echo ""
echo "=== 步骤2: 验证安装 ==="

# 检查数据库文件
if [ -f "../10_Annotation/eggnog_data/eggnog.db" ]; then
    echo "✅ 主数据库下载成功"
else
    echo "❌ 主数据库下载失败"
    exit 1
fi

echo ""
echo "=== 步骤3: 测试小样本 ==="

# 创建测试样本
head -n 20 ../08_Results/predicted_proteins.fa > ../10_Annotation/test_sample.fa

echo "测试样本序列数: $(grep -c '^>' ../10_Annotation/test_sample.fa)"

# 运行测试
emapper.py \
    -i ../10_Annotation/test_sample.fa \
    --output test_run \
    --output_dir ../10_Annotation/eggnog_results \
    --data_dir ../10_Annotation/eggnog_data \
    --cpu 2 \
    --override

echo ""
echo "=== 检查测试结果 ==="

if [ -f "../10_Annotation/eggnog_results/test_run.emapper.annotations" ]; then
    echo "✅ 测试成功！"
    echo "注释结果行数: $(wc -l < ../10_Annotation/eggnog_results/test_run.emapper.annotations)"
    
    echo ""
    echo "前5行结果预览:"
    head -n 5 ../10_Annotation/eggnog_results/test_run.emapper.annotations
    
    echo ""
    echo "✅ EggNOG-mapper设置完成，可以运行完整注释"
else
    echo "❌ 测试失败"
    exit 1
fi

echo ""
echo "完成时间: $(date)"
