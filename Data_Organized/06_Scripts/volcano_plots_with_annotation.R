#!/usr/bin/env Rscript

# 带EggNOG基因名注释的火山图绘制
# 整合EggNOG-mapper结果，优先使用基因名，无注释时使用XLOC号

library(ggplot2)
library(dplyr)
library(ggrepel)

cat("=== 创建带基因名注释的火山图 ===\n\n")

# 1. 读取EggNOG注释数据
cat("1. 读取EggNOG注释数据...\n")

# 读取EggNOG注释文件
eggnog_file <- "../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

# 跳过注释行，读取数据
eggnog_data <- read.table(eggnog_file, sep='\t', comment.char='#', 
                         header=FALSE, skip=4, fill=TRUE, quote="")

# 设置列名
eggnog_cols <- c('query', 'seed_ortholog', 'evalue', 'score', 'eggNOG_OGs', 
                'max_annot_lvl', 'COG_category', 'Description', 'Preferred_name', 
                'GOs', 'EC', 'KEGG_ko', 'KEGG_Pathway', 'KEGG_Module', 
                'KEGG_Reaction', 'KEGG_rclass', 'BRITE', 'KEGG_TC', 'CAZy', 
                'BiGG_Reaction', 'PFAMs')

# 只保留存在的列
colnames(eggnog_data) <- eggnog_cols[1:ncol(eggnog_data)]

cat(sprintf("   - EggNOG注释: %d 条记录\n", nrow(eggnog_data)))

# 2. 创建基因ID映射
cat("2. 创建基因ID映射...\n")

# 从蛋白质ID (MSTRG.X.Y.p1) 映射到转录本ID (XLOC_XXXXXX)
gene_mapping <- data.frame(
  xloc_id = character(),
  protein_id = character(),
  gene_name = character(),
  description = character(),
  stringsAsFactors = FALSE
)

for(i in 1:nrow(eggnog_data)) {
  protein_id <- as.character(eggnog_data$query[i])
  
  # 提取MSTRG号码
  if(grepl("MSTRG\\.(\\d+)\\.", protein_id)) {
    mstrg_num <- as.numeric(gsub(".*MSTRG\\.(\\d+)\\..*", "\\1", protein_id))
    xloc_id <- sprintf("XLOC_%06d", mstrg_num)
    
    # 获取基因名和描述
    gene_name <- as.character(eggnog_data$Preferred_name[i])
    description <- as.character(eggnog_data$Description[i])
    
    # 清理基因名
    if(is.na(gene_name) || gene_name == "" || gene_name == "-") {
      gene_name <- xloc_id  # 无注释时使用XLOC号
    }
    
    # 清理描述
    if(is.na(description) || description == "" || description == "-") {
      description <- "No description"
    }
    
    gene_mapping <- rbind(gene_mapping, data.frame(
      xloc_id = xloc_id,
      protein_id = protein_id,
      gene_name = gene_name,
      description = description,
      stringsAsFactors = FALSE
    ))
  }
}

cat(sprintf("   - 成功映射: %d 个基因ID\n", nrow(gene_mapping)))

# 3. 获取所有差异表达结果文件
de_files <- list.files("../10_Annotation/deseq2_results", 
                      pattern = "DE_.*_vs_all\\.csv", full.names = TRUE)

cat(sprintf("3. 找到 %d 个差异表达结果文件\n", length(de_files)))

# 创建火山图目录
if (!dir.exists("../10_Annotation/volcano_plots_annotated")) {
  dir.create("../10_Annotation/volcano_plots_annotated")
}

# 4. 对每个差异表达结果文件绘制带注释的火山图
for (de_file in de_files) {
  # 提取组织名称
  tissue <- gsub(".*DE_hg3(.*)_vs_all\\.csv", "\\1", de_file)
  
  cat(sprintf("\n创建 %s 带注释火山图...\n", tissue))
  
  # 读取差异表达结果
  de_results <- read.csv(de_file) %>%
    filter(!is.na(padj)) %>%
    filter(!is.na(pvalue)) %>%
    mutate(
      # 处理pvalue=0的情况
      min_pvalue = min(pvalue[pvalue > 0], na.rm = TRUE),
      pvalue = ifelse(pvalue == 0, min_pvalue, pvalue),
      # 添加显著性标记
      significant = ifelse(abs(log2FoldChange) > 1 & padj < 0.05, 
                          "Significant", "Not significant")
    ) %>%
    select(-min_pvalue)
  
  # 添加基因名注释
  de_results <- de_results %>%
    left_join(gene_mapping, by = c("gene_id" = "xloc_id")) %>%
    mutate(
      # 如果没有找到注释，使用原始gene_id
      display_name = ifelse(is.na(gene_name), gene_id, gene_name),
      # 如果基因名就是XLOC号，也使用原始gene_id
      display_name = ifelse(display_name == gene_id, gene_id, display_name)
    )
  
  # 统计注释情况
  annotated_count <- sum(!is.na(de_results$gene_name) & de_results$gene_name != de_results$gene_id)
  cat(sprintf("   - 成功注释基因: %d/%d (%.1f%%)\n", 
              annotated_count, nrow(de_results), annotated_count/nrow(de_results)*100))
  
  # 统计显著基因数
  n_sig <- sum(de_results$significant == "Significant")
  n_up <- sum(de_results$log2FoldChange > 1 & de_results$padj < 0.05)
  n_down <- sum(de_results$log2FoldChange < -1 & de_results$padj < 0.05)
  
  cat(sprintf("   - 显著基因: %d (上调: %d, 下调: %d)\n", n_sig, n_up, n_down))
  
  # 获取上下调top3基因 (减少标签数量，优先选择有基因名注释的)
  top_up <- de_results %>%
    filter(log2FoldChange > 1 & padj < 0.05) %>%
    # 优先选择有基因名注释的基因
    arrange(desc(ifelse(display_name != gene_id, 1, 0)), desc(log2FoldChange), pvalue) %>%
    head(3)

  top_down <- de_results %>%
    filter(log2FoldChange < -1 & padj < 0.05) %>%
    # 优先选择有基因名注释的基因
    arrange(desc(ifelse(display_name != gene_id, 1, 0)), log2FoldChange, pvalue) %>%
    head(3)

  # 合并要标记的基因
  genes_to_label <- bind_rows(top_up, top_down)
  
  # 绘制火山图
  p <- ggplot(de_results, aes(x = log2FoldChange, y = -log10(pvalue), 
                            color = -log10(pvalue), size = abs(log2FoldChange))) +
    geom_point(alpha = 0.7) +
    # 标记top基因 (使用基因名，优化避免重叠)
    geom_text_repel(
      data = genes_to_label,
      aes(label = display_name, color = NULL, size = NULL),
      color = "black",
      size = 3.5,
      fontface = "bold",
      box.padding = 1.0,
      point.padding = 0.8,
      max.overlaps = 20,
      segment.color = "grey30",
      segment.size = 0.5,
      min.segment.length = 0.3,
      force = 2,
      force_pull = 0.1,
      nudge_x = 0.1,
      nudge_y = 0.1,
      direction = "both",
      seed = 123
    ) +
    # 颜色渐变设置
    scale_color_gradientn(
      colours = c("blue", "green", "yellow", "red"),
      name = "-log10(p-value)",
      guide = guide_colorbar(barwidth = 1, barheight = 15)
    ) +
    # 点大小设置
    scale_size_continuous(
      range = c(1, 3),
      name = "|log2FC|",
      guide = guide_legend(override.aes = list(color = "grey50"))
    ) +
    labs(
      title = paste("Volcano Plot:", tissue, "vs All"),
      subtitle = paste("Up:", n_up, "Down:", n_down, "Total DEGs:", n_sig, 
                      "| Annotated genes:", annotated_count),
      x = "log2 Fold Change",
      y = "-log10(p-value)",
      caption = "Significance threshold: |log2FC| > 1, padj < 0.05\nGene names from EggNOG-mapper annotation"
    ) +
    theme_minimal(base_size = 12) +
    theme(
      legend.position = "right",
      plot.title = element_text(face = "bold", hjust = 0.5),
      plot.subtitle = element_text(hjust = 0.5),
      panel.grid.major = element_line(color = "grey90"),
      panel.grid.minor = element_blank(),
      axis.line = element_line(color = "black")
    ) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey50") +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey50")
  
  # 设置坐标轴范围
  if(nrow(de_results) > 0) {
    max_fc <- max(abs(de_results$log2FoldChange), na.rm = TRUE)
    p <- p + coord_cartesian(xlim = c(-max_fc, max_fc))
  }
  
  # 只保存PDF格式火山图
  ggsave(
    paste0("../10_Annotation/volcano_plots_annotated/volcano_", tissue, "_annotated.pdf"),
    plot = p,
    width = 14,
    height = 10,
    device = "pdf",
    bg = "white"
  )

  cat(sprintf("   - 带注释火山图已保存: volcano_%s_annotated.pdf\n", tissue))
  
  # 保存带注释的显著基因列表
  if(nrow(genes_to_label) > 0) {
    annotated_genes_file <- paste0("../10_Annotation/volcano_plots_annotated/top_genes_", tissue, "_annotated.csv")
    write.csv(genes_to_label, annotated_genes_file, row.names = FALSE)
    cat(sprintf("   - Top基因列表已保存: top_genes_%s_annotated.csv\n", tissue))
  }
}

cat("\n=== 带基因名注释的火山图创建完成 ===\n")
cat("所有火山图已保存到 ../10_Annotation/volcano_plots_annotated/ 目录 (仅PDF格式)\n")
cat("优化特点:\n")
cat("- 基因标注使用EggNOG-mapper的基因名，无注释时使用XLOC号\n")
cat("- 减少标签数量 (top3上调+top3下调) 避免重叠\n")
cat("- 优先标注有基因名注释的基因\n")
cat("- 优化标签布局和字体\n")
