#!/usr/bin/env Rscript

# 盲鳗组织特异性表达分析
# 重点关注免疫相关组织：鳃(L1-L6)、肠(intes)、肾(ki)、白细胞(leu)

library(DESeq2)
library(dplyr)
library(ggplot2)
library(pheatmap)
library(clusterProfiler)

cat("=== 盲鳗组织特异性表达分析 ===\n")

# 1. 读取数据
cat("1. 读取表达数据和注释信息...\n")

# 读取表达矩阵
expr_data <- read.table("../08_Results/hagfish_expression_matrix.txt", 
                       header = TRUE, row.names = 1, sep = "\t")

# 读取EggNOG注释
eggnog_data <- read.table("../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations",
                         header = TRUE, sep = "\t", comment.char = "#", quote = "")

# 样本信息
sample_info <- data.frame(
  sample = colnames(expr_data),
  tissue = c("larva", "larva", "larva", "larva", "larva", "larva",  # L1-L6 (鳃)
            "reproductive", "digestive", "structural", "nervous",     # sex, intes, noto, axon
            "circulatory", "muscular", "digestive", "digestive",     # heart, muscl, gall, liver
            "nervous", "unknown", "integumentary", "excretory",      # brain, solo, skin1, ki
            "immune", "integumentary", "reproductive"),              # leu, skin2, ovary
  group = c(rep("gill", 6),                                         # L1-L6归为鳃组
           "other", "intestine", "other", "other",                   # 其他组织
           "other", "other", "other", "other", 
           "other", "other", "other", "kidney",                      # 肾脏
           "leukocyte", "other", "other"),                          # 白细胞
  stringsAsFactors = FALSE
)

cat(sprintf("   - 表达矩阵: %d 基因 x %d 样本\n", nrow(expr_data), ncol(expr_data)))
cat(sprintf("   - 注释信息: %d 基因\n", nrow(eggnog_data)))

# 2. 数据预处理
cat("2. 数据预处理...\n")

# 过滤低表达基因
keep_genes <- rowSums(expr_data >= 10) >= 3  # 至少3个样本中表达量>=10
expr_filtered <- expr_data[keep_genes, ]
cat(sprintf("   - 过滤后保留: %d 基因\n", nrow(expr_filtered)))

# 3. 组织特异性分析函数
perform_tissue_analysis <- function(target_group, group_name, expr_data, sample_info) {
  cat(sprintf("\n=== %s vs 其他组织分析 ===\n", group_name))
  
  # 创建分组信息
  sample_info$comparison <- ifelse(sample_info$group == target_group, 
                                  group_name, "Other")
  
  # 检查样本数
  target_n <- sum(sample_info$comparison == group_name)
  other_n <- sum(sample_info$comparison == "Other")
  cat(sprintf("   - %s组: %d 样本\n", group_name, target_n))
  cat(sprintf("   - 其他组: %d 样本\n", other_n))
  
  if(target_n < 2) {
    cat("   - 警告: 目标组样本数少于2，跳过分析\n")
    return(NULL)
  }
  
  # DESeq2分析
  dds <- DESeqDataSetFromMatrix(countData = round(expr_data),
                               colData = sample_info,
                               design = ~ comparison)
  
  # 设置参考水平
  dds$comparison <- relevel(dds$comparison, ref = "Other")
  
  # 运行DESeq2
  dds <- DESeq(dds)
  res <- results(dds, contrast = c("comparison", group_name, "Other"))
  
  # 整理结果
  res_df <- as.data.frame(res)
  res_df$gene_id <- rownames(res_df)
  res_df <- res_df[!is.na(res_df$padj), ]
  
  # 添加注释信息
  if(nrow(eggnog_data) > 0) {
    # 匹配基因ID (可能需要调整匹配方式)
    colnames(eggnog_data)[1] <- "gene_id"  # 确保列名一致
    res_df <- merge(res_df, eggnog_data[, c("gene_id", "Description", "Preferred_name", 
                                           "GOs", "KEGG_ko", "COG_category")], 
                   by = "gene_id", all.x = TRUE)
  }
  
  # 筛选显著差异基因
  sig_genes <- res_df[res_df$padj < 0.05 & abs(res_df$log2FoldChange) > 1, ]
  up_genes <- sig_genes[sig_genes$log2FoldChange > 0, ]
  down_genes <- sig_genes[sig_genes$log2FoldChange < 0, ]
  
  cat(sprintf("   - 显著差异基因: %d (上调: %d, 下调: %d)\n", 
              nrow(sig_genes), nrow(up_genes), nrow(down_genes)))
  
  # 保存结果
  output_file <- sprintf("../10_Annotation/%s_vs_others_DEG.txt", tolower(group_name))
  write.table(res_df, output_file, sep = "\t", row.names = FALSE, quote = FALSE)
  cat(sprintf("   - 结果已保存: %s\n", output_file))
  
  return(list(
    results = res_df,
    significant = sig_genes,
    up_regulated = up_genes,
    down_regulated = down_genes,
    dds = dds
  ))
}

# 4. 执行分析
cat("\n4. 执行组织特异性分析...\n")

# 分析目标组织
analyses <- list()

# 鳃 (L1-L6合并)
analyses$gill <- perform_tissue_analysis("gill", "Gill", expr_filtered, sample_info)

# 肠道
analyses$intestine <- perform_tissue_analysis("intestine", "Intestine", expr_filtered, sample_info)

# 肾脏
analyses$kidney <- perform_tissue_analysis("kidney", "Kidney", expr_filtered, sample_info)

# 白细胞
analyses$leukocyte <- perform_tissue_analysis("leukocyte", "Leukocyte", expr_filtered, sample_info)

# 5. 结果汇总
cat("\n5. 分析结果汇总:\n")
for(tissue in names(analyses)) {
  if(!is.null(analyses[[tissue]])) {
    n_sig <- nrow(analyses[[tissue]]$significant)
    n_up <- nrow(analyses[[tissue]]$up_regulated)
    n_down <- nrow(analyses[[tissue]]$down_regulated)
    cat(sprintf("   - %s: %d个差异基因 (↑%d, ↓%d)\n", 
                tissue, n_sig, n_up, n_down))
  }
}

cat("\n=== 分析完成 ===\n")
cat("后续可进行:\n")
cat("1. GO/KEGG富集分析\n")
cat("2. 免疫相关基因筛选\n")
cat("3. 组织间共同/特异基因分析\n")
cat("4. 热图可视化\n")
