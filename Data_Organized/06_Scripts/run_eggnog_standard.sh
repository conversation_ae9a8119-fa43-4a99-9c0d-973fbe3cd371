#!/bin/bash

# 按照标准教程运行EggNOG-mapper
# 参考: https://blog.csdn.net/woodcorpse/article/details/83144768

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper 标准流程 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins"

# 检查输入文件
if [ ! -f "$PROTEIN_FILE" ]; then
    echo "错误: 蛋白质文件不存在: $PROTEIN_FILE"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 步骤1: 运行EggNOG-mapper (Diamond方法) ==="
echo "注意: 使用Diamond方法，适合大量序列"

# 运行EggNOG-mapper
# 参数说明:
# -m diamond: 使用diamond方法，适合大量序列
# -i: 输入蛋白质文件
# --output: 输出文件前缀
# --output_dir: 输出目录
# --data_dir: 数据库目录
# --cpu: 使用的CPU核心数
# --override: 覆盖已存在的文件

emapper.py \
    -m diamond \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 4 \
    --override

echo ""
echo "=== 步骤2: 检查结果 ==="

# 检查主要输出文件
ANNOTATION_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$ANNOTATION_FILE" ]; then
    echo "✅ 注释完成！"
    echo "注释文件: $ANNOTATION_FILE"
    
    # 统计注释结果
    TOTAL_LINES=$(wc -l < "$ANNOTATION_FILE")
    HEADER_LINES=$(grep -c "^#" "$ANNOTATION_FILE")
    ANNOTATED_SEQS=$((TOTAL_LINES - HEADER_LINES))
    
    echo "总行数: $TOTAL_LINES"
    echo "注释序列数: $ANNOTATED_SEQS"
    
    # 统计有功能注释的序列
    echo ""
    echo "=== 注释统计 ==="
    
    # 有GO注释的序列
    GO_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f6 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有GO注释的序列: $GO_COUNT"
    
    # 有KEGG注释的序列
    KEGG_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有KEGG注释的序列: $KEGG_COUNT"
    
    # 有COG注释的序列
    COG_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f21 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有COG注释的序列: $COG_COUNT"
    
    # 有基因名的序列
    GENE_NAME_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f5 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有基因名的序列: $GENE_NAME_COUNT"
    
    echo ""
    echo "前5行注释结果预览:"
    head -n 10 "$ANNOTATION_FILE"
    
else
    echo "❌ 注释失败，请检查错误信息"
    exit 1
fi

echo ""
echo "完成时间: $(date)"
echo "=== EggNOG-mapper注释完成 ==="

# 列出所有输出文件
echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"
