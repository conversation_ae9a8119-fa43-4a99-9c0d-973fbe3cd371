#!/usr/bin/env python3
"""
分析EggNOG-mapper注释结果
"""

import pandas as pd
import numpy as np

def analyze_eggnog_results():
    print("=== 盲鳗蛋白质EggNOG注释结果分析 ===\n")
    
    # 读取注释文件
    annotation_file = "../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
    
    # 读取数据，跳过注释行
    df = pd.read_csv(annotation_file, sep='\t', comment='#', header=0)
    
    print(f"1. 基本统计:")
    print(f"   - 总序列数: {len(df):,}")
    print(f"   - 注释文件大小: {len(df)} 行")
    
    # 统计各种注释类型
    print(f"\n2. 功能注释统计:")
    
    # 先查看列名
    print(f"   - 数据列数: {len(df.columns)}")

    # 使用列索引而不是列名
    # 根据EggNOG-mapper标准格式：
    # 0: query, 1: seed_ortholog, 2: evalue, 3: score, 4: eggNOG_OGs, 5: max_annot_lvl,
    # 6: COG_category, 7: Description, 8: Preferred_name, 9: GOs, 10: EC, 11: KEGG_ko,
    # 12: KEGG_Pathway, 13: KEGG_Module, 14: KEGG_Reaction, 15: KEGG_rclass,
    # 16: BRITE, 17: KEGG_TC, 18: CAZy, 19: BiGG_Reaction, 20: PFAMs

    # 基因名称 (第9列)
    if len(df.columns) > 8:
        gene_names = df.iloc[:, 8].fillna('-').astype(str)
        has_gene_name = (gene_names != '-') & (gene_names != '')
        print(f"   - 有基因名称: {has_gene_name.sum():,} ({has_gene_name.mean()*100:.1f}%)")

    # 功能描述 (第8列)
    if len(df.columns) > 7:
        descriptions = df.iloc[:, 7].fillna('-').astype(str)
        has_description = (descriptions != '-') & (descriptions != '')
        print(f"   - 有功能描述: {has_description.sum():,} ({has_description.mean()*100:.1f}%)")

    # GO注释 (第10列)
    if len(df.columns) > 9:
        go_terms = df.iloc[:, 9].fillna('-').astype(str)
        has_go = (go_terms != '-') & (go_terms != '')
        print(f"   - 有GO注释: {has_go.sum():,} ({has_go.mean()*100:.1f}%)")

    # KEGG注释 (第12列)
    if len(df.columns) > 11:
        kegg_ko = df.iloc[:, 11].fillna('-').astype(str)
        has_kegg = (kegg_ko != '-') & (kegg_ko != '')
        print(f"   - 有KEGG注释: {has_kegg.sum():,} ({has_kegg.mean()*100:.1f}%)")

    # COG分类 (第7列)
    if len(df.columns) > 6:
        cog_cat = df.iloc[:, 6].fillna('-').astype(str)
        has_cog = (cog_cat != '-') & (cog_cat != '')
        print(f"   - 有COG分类: {has_cog.sum():,} ({has_cog.mean()*100:.1f}%)")

    # EC号 (第11列)
    if len(df.columns) > 10:
        ec_numbers = df.iloc[:, 10].fillna('-').astype(str)
        has_ec = (ec_numbers != '-') & (ec_numbers != '')
        print(f"   - 有EC号: {has_ec.sum():,} ({has_ec.mean()*100:.1f}%)")

    # PFAM结构域 (第21列)
    if len(df.columns) > 20:
        pfams = df.iloc[:, 20].fillna('-').astype(str)
        has_pfam = (pfams != '-') & (pfams != '')
        print(f"   - 有PFAM结构域: {has_pfam.sum():,} ({has_pfam.mean()*100:.1f}%)")
    
    print(f"\n3. COG功能分类统计:")
    # COG分类统计
    if len(df.columns) > 6 and has_cog.sum() > 0:
        cog_categories = df[has_cog].iloc[:, 6].str.split('').explode().value_counts()
        cog_categories = cog_categories[cog_categories.index != '']
    
    # COG分类对应表
    cog_descriptions = {
        'J': 'Translation, ribosomal structure and biogenesis',
        'A': 'RNA processing and modification', 
        'K': 'Transcription',
        'L': 'Replication, recombination and repair',
        'B': 'Chromatin structure and dynamics',
        'D': 'Cell cycle control, cell division, chromosome partitioning',
        'Y': 'Nuclear structure',
        'V': 'Defense mechanisms',
        'T': 'Signal transduction mechanisms',
        'M': 'Cell wall/membrane/envelope biogenesis',
        'N': 'Cell motility',
        'Z': 'Cytoskeleton',
        'W': 'Extracellular structures',
        'U': 'Intracellular trafficking, secretion, and vesicular transport',
        'O': 'Posttranslational modification, protein turnover, chaperones',
        'C': 'Energy production and conversion',
        'G': 'Carbohydrate transport and metabolism',
        'E': 'Amino acid transport and metabolism',
        'F': 'Nucleotide transport and metabolism',
        'H': 'Coenzyme transport and metabolism',
        'I': 'Lipid transport and metabolism',
        'P': 'Inorganic ion transport and metabolism',
        'Q': 'Secondary metabolites biosynthesis, transport and catabolism',
        'R': 'General function prediction only',
        'S': 'Function unknown'
    }
    
    print("   前10个COG分类:")
    for i, (cat, count) in enumerate(cog_categories.head(10).items()):
        desc = cog_descriptions.get(cat, 'Unknown')
        print(f"   {i+1:2d}. [{cat}] {desc}: {count:,}")
    
    print(f"\n4. 常见基因名称:")
    # 统计常见基因名称
    if len(df.columns) > 8 and has_gene_name.sum() > 0:
        common_genes = df[has_gene_name].iloc[:, 8].value_counts().head(10)
        for i, (gene, count) in enumerate(common_genes.items()):
            print(f"   {i+1:2d}. {gene}: {count:,}")

    print(f"\n5. 注释质量评估:")
    # 综合注释质量
    if len(df.columns) > 9:
        well_annotated = has_gene_name & has_description & has_go
        print(f"   - 高质量注释 (有基因名+描述+GO): {well_annotated.sum():,} ({well_annotated.mean()*100:.1f}%)")

        basic_annotated = has_description | has_go | has_kegg
        print(f"   - 基本注释 (有描述或GO或KEGG): {basic_annotated.sum():,} ({basic_annotated.mean()*100:.1f}%)")

        no_annotation = ~basic_annotated
        print(f"   - 无功能注释: {no_annotation.sum():,} ({no_annotation.mean()*100:.1f}%)")

        print(f"\n6. 示例注释结果:")
        # 显示一些好的注释例子
        if well_annotated.sum() > 0:
            good_examples = df[well_annotated].head(3)
            for i, (_, row) in enumerate(good_examples.iterrows()):
                print(f"   例子 {i+1}:")
                print(f"     序列ID: {row.iloc[0]}")
                print(f"     基因名: {row.iloc[8]}")
                print(f"     功能: {row.iloc[7]}")
                print(f"     COG: [{row.iloc[6]}]")
                print()
    
    print("=== 分析完成 ===")
    
    return df

if __name__ == "__main__":
    df = analyze_eggnog_results()
