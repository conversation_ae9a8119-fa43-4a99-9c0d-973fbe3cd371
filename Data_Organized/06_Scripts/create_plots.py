#!/usr/bin/env python3
"""
创建火山图和热图
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10
sns.set_style("whitegrid")

def create_volcano_plot(tissue_name, results_file, output_prefix):
    """创建火山图"""
    print(f"创建 {tissue_name} 火山图...")
    
    try:
        # 读取DESeq2结果
        res_data = pd.read_csv(results_file, sep='\t')
        
        # 添加显著性标签
        res_data['significance'] = 'Not Significant'
        up_mask = (res_data['padj'] < 0.05) & (res_data['log2FoldChange'] > 1)
        down_mask = (res_data['padj'] < 0.05) & (res_data['log2FoldChange'] < -1)
        
        res_data.loc[up_mask, 'significance'] = 'Up-regulated'
        res_data.loc[down_mask, 'significance'] = 'Down-regulated'
        
        # 统计显著基因数
        n_up = up_mask.sum()
        n_down = down_mask.sum()
        n_total = n_up + n_down
        
        print(f"   - 上调基因: {n_up}, 下调基因: {n_down}, 总计: {n_total}")
        
        # 创建火山图
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 绘制点
        colors = {'Up-regulated': '#FF6B6B', 'Down-regulated': '#4ECDC4', 'Not Significant': '#CCCCCC'}
        
        for sig_type in ['Not Significant', 'Down-regulated', 'Up-regulated']:
            mask = res_data['significance'] == sig_type
            if mask.sum() > 0:
                ax.scatter(res_data[mask]['log2FoldChange'], 
                          -np.log10(res_data[mask]['padj'] + 1e-300),  # 避免log(0)
                          c=colors[sig_type], 
                          alpha=0.6, 
                          s=20 if sig_type != 'Not Significant' else 10,
                          label=f'{sig_type} ({mask.sum()})')
        
        # 添加阈值线
        ax.axvline(x=1, color='gray', linestyle='--', alpha=0.7)
        ax.axvline(x=-1, color='gray', linestyle='--', alpha=0.7)
        ax.axhline(y=-np.log10(0.05), color='gray', linestyle='--', alpha=0.7)
        
        # 设置标签和标题
        ax.set_xlabel('Log2 Fold Change', fontsize=12)
        ax.set_ylabel('-Log10 (Adjusted P-value)', fontsize=12)
        ax.set_title(f'{tissue_name} vs Others\nVolcano Plot\n(Up: {n_up}, Down: {n_down}, Total DEGs: {n_total})', 
                    fontsize=14, fontweight='bold')
        
        # 图例
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 美化
        ax.grid(True, alpha=0.3)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(f"../10_Annotation/{output_prefix}_volcano_plot.png", bbox_inches='tight')
        plt.savefig(f"../10_Annotation/{output_prefix}_volcano_plot.pdf", bbox_inches='tight')
        plt.close()
        
        print(f"   - 火山图已保存: {output_prefix}_volcano_plot.png/pdf")
        
    except Exception as e:
        print(f"   - 错误: 无法创建{tissue_name}火山图 - {e}")

def create_summary_barplot():
    """创建汇总柱状图"""
    print("创建汇总柱状图...")
    
    try:
        # 读取汇总数据
        summary_data = pd.read_csv("../10_Annotation/DESeq2_analysis_summary.txt", sep='\t')
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：总差异基因数
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        bars1 = ax1.bar(summary_data['tissue'], summary_data['significant_genes'], 
                       color=colors, alpha=0.8, edgecolor='black')
        
        # 添加数值标签
        for bar, value in zip(bars1, summary_data['significant_genes']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 50,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        ax1.set_title('Number of Significant DEGs by Tissue\n(padj < 0.05, |log2FC| > 1)', 
                     fontsize=12, fontweight='bold')
        ax1.set_ylabel('Number of Significant DEGs')
        ax1.set_xlabel('Tissue')
        ax1.grid(True, alpha=0.3)
        
        # 右图：上调vs下调
        x = np.arange(len(summary_data))
        width = 0.35
        
        bars2 = ax2.bar(x - width/2, summary_data['up_regulated'], width, 
                       label='Up-regulated', color='#FF6B6B', alpha=0.8)
        bars3 = ax2.bar(x + width/2, summary_data['down_regulated'], width,
                       label='Down-regulated', color='#4ECDC4', alpha=0.8)
        
        # 添加数值标签
        for bars in [bars2, bars3]:
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 20,
                            f'{int(height)}', ha='center', va='bottom', fontsize=9)
        
        ax2.set_title('Up-regulated vs Down-regulated DEGs', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Number of DEGs')
        ax2.set_xlabel('Tissue')
        ax2.set_xticks(x)
        ax2.set_xticklabels(summary_data['tissue'])
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig("../10_Annotation/DEG_summary_plots.png", bbox_inches='tight')
        plt.savefig("../10_Annotation/DEG_summary_plots.pdf", bbox_inches='tight')
        plt.close()
        
        print("   - 汇总图已保存: DEG_summary_plots.png/pdf")
        
    except Exception as e:
        print(f"   - 错误: 无法创建汇总图 - {e}")

def create_expression_heatmap():
    """创建表达热图"""
    print("创建表达热图...")
    
    try:
        # 读取表达数据
        count_data = pd.read_csv("../08_Results/hagfish_expression_matrix.txt", 
                               sep='\t', index_col=0)
        
        # 收集top差异基因
        all_sig_genes = []
        tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
        
        for tissue in tissues:
            sig_file = f"../10_Annotation/{tissue}_significant_DEGs.txt"
            try:
                sig_data = pd.read_csv(sig_file, sep='\t')
                if len(sig_data) > 0:
                    # 按log2FoldChange排序，取top 15
                    top_genes = sig_data.nlargest(15, 'log2FoldChange')
                    all_sig_genes.extend(top_genes['gene_id'].tolist())
            except:
                continue
        
        # 去重并筛选存在的基因
        all_sig_genes = list(set(all_sig_genes))
        all_sig_genes = [g for g in all_sig_genes if g in count_data.index]
        
        if len(all_sig_genes) == 0:
            print("   - 没有找到匹配的显著差异基因")
            return
        
        # 限制基因数量
        if len(all_sig_genes) > 50:
            all_sig_genes = all_sig_genes[:50]
        
        print(f"   - 选择了 {len(all_sig_genes)} 个显著差异基因用于热图")
        
        # 提取表达数据并log转换
        heatmap_data = count_data.loc[all_sig_genes]
        heatmap_data_log = np.log2(heatmap_data + 1)
        
        # 标准化 (Z-score)
        heatmap_data_norm = heatmap_data_log.sub(heatmap_data_log.mean(axis=1), axis=0).div(heatmap_data_log.std(axis=1), axis=0)
        
        # 样本注释
        sample_colors = []
        for col in heatmap_data.columns:
            if any(x in col for x in ['L1', 'L2', 'L3', 'L4', 'L5', 'L6']):
                sample_colors.append('#FF6B6B')  # 鳃
            elif 'intes' in col:
                sample_colors.append('#4ECDC4')  # 肠
            elif 'ki' in col:
                sample_colors.append('#45B7D1')  # 肾
            elif 'leu' in col:
                sample_colors.append('#96CEB4')  # 白细胞
            else:
                sample_colors.append('#CCCCCC')  # 其他
        
        # 创建热图
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制热图
        im = ax.imshow(heatmap_data_norm.values, cmap='RdBu_r', aspect='auto', vmin=-2, vmax=2)
        
        # 设置坐标轴
        ax.set_xticks(range(len(heatmap_data.columns)))
        ax.set_xticklabels(heatmap_data.columns, rotation=45, ha='right')
        ax.set_yticks(range(len(all_sig_genes)))
        ax.set_yticklabels(all_sig_genes, fontsize=6)
        
        # 添加样本颜色条
        for i, color in enumerate(sample_colors):
            ax.add_patch(plt.Rectangle((i-0.4, -1.5), 0.8, 0.8, facecolor=color, alpha=0.8))
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Z-score (Log2 Expression)', fontsize=10)
        
        ax.set_title('Expression Heatmap of Top Significant DEGs\n(Z-score normalized)', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Samples')
        ax.set_ylabel('Genes')
        
        plt.tight_layout()
        plt.savefig("../10_Annotation/expression_heatmap.png", bbox_inches='tight')
        plt.savefig("../10_Annotation/expression_heatmap.pdf", bbox_inches='tight')
        plt.close()
        
        print("   - 表达热图已保存: expression_heatmap.png/pdf")
        
    except Exception as e:
        print(f"   - 错误: 无法创建热图 - {e}")

def main():
    print("=== 创建火山图和可视化 ===\n")
    
    # 创建火山图
    tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    tissue_names = ['Gill (鳃)', 'Intestine (肠)', 'Kidney (肾)', 'Leukocyte (白细胞)']
    
    for tissue, tissue_name in zip(tissues, tissue_names):
        results_file = f"../10_Annotation/{tissue}_vs_others_DESeq2.txt"
        create_volcano_plot(tissue_name, results_file, tissue)
    
    # 创建汇总图
    create_summary_barplot()
    
    # 创建热图
    create_expression_heatmap()
    
    print("\n=== 所有图表创建完成 ===")
    print("生成的图表文件:")
    print("1. *_volcano_plot.png/pdf - 各组织火山图")
    print("2. DEG_summary_plots.png/pdf - 差异基因汇总图")
    print("3. expression_heatmap.png/pdf - 表达热图")
    print("\n所有图表已保存到: ../10_Annotation/ 目录")

if __name__ == "__main__":
    main()
