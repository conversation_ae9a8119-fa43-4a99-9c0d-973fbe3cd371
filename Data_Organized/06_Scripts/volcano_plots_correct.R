#!/usr/bin/env Rscript

# 基于参考代码的火山图绘制
# 参考: Data_Organized/盲鳗二代参考代码/volcano_plot.R

library(ggplot2)
library(dplyr)
library(ggrepel)  # 用于避免标签重叠

cat("=== 创建火山图 (参考标准方法) ===\n\n")

# 获取所有差异表达结果文件
de_files <- list.files("../10_Annotation/deseq2_results", 
                      pattern = "DE_.*_vs_all\\.csv", full.names = TRUE)

cat(sprintf("找到 %d 个差异表达结果文件\n", length(de_files)))

# 创建火山图目录
if (!dir.exists("../10_Annotation/volcano_plots")) {
  dir.create("../10_Annotation/volcano_plots")
}

# 对每个差异表达结果文件绘制火山图
for (de_file in de_files) {
  # 提取组织名称
  tissue <- gsub(".*DE_hg3(.*)_vs_all\\.csv", "\\1", de_file)
  
  cat(sprintf("\n创建 %s 火山图...\n", tissue))
  
  # 读取差异表达结果并预处理
  de_results <- read.csv(de_file) %>%
    filter(!is.na(padj)) %>%  # 移除padj为NA的行
    filter(!is.na(pvalue)) %>% # 确保pvalue不为NA
    mutate(
      # 处理pvalue=0的情况：
      # 1. 首先找到数据集中最小的非零pvalue值
      # 2. 将所有pvalue=0的值替换为该最小值
      # 这种处理方式可以避免计算-log10(0)得到无限大值
      # 同时保持数据的相对关系
      min_pvalue = min(pvalue[pvalue > 0], na.rm = TRUE),
      pvalue = ifelse(pvalue == 0, min_pvalue, pvalue),
      # 添加显著性标记
      significant = ifelse(abs(log2FoldChange) > 1 & padj < 0.05, 
                          "Significant", "Not significant"),
      # 添加基因名列
      gene = as.character(gene_id)
    ) %>%
    select(-min_pvalue)  # 移除临时变量
  
  # 统计显著基因数
  n_sig <- sum(de_results$significant == "Significant")
  n_up <- sum(de_results$log2FoldChange > 1 & de_results$padj < 0.05)
  n_down <- sum(de_results$log2FoldChange < -1 & de_results$padj < 0.05)
  
  cat(sprintf("   - 显著基因: %d (上调: %d, 下调: %d)\n", n_sig, n_up, n_down))
  
  # 获取上下调top5基因
  top_up <- de_results %>%
    filter(log2FoldChange > 1 & padj < 0.05) %>%
    arrange(desc(log2FoldChange), pvalue) %>%
    head(5)
  
  top_down <- de_results %>%
    filter(log2FoldChange < -1 & padj < 0.05) %>%
    arrange(log2FoldChange, pvalue) %>%
    head(5)
  
  # 合并要标记的基因
  genes_to_label <- bind_rows(top_up, top_down)
  
  # 绘制火山图
  p <- ggplot(de_results, aes(x = log2FoldChange, y = -log10(pvalue), 
                            color = -log10(pvalue), size = abs(log2FoldChange))) +
    geom_point(alpha = 0.7) +  # 调整透明度
    # 标记top基因
    geom_text_repel(
      data = genes_to_label,
      aes(label = gene, color = NULL, size = NULL),
      color = "black",
      size = 3,
      box.padding = 0.5,
      point.padding = 0.5,
      max.overlaps = 100,
      segment.color = "grey50",
      min.segment.length = 0.2
    ) +
    # 颜色渐变设置
    scale_color_gradientn(
      colours = c("blue", "green", "yellow", "red"),
      name = "-log10(p-value)",
      guide = guide_colorbar(barwidth = 1, barheight = 15)
    ) +
    # 点大小设置
    scale_size_continuous(
      range = c(1, 3),
      name = "|log2FC|",
      guide = guide_legend(override.aes = list(color = "grey50"))
    ) +
    labs(
      title = paste("Volcano Plot:", tissue, "vs All"),
      subtitle = paste("Up:", n_up, "Down:", n_down, "Total DEGs:", n_sig),
      x = "log2 Fold Change",
      y = "-log10(p-value)",
      caption = "Significance threshold: |log2FC| > 1, padj < 0.05"
    ) +
    theme_minimal(base_size = 12) +
    theme(
      legend.position = "right",
      plot.title = element_text(face = "bold", hjust = 0.5),
      plot.subtitle = element_text(hjust = 0.5),
      panel.grid.major = element_line(color = "grey90"),
      panel.grid.minor = element_blank(),
      axis.line = element_line(color = "black")
    ) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey50") +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey50")
  
  # 设置坐标轴范围
  if(nrow(de_results) > 0) {
    max_fc <- max(abs(de_results$log2FoldChange), na.rm = TRUE)
    p <- p + coord_cartesian(xlim = c(-max_fc, max_fc))
  }
  
  # 保存火山图(PNG和PDF格式)
  ggsave(
    paste0("../10_Annotation/volcano_plots/volcano_", tissue, ".png"), 
    plot = p, 
    width = 12,
    height = 8,
    dpi = 300,
    bg = "white"
  )
  
  ggsave(
    paste0("../10_Annotation/volcano_plots/volcano_", tissue, ".pdf"), 
    plot = p, 
    width = 12,
    height = 8,
    device = "pdf",
    bg = "white"
  )
  
  cat(sprintf("   - 火山图已保存: volcano_%s.png/pdf\n", tissue))
}

cat("\n=== 火山图创建完成 ===\n")
cat("所有火山图已保存到 ../10_Annotation/volcano_plots/ 目录\n")
