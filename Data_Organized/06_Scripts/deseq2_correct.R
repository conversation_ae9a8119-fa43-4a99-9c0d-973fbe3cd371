#!/usr/bin/env Rscript

# 基于参考代码的正确DESeq2分析
# 参考: Data_Organized/盲鳗二代参考代码/deseq2_analysis.R

# 加载必要的包
library(DESeq2)

cat("=== 盲鳗组织DESeq2差异表达分析 (参考标准方法) ===\n\n")

# 读取counts数据
cat("1. 读取Count数据...\n")
count_data <- read.table("../08_Results/hagfish_expression_matrix.txt", 
                        header=TRUE, row.names=1, sep="\t")

cat(sprintf("   - Count矩阵: %d 基因 x %d 样本\n", nrow(count_data), ncol(count_data)))

# 组织样本列表 (根据参考代码的命名方式)
tissues <- c("L1", "L2", "L3", "L4", "L5", "L6", "sex", "intes", "noto", "axon", 
             "heart", "muscl", "gall", "liver", "brain", "solo", "skin1", "ki", 
             "leu", "skin2", "ovary")

cat("2. 样本信息:\n")
cat("   - 样本名称:", paste(colnames(count_data), collapse=", "), "\n")
cat("   - 分析组织:", paste(tissues, collapse=", "), "\n")

# 创建结果目录
if (!dir.exists("../10_Annotation/deseq2_results")) {
  dir.create("../10_Annotation/deseq2_results")
}

cat("\n3. 开始DESeq2分析...\n")

# 对每个组织进行差异表达分析
results_summary <- data.frame(
  tissue = character(),
  total_genes = integer(),
  significant_genes = integer(),
  up_regulated = integer(),
  down_regulated = integer(),
  stringsAsFactors = FALSE
)

for (tissue in tissues) {
  cat(sprintf("\n=== 分析 %s vs 其他组织 ===\n", tissue))
  
  # 创建样本分组信息
  sample_names <- colnames(count_data)
  condition <- ifelse(grepl(tissue, sample_names), tissue, "other")
  col_data <- data.frame(row.names=sample_names, condition=factor(condition))
  
  # 检查样本数
  target_n <- sum(condition == tissue)
  other_n <- sum(condition == "other")
  cat(sprintf("   - %s组: %d 样本\n", tissue, target_n))
  cat(sprintf("   - 其他组: %d 样本\n", other_n))
  
  if(target_n == 0) {
    cat("   - 跳过: 无目标组样本\n")
    next
  }
  
  # 创建DESeqDataSet对象
  dds <- DESeqDataSetFromMatrix(countData=count_data,
                               colData=col_data,
                               design=~condition)
  
  # 过滤低表达基因 (参考代码的方法)
  keep <- rowSums(counts(dds)) >= 10
  dds <- dds[keep,]
  
  cat(sprintf("   - 过滤后基因数: %d\n", nrow(dds)))
  
  # 运行DESeq分析
  cat("   - 运行DESeq2分析...\n")
  dds <- DESeq(dds)
  
  # 获取结果
  res <- results(dds, contrast=c("condition", tissue, "other"))
  
  # 转换为数据框并添加基因ID
  res_df <- as.data.frame(res)
  res_df$gene_id <- rownames(res_df)
  
  # 移除NA值
  res_df <- res_df[!is.na(res_df$padj), ]
  
  # 筛选显著差异基因
  sig_genes <- res_df[res_df$padj < 0.05 & abs(res_df$log2FoldChange) > 1, ]
  up_genes <- sig_genes[sig_genes$log2FoldChange > 0, ]
  down_genes <- sig_genes[sig_genes$log2FoldChange < 0, ]
  
  cat(sprintf("   - 显著差异基因: %d (上调: %d, 下调: %d)\n", 
              nrow(sig_genes), nrow(up_genes), nrow(down_genes)))
  
  # 保存结果 (按照参考代码的命名方式)
  output_file <- sprintf("../10_Annotation/deseq2_results/DE_hg3%s_vs_all.csv", tissue)
  write.csv(res_df, file=output_file, row.names=FALSE)
  cat(sprintf("   - 结果已保存: %s\n", output_file))
  
  # 保存显著基因
  if(nrow(sig_genes) > 0) {
    sig_file <- sprintf("../10_Annotation/deseq2_results/DE_hg3%s_vs_all_significant.csv", tissue)
    write.csv(sig_genes, file=sig_file, row.names=FALSE)
    cat(sprintf("   - 显著基因已保存: %s\n", sig_file))
  }
  
  # 添加到汇总
  results_summary <- rbind(results_summary, data.frame(
    tissue = tissue,
    total_genes = nrow(res_df),
    significant_genes = nrow(sig_genes),
    up_regulated = nrow(up_genes),
    down_regulated = nrow(down_genes)
  ))
}

# 保存汇总结果
write.csv(results_summary, "../10_Annotation/deseq2_results/analysis_summary.csv", row.names=FALSE)

cat("\n4. 分析结果汇总:\n")
for(i in 1:nrow(results_summary)) {
  row <- results_summary[i, ]
  cat(sprintf("   - %s: %d个差异基因 (↑%d, ↓%d)\n", 
              row$tissue, row$significant_genes, row$up_regulated, row$down_regulated))
}

cat("\n=== DESeq2分析完成 ===\n")
cat("结果文件已保存到 ../10_Annotation/deseq2_results/ 目录\n")
cat("接下来可以运行火山图脚本\n")
