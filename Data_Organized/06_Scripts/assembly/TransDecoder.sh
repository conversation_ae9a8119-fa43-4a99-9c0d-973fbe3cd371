#!/bin/bash
# TransDecoder.sh
# 功能：从转录本预测 ORF，结合 BLASTP 和 PFAM 信息，最终提取无同源性 ORFs

# ===================== 参数设置 =====================
# 输入转录本 fasta（由 StringTie 提取）
transcripts="/public/home/<USER>/2025hagfish/NGS/Data/stringtie_merge/transcripts.fa"

# PFAM 数据库（已 hmmpress）
pfam_db="/public/home/<USER>/database/PFAM/Pfam-A.hmm"

# SwissProt BLAST 数据库（已格式化）
swissprot_db="/public/home/<USER>/database/protein/SwissProt/Swiss_prot_library"

# 线程数
threads=10

# 工作目录
workdir=$(dirname "$transcripts")
cd "$workdir"

# ===================== Step 1: 提取候选 ORFs =====================
echo "Step 1: 提取候选 ORFs"
TransDecoder.LongOrfs -t "$transcripts" -m 100

# ===================== Step 2: BLASTP 比对 =====================
echo "Step 2: BLASTP（比对 SwissProt）"
blastp -query "${transcripts}.transdecoder_dir/longest_orfs.pep" \
  -db "$swissprot_db" \
  -evalue 1e-5 -num_threads "$threads" -max_target_seqs 1 -outfmt 6 \
  > blastp.outfmt6

# ===================== Step 3: Pfam 结构域注释 =====================
echo "Step 3: PFAM 搜索"
hmmsearch --cpu "$threads" -E 1e-10 \
  --domtblout pfam.domtblout "$pfam_db" \
  "${transcripts}.transdecoder_dir/longest_orfs.pep"

# ===================== Step 4: 整合同源信息进行最终预测 =====================
echo "Step 4: 最终 ORF 预测（保留有 Pfam/BLAST 命中的 ORF）"
TransDecoder.Predict -t "$transcripts" \
  --retain_blastp_hits blastp.outfmt6 \
  --retain_pfam_hits pfam.domtblout \
  --single_best_only

# ===================== Step 5: 可选生成基因组坐标注释 =====================
if [ -f transcripts.gff3 ]; then
  echo "Step 5: 基因组坐标 GFF3"
  ../TransDecoder/util/cdna_alignment_orf_to_genome_orf.pl \
    "${transcripts}.transdecoder.gff3" transcripts.gff3 "$transcripts" \
    > "${transcripts}.transdecoder.genome.gff3"
else
  echo "未检测到 transcripts.gff3，跳过 genome GFF3 映射"
fi

# ===================== Step 6: 提取被排除的 ORFs =====================
echo "Step 6: 提取无同源性 ORFs"
all_orfs="${transcripts}.transdecoder_dir/longest_orfs.pep"
final_orfs="${transcripts}.transdecoder.pep"
unannotated_orfs="${transcripts}.no_homology_filtered.pep"

# 需要安装 seqkit： conda install -c bioconda seqkit
seqkit grep -v -f <(grep "^>" "$final_orfs" | sed 's/^>//') "$all_orfs" > "$unannotated_orfs"

echo "完成，最终输出文件："
echo "有同源性预测 ORFs：$final_orfs"
echo "无同源性 ORFs（候选新基因）：$unannotated_orfs"