#!/bin/bash
# Step 1: 单样本拼接 GTF 文件

# 输入 BAM 文件目录
bamdir="/public/home/<USER>/2025hagfish/NGS/Data/Hisat2"

# 输出 GTF 目录（拼接结果）
outdir="/public/home/<USER>/2025hagfish/NGS/Data/stringtie"
mkdir -p "$outdir"

# 执行拼接
cd "$bamdir"
for bam in *.sorted.bam; do
    id=$(basename "$bam" .sorted.bam)
    echo "正在处理样本：$id"
    stringtie "$bam" -p 8 -o "${outdir}/${id}.gtf" -c 2 -j 3 -a 15 -m 200 -f 0.05
done

echo "所有样本拼接完成，GTF 文件已保存至：$outdir"
