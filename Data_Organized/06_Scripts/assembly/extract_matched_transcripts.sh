#!/bin/bash

# 设置路径
cds_fa="/public/home/<USER>/2025hagfish/NGS/Data/stringtie_merge/transcripts.fa.transdecoder.cds"
transcript_fa="/public/home/<USER>/2025hagfish/NGS/Data/stringtie_merge/transcripts.fa"
output_fa="/public/home/<USER>/2025hagfish/NGS/Data/stringtie_merge/cds_matched_full_transcripts.fa"

# Step 1: 生成 ID 对照表（用于替换）
awk '/^>/{ split($1, arr, ".p"); orig=arr[1]; sub(/^>/, "", $0); print "^" orig "$\t" $0 }' "$cds_fa" > id_patterns.tsv

# Step 2: 提取目标转录本序列
cut -f1 id_patterns.tsv | sed 's/^\^//;s/\$//' | sort -u > wanted_ids.txt
seqkit grep -n -f wanted_ids.txt "$transcript_fa" > extracted.fa

# Step 3: 用 pattern 表替换 header（使用正则）
seqkit replace -p-file id_patterns.tsv extracted.fa -o "$output_fa"

# Step 4: 清理
rm id_patterns.tsv wanted_ids.txt extracted.fa

echo "✅ 提取并重命名完成！结果文件：$output_fa"
