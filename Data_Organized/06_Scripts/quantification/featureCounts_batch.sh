#!/bin/bash
# featureCounts_batch.sh
# ----------------------
# 这个脚本用于批量对 HISAT2 比对后的 BAM 文件进行基因计数，
# 并生成一个包含所有样本表达水平的表达矩阵文件。
#
# 脚本流程：
# 1. 设置 HISAT2 比对结果和注释文件所在目录（输入目录）。
# 2. 自动查找输入目录下的第一个 .gtf.gz 注释文件，并赋值给变量 gtf。
# 3. 定义输出目录，用于存放 featureCounts 结果文件。
# 4. 调用 featureCounts 对所有排序后的 BAM 文件进行计数。
# 5. 从 featureCounts 的输出文件中提取出基因表达矩阵（基因 ID 及各样本计数）。
#
# 备注：本脚本假定 BAM 文件的命名格式为 "*sorted.bam"，
#        且注释文件（.gtf.gz）位于输入目录中或同级目录中。
# ----------------------

# 定义输入目录，包含 HISAT2 的比对结果（BAM 文件）和注释文件
inputdir="/public/home/<USER>/2025hagfish_1.5T/NGS/Data/Hisat2"

# 自动查找输入目录下的 .gtf.gz 注释文件（取第一个匹配的文件）
gtf=$(find "$inputdir" -maxdepth 1 -name "*.gtf.gz" | head -n 1)
if [ -z "$gtf" ]; then
    echo "未找到 .gtf.gz 注释文件，请检查目录 $inputdir 中是否存在 .gtf.gz 文件。"
    exit 1
fi
echo "使用的注释文件：$gtf"

# 定义输出目录，用于存放 featureCounts 的输出文件
outdir="/public/home/<USER>/2025hagfish_1.5T/NGS/Data/FeatureCounts"
mkdir -p "$outdir"

# 调用 featureCounts 对所有排序后的 BAM 文件计数
# 参数说明：
# -T        ：使用线程数
# -p          ：表明输入数据为双端（paired-end）数据
# -t exon     ：统计注释文件中 type 为 exon 的区域
# -g gene_id  ：按照注释文件中 gene_id 属性对读段进行分组计数
# -a "$gtf"   ：指定注释文件（支持 .gtf.gz 格式）
# -o "$outdir/all.id.txt" ：输出计数结果文件
# "$inputdir"/*sorted.bam ：所有以 sorted.bam 结尾的 BAM 文件
nohup featureCounts -T 8 -p -t exon -g gene_id -a "$gtf" -o "$outdir/all.id.txt" "$inputdir"/*sorted.bam &

# 注意：由于 featureCounts 运行可能需要一定时间，你可以先检查是否生成了
#       all.id.txt 和 all.id.txt.summary 文件，再进行下一步。

# 从 featureCounts 输出文件 all.id.txt 中提取表达矩阵
# featureCounts 的输出文件中：
#   - 第一列是 gene_id，
#   - 第7列及以后为各个样本的读数计数
cat "$outdir/all.id.txt" | cut -f 1,7- > "$outdir/counts.txt"

echo "所有样本计数完成，表达矩阵保存在：$outdir/counts.txt"
