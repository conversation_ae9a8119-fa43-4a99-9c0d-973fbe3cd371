#!/usr/bin/env python3
"""
创建组织特异性分析的可视化图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

def create_summary_barplot():
    """创建组织特异性基因数量汇总柱状图"""
    print("1. 创建组织特异性基因数量汇总图...")
    
    # 读取汇总数据
    summary_data = {
        'Tissue': ['Gill\n(鳃)', 'Intestine\n(肠)', 'Kidney\n(肾)', 'Leukocyte\n(白细胞)'],
        'Significant_Genes': [959, 2169, 156, 546],
        'Colors': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    }
    
    df = pd.DataFrame(summary_data)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    bars = ax.bar(df['Tissue'], df['Significant_Genes'], 
                  color=df['Colors'], alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, value in zip(bars, df['Significant_Genes']):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                f'{value}', ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    ax.set_title('Hagfish Tissue-Specific Gene Expression\n盲鳗组织特异性基因表达数量', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel('Number of Tissue-Specific Genes\n组织特异性基因数量', fontsize=12)
    ax.set_xlabel('Tissue Type\n组织类型', fontsize=12)
    
    # 设置y轴范围
    ax.set_ylim(0, max(df['Significant_Genes']) * 1.15)
    
    # 美化图表
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('../10_Annotation/tissue_specific_genes_summary.png', dpi=300, bbox_inches='tight')
    plt.savefig('../10_Annotation/tissue_specific_genes_summary.pdf', bbox_inches='tight')
    print("   - 保存: tissue_specific_genes_summary.png/pdf")
    plt.close()

def create_expression_heatmap():
    """创建top基因的表达热图"""
    print("2. 创建top基因表达热图...")
    
    # 读取表达数据
    expr_data = pd.read_csv("../08_Results/hagfish_expression_matrix.txt", 
                           sep='\t', index_col=0)
    
    # 读取各组织的top基因
    tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    top_genes = []
    gene_labels = []
    
    for tissue in tissues:
        try:
            sig_file = f"../10_Annotation/{tissue}_annotated_genes.txt"
            sig_data = pd.read_csv(sig_file, sep='\t')
            
            # 取top 5基因
            top_5 = sig_data.head(5)
            for _, row in top_5.iterrows():
                gene_id = row['gene_id']
                gene_name = row['gene_name'] if row['gene_name'] != 'Unknown' else gene_id
                fold_change = row['fold_change']
                
                if gene_id in expr_data.index:
                    top_genes.append(gene_id)
                    gene_labels.append(f"{gene_name}\n({tissue}, {fold_change:.1f}x)")
        except:
            continue
    
    if len(top_genes) > 0:
        # 提取表达数据
        heatmap_data = expr_data.loc[top_genes]
        
        # Log2转换 (加1避免log(0))
        heatmap_data_log = np.log2(heatmap_data + 1)
        
        # 创建热图
        fig, ax = plt.subplots(figsize=(14, 10))
        
        # 样本分组颜色
        sample_colors = []
        sample_labels = []
        for col in heatmap_data.columns:
            if 'L1' in col or 'L2' in col or 'L3' in col or 'L4' in col or 'L5' in col or 'L6' in col:
                sample_colors.append('#FF6B6B')  # 鳃 - 红色
                sample_labels.append('Gill')
            elif 'intes' in col:
                sample_colors.append('#4ECDC4')  # 肠 - 青色
                sample_labels.append('Intestine')
            elif 'ki' in col:
                sample_colors.append('#45B7D1')  # 肾 - 蓝色
                sample_labels.append('Kidney')
            elif 'leu' in col:
                sample_colors.append('#96CEB4')  # 白细胞 - 绿色
                sample_labels.append('Leukocyte')
            else:
                sample_colors.append('#CCCCCC')  # 其他 - 灰色
                sample_labels.append('Other')
        
        # 绘制热图
        im = ax.imshow(heatmap_data_log.values, cmap='RdYlBu_r', aspect='auto')
        
        # 设置坐标轴
        ax.set_xticks(range(len(heatmap_data.columns)))
        ax.set_xticklabels(heatmap_data.columns, rotation=45, ha='right')
        ax.set_yticks(range(len(gene_labels)))
        ax.set_yticklabels(gene_labels, fontsize=8)
        
        # 添加样本分组颜色条
        for i, (color, label) in enumerate(zip(sample_colors, sample_labels)):
            rect = Rectangle((i-0.4, -0.8), 0.8, 0.3, facecolor=color, alpha=0.8)
            ax.add_patch(rect)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Log2(Expression + 1)', fontsize=12)
        
        ax.set_title('Top Tissue-Specific Genes Expression Heatmap\n组织特异性基因表达热图', 
                    fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Samples\n样本', fontsize=12)
        ax.set_ylabel('Genes (Tissue, Fold Change)\n基因 (组织, 倍数变化)', fontsize=12)
        
        plt.tight_layout()
        plt.savefig('../10_Annotation/tissue_specific_heatmap.png', dpi=300, bbox_inches='tight')
        plt.savefig('../10_Annotation/tissue_specific_heatmap.pdf', bbox_inches='tight')
        print("   - 保存: tissue_specific_heatmap.png/pdf")
        plt.close()

def create_volcano_plots():
    """创建火山图显示差异表达基因"""
    print("3. 创建火山图...")
    
    tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    tissue_names = ['Gill (鳃)', 'Intestine (肠)', 'Kidney (肾)', 'Leukocyte (白细胞)']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i, (tissue, tissue_name, color) in enumerate(zip(tissues, tissue_names, colors)):
        try:
            # 读取结果数据
            results_file = f"../10_Annotation/{tissue}_vs_others_results.txt"
            data = pd.read_csv(results_file, sep='\t')
            
            # 计算-log10(p-value) 的替代值 (基于fold change)
            # 对于没有p值的数据，用fold change作为显著性指标
            data['neg_log10_p'] = np.where(data['fold_change'] > 4, 
                                          np.log10(data['fold_change']), 
                                          np.log10(data['fold_change']) * 0.5)
            
            # 筛选显示的点
            significant = (data['fold_change'] > 4) & (data[f'{tissue}_mean'] > 20)
            
            ax = axes[i]
            
            # 绘制所有点
            ax.scatter(data['log2_fold_change'], data['neg_log10_p'], 
                      c='lightgray', alpha=0.5, s=10)
            
            # 绘制显著点
            if significant.sum() > 0:
                ax.scatter(data[significant]['log2_fold_change'], 
                          data[significant]['neg_log10_p'],
                          c=color, alpha=0.8, s=20)
            
            # 添加阈值线
            ax.axvline(x=2, color='red', linestyle='--', alpha=0.7)  # 4倍变化
            ax.axhline(y=np.log10(4), color='red', linestyle='--', alpha=0.7)
            
            ax.set_xlabel('Log2 Fold Change', fontsize=10)
            ax.set_ylabel('Significance Score', fontsize=10)
            ax.set_title(f'{tissue_name} vs Others\n({significant.sum()} significant genes)', 
                        fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3)
            
        except Exception as e:
            print(f"   - 警告: 无法创建{tissue}火山图 - {e}")
            axes[i].text(0.5, 0.5, f'No data for {tissue}', 
                        ha='center', va='center', transform=axes[i].transAxes)
    
    plt.suptitle('Volcano Plots for Tissue-Specific Expression\n组织特异性表达火山图', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('../10_Annotation/tissue_volcano_plots.png', dpi=300, bbox_inches='tight')
    plt.savefig('../10_Annotation/tissue_volcano_plots.pdf', bbox_inches='tight')
    print("   - 保存: tissue_volcano_plots.png/pdf")
    plt.close()

def create_functional_analysis():
    """创建功能分类分析图"""
    print("4. 创建功能分类分析图...")
    
    # 统计各组织的功能注释情况
    tissues = ['gill', 'intestine', 'kidney', 'leukocyte']
    tissue_names = ['Gill', 'Intestine', 'Kidney', 'Leukocyte']
    
    annotation_stats = []
    
    for tissue in tissues:
        try:
            ann_file = f"../10_Annotation/{tissue}_annotated_genes.txt"
            data = pd.read_csv(ann_file, sep='\t')
            
            total = len(data)
            has_name = (data['gene_name'] != 'Unknown').sum()
            has_go = (data['GO_terms'] != 'None').sum()
            has_kegg = (data['KEGG_ko'] != 'None').sum()
            
            annotation_stats.append({
                'Tissue': tissue.capitalize(),
                'Total': total,
                'Gene_Name': has_name,
                'GO_Terms': has_go,
                'KEGG': has_kegg
            })
        except:
            continue
    
    if annotation_stats:
        df = pd.DataFrame(annotation_stats)
        
        # 计算百分比
        df['Gene_Name_Pct'] = df['Gene_Name'] / df['Total'] * 100
        df['GO_Terms_Pct'] = df['GO_Terms'] / df['Total'] * 100
        df['KEGG_Pct'] = df['KEGG'] / df['Total'] * 100
        
        # 创建堆叠柱状图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：绝对数量
        x = np.arange(len(df))
        width = 0.25
        
        ax1.bar(x - width, df['Gene_Name'], width, label='Gene Names', color='#FF9999')
        ax1.bar(x, df['GO_Terms'], width, label='GO Terms', color='#66B2FF')
        ax1.bar(x + width, df['KEGG'], width, label='KEGG', color='#99FF99')
        
        ax1.set_xlabel('Tissue')
        ax1.set_ylabel('Number of Annotated Genes')
        ax1.set_title('Functional Annotation Coverage\n功能注释覆盖度 (绝对数量)')
        ax1.set_xticks(x)
        ax1.set_xticklabels(df['Tissue'])
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 右图：百分比
        ax2.bar(x - width, df['Gene_Name_Pct'], width, label='Gene Names', color='#FF9999')
        ax2.bar(x, df['GO_Terms_Pct'], width, label='GO Terms', color='#66B2FF')
        ax2.bar(x + width, df['KEGG_Pct'], width, label='KEGG', color='#99FF99')
        
        ax2.set_xlabel('Tissue')
        ax2.set_ylabel('Percentage of Annotated Genes (%)')
        ax2.set_title('Functional Annotation Coverage\n功能注释覆盖度 (百分比)')
        ax2.set_xticks(x)
        ax2.set_xticklabels(df['Tissue'])
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)
        
        plt.tight_layout()
        plt.savefig('../10_Annotation/functional_annotation_stats.png', dpi=300, bbox_inches='tight')
        plt.savefig('../10_Annotation/functional_annotation_stats.pdf', bbox_inches='tight')
        print("   - 保存: functional_annotation_stats.png/pdf")
        plt.close()

def main():
    print("=== 创建组织特异性分析可视化图表 ===\n")
    
    # 创建各种图表
    create_summary_barplot()
    create_expression_heatmap()
    create_volcano_plots()
    create_functional_analysis()
    
    print("\n=== 图表创建完成 ===")
    print("生成的图表文件:")
    print("1. tissue_specific_genes_summary.png/pdf - 组织特异性基因数量汇总")
    print("2. tissue_specific_heatmap.png/pdf - top基因表达热图")
    print("3. tissue_volcano_plots.png/pdf - 差异表达火山图")
    print("4. functional_annotation_stats.png/pdf - 功能注释统计")
    print("\n所有图表已保存到: ../10_Annotation/ 目录")

if __name__ == "__main__":
    main()
