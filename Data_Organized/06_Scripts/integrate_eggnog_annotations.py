#!/usr/bin/env python3
"""
整合EggNOG-mapper注释结果与表达矩阵
"""

import pandas as pd
import numpy as np

def load_eggnog_annotations():
    """加载EggNOG注释结果"""
    print("=== 加载EggNOG-mapper注释结果 ===")
    
    # 读取注释文件
    annotation_file = "../Ebu3.2_collapsedGMAP.9095_Ensembl_merge_longpep_eggnogmapper.txt.emapper.annotations.txt"
    
    # 跳过注释行，读取数据
    annotations = pd.read_csv(annotation_file, sep='\t', skiprows=4)
    
    print(f"注释文件包含 {len(annotations)} 个基因")
    
    # 显示列名
    print(f"注释列: {list(annotations.columns)}")
    
    # 统计注释情况
    print(f"\n=== 注释统计 ===")
    print(f"有基因名的: {(annotations['Preferred_name'] != '-').sum()}")
    print(f"有功能描述的: {(annotations['Description'] != '-').sum()}")
    print(f"有GO注释的: {(annotations['GOs'] != '-').sum()}")
    print(f"有KEGG注释的: {(annotations['KEGG_ko'] != '-').sum()}")
    print(f"有COG分类的: {(annotations['COG_category'] != '-').sum()}")
    
    return annotations

def create_gene_mapping():
    """创建基因ID映射关系"""
    print(f"\n=== 创建基因ID映射 ===")
    
    # 加载注释
    annotations = load_eggnog_annotations()
    
    # 创建映射字典
    gene_mapping = {}
    
    for idx, row in annotations.iterrows():
        query_id = row['#query']
        gene_name = row['Preferred_name'] if row['Preferred_name'] != '-' else ''
        description = row['Description'] if row['Description'] != '-' else ''
        
        # 创建更有意义的基因名
        if gene_name and gene_name != '-':
            meaningful_name = gene_name
        elif description and description != '-':
            # 如果没有基因名但有描述，使用描述的前几个词
            desc_words = description.split()[:3]
            meaningful_name = '_'.join(desc_words).replace(' ', '_')
        else:
            meaningful_name = query_id
        
        gene_mapping[query_id] = {
            'gene_name': gene_name,
            'description': description,
            'meaningful_name': meaningful_name,
            'go_terms': row['GOs'],
            'kegg_ko': row['KEGG_ko'],
            'cog_category': row['COG_category']
        }
    
    print(f"创建了 {len(gene_mapping)} 个基因的映射关系")
    
    return gene_mapping, annotations

def integrate_with_expression_matrices():
    """整合注释与表达矩阵"""
    print(f"\n=== 整合表达矩阵与注释 ===")
    
    # 加载基因映射
    gene_mapping, annotations = create_gene_mapping()
    
    # 加载表达矩阵
    matrices = {
        'counts': '../08_Results/hagfish_expression_matrix.txt',
        'fpkm': '../08_Results/hagfish_fpkm_matrix.txt',
        'tpm': '../08_Results/hagfish_tpm_matrix.txt'
    }
    
    for matrix_type, matrix_file in matrices.items():
        print(f"\n处理 {matrix_type.upper()} 矩阵...")
        
        # 读取表达矩阵
        df = pd.read_csv(matrix_file, sep='\t')
        print(f"原始矩阵: {df.shape[0]} 基因 × {df.shape[1]-1} 样本")
        
        # 添加注释列
        df['Gene_Name'] = ''
        df['Description'] = ''
        df['GO_Terms'] = ''
        df['KEGG_KO'] = ''
        df['COG_Category'] = ''
        
        # 匹配注释
        matched_count = 0
        for idx, row in df.iterrows():
            gene_id = row['Geneid']
            
            # 尝试直接匹配
            if gene_id in gene_mapping:
                mapping = gene_mapping[gene_id]
                df.at[idx, 'Gene_Name'] = mapping['gene_name']
                df.at[idx, 'Description'] = mapping['description']
                df.at[idx, 'GO_Terms'] = mapping['go_terms']
                df.at[idx, 'KEGG_KO'] = mapping['kegg_ko']
                df.at[idx, 'COG_Category'] = mapping['cog_category']
                matched_count += 1
            else:
                # 尝试模糊匹配（去掉后缀等）
                base_id = gene_id.split('.')[0] if '.' in gene_id else gene_id
                for annot_id in gene_mapping:
                    if base_id in annot_id or annot_id in base_id:
                        mapping = gene_mapping[annot_id]
                        df.at[idx, 'Gene_Name'] = mapping['gene_name']
                        df.at[idx, 'Description'] = mapping['description']
                        df.at[idx, 'GO_Terms'] = mapping['go_terms']
                        df.at[idx, 'KEGG_KO'] = mapping['kegg_ko']
                        df.at[idx, 'COG_Category'] = mapping['cog_category']
                        matched_count += 1
                        break
        
        print(f"成功匹配 {matched_count} 个基因的注释 ({matched_count/len(df)*100:.1f}%)")
        
        # 重新排列列顺序
        cols = ['Geneid', 'Gene_Name', 'Description', 'GO_Terms', 'KEGG_KO', 'COG_Category']
        sample_cols = [col for col in df.columns if col not in cols]
        df = df[cols + sample_cols]
        
        # 保存注释后的矩阵
        output_file = f"../08_Results/hagfish_{matrix_type}_annotated.txt"
        df.to_csv(output_file, sep='\t', index=False)
        print(f"保存到: {output_file}")
    
    return df

def create_annotation_summary():
    """创建注释摘要"""
    print(f"\n=== 创建注释摘要 ===")
    
    gene_mapping, annotations = create_gene_mapping()
    
    # 统计各类注释
    stats = {
        'total_genes': len(annotations),
        'with_gene_name': (annotations['Preferred_name'] != '-').sum(),
        'with_description': (annotations['Description'] != '-').sum(),
        'with_go': (annotations['GOs'] != '-').sum(),
        'with_kegg': (annotations['KEGG_ko'] != '-').sum(),
        'with_cog': (annotations['COG_category'] != '-').sum()
    }
    
    # 保存摘要
    summary_file = "../10_Annotation/annotation_summary.txt"
    with open(summary_file, 'w') as f:
        f.write("=== 盲鳗基因功能注释摘要 ===\n\n")
        f.write(f"总基因数: {stats['total_genes']:,}\n")
        f.write(f"有基因名的: {stats['with_gene_name']:,} ({stats['with_gene_name']/stats['total_genes']*100:.1f}%)\n")
        f.write(f"有功能描述的: {stats['with_description']:,} ({stats['with_description']/stats['total_genes']*100:.1f}%)\n")
        f.write(f"有GO注释的: {stats['with_go']:,} ({stats['with_go']/stats['total_genes']*100:.1f}%)\n")
        f.write(f"有KEGG注释的: {stats['with_kegg']:,} ({stats['with_kegg']/stats['total_genes']*100:.1f}%)\n")
        f.write(f"有COG分类的: {stats['with_cog']:,} ({stats['with_cog']/stats['total_genes']*100:.1f}%)\n")
        
        f.write(f"\n=== 前20个注释基因示例 ===\n")
        for idx, row in annotations.head(20).iterrows():
            gene_name = row['Preferred_name'] if row['Preferred_name'] != '-' else 'N/A'
            description = row['Description'] if row['Description'] != '-' else 'N/A'
            f.write(f"{row['#query']}\t{gene_name}\t{description}\n")
    
    print(f"注释摘要保存到: {summary_file}")
    
    return stats

if __name__ == "__main__":
    # 执行整合
    annotations = load_eggnog_annotations()
    integrated_df = integrate_with_expression_matrices()
    stats = create_annotation_summary()
    
    print(f"\n=== 整合完成 ===")
    print(f"现在您有了带功能注释的表达矩阵！")
    print(f"文件位置: 08_Results/hagfish_*_annotated.txt")
