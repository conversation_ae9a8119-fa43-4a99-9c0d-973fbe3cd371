#!/bin/bash

# 针对真核生物（盲鳗）的EggNOG-mapper分析
# 使用真核生物特异性参数

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper 真核生物方法 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_eukaryotic"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_eukaryotic"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 检查EggNOG-mapper可用选项 ==="

# 检查版本
echo "EggNOG-mapper版本:"
emapper.py --version

echo ""
echo "=== 运行真核生物特异性EggNOG-mapper分析 ==="

# 针对真核生物的参数:
# --tax_scope: 设置分类范围为真核生物
# --target_orthologs: 使用真核生物的直系同源基因
# --go_evidence: GO证据代码
# --pfam_realign: Pfam重新比对

emapper.py \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --tax_scope euk \
    --target_orthologs all \
    --go_evidence non-electronic \
    --pfam_realign \
    --cpu 4 \
    --override

echo "真核生物方法退出码: $?"

# 检查结果
ANNOTATION_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$ANNOTATION_FILE" ]; then
    echo "✅ 真核生物注释完成！"
    echo "注释文件: $ANNOTATION_FILE"
    
    # 统计注释结果
    TOTAL_LINES=$(wc -l < "$ANNOTATION_FILE")
    HEADER_LINES=$(grep -c "^#" "$ANNOTATION_FILE")
    ANNOTATED_SEQS=$((TOTAL_LINES - HEADER_LINES))
    
    echo "注释序列数: $ANNOTATED_SEQS"
    
    # 统计功能注释
    echo ""
    echo "=== 真核生物注释统计 ==="
    
    # 有GO注释的序列
    GO_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有GO注释的序列: $GO_COUNT"
    
    # 有KEGG注释的序列  
    KEGG_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有KEGG注释的序列: $KEGG_COUNT"
    
    # 有基因名的序列
    GENE_NAME_COUNT=$(tail -n +5 "$ANNOTATION_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    echo "有基因名的序列: $GENE_NAME_COUNT"
    
    # 与原始Diamond方法对比
    echo ""
    echo "=== 与Diamond方法对比 ==="
    
    DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
    
    if [ -f "$DIAMOND_FILE" ]; then
        # Diamond统计
        DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
        DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
        DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
        DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
        
        echo ""
        echo "方法对比:"
        echo "                Diamond    真核特异"
        echo "总注释序列:     $DIAMOND_TOTAL       $ANNOTATED_SEQS"
        echo "GO注释:         $DIAMOND_GO       $GO_COUNT"
        echo "KEGG注释:       $DIAMOND_KEGG       $KEGG_COUNT"
        echo "基因名:         $DIAMOND_GENE       $GENE_NAME_COUNT"
        
        # 计算改进百分比
        if [ $DIAMOND_TOTAL -gt 0 ]; then
            GO_IMPROVE=$(echo "scale=1; ($GO_COUNT - $DIAMOND_GO) * 100 / $DIAMOND_GO" | bc -l 2>/dev/null || echo "N/A")
            KEGG_IMPROVE=$(echo "scale=1; ($KEGG_COUNT - $DIAMOND_KEGG) * 100 / $DIAMOND_KEGG" | bc -l 2>/dev/null || echo "N/A")
            echo ""
            echo "改进情况:"
            echo "GO注释改进: $GO_IMPROVE%"
            echo "KEGG注释改进: $KEGG_IMPROVE%"
        fi
        
    else
        echo "未找到Diamond结果文件"
    fi
    
else
    echo "❌ 真核生物注释失败"
    echo "尝试简化参数..."
    
    # 简化版本
    emapper.py \
        -i "$PROTEIN_FILE" \
        --output "${OUTPUT_PREFIX}_simple" \
        --output_dir "$OUTPUT_DIR" \
        --data_dir "$DATA_DIR" \
        --tax_scope euk \
        --cpu 2 \
        --override
        
    SIMPLE_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}_simple.emapper.annotations"
    if [ -f "$SIMPLE_FILE" ]; then
        echo "✅ 简化版真核生物注释成功！"
        mv "$SIMPLE_FILE" "$ANNOTATION_FILE"
    fi
fi

echo ""
echo "完成时间: $(date)"
echo "=== 真核生物EggNOG-mapper分析完成 ==="

echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"
