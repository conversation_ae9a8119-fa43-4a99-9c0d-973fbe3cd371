#!/usr/bin/env Rscript

# 创建火山图和热图
library(ggplot2)
library(pheatmap)
library(RColorBrewer)
library(dplyr)

cat("=== 创建火山图和热图 ===\n\n")

# 创建火山图函数
create_volcano_plot <- function(tissue_name, results_file, output_prefix) {
  cat(sprintf("创建 %s 火山图...\n", tissue_name))
  
  # 读取DESeq2结果
  if(!file.exists(results_file)) {
    cat(sprintf("   - 错误: 文件不存在 %s\n", results_file))
    return(NULL)
  }
  
  res_data <- read.table(results_file, header = TRUE, sep = "\t")
  
  # 添加显著性标签
  res_data$significance <- "Not Significant"
  res_data$significance[res_data$padj < 0.05 & res_data$log2FoldChange > 1] <- "Up-regulated"
  res_data$significance[res_data$padj < 0.05 & res_data$log2FoldChange < -1] <- "Down-regulated"
  
  # 统计显著基因数
  n_up <- sum(res_data$significance == "Up-regulated", na.rm = TRUE)
  n_down <- sum(res_data$significance == "Down-regulated", na.rm = TRUE)
  n_total <- n_up + n_down
  
  cat(sprintf("   - 上调基因: %d, 下调基因: %d, 总计: %d\n", n_up, n_down, n_total))
  
  # 创建火山图
  p <- ggplot(res_data, aes(x = log2FoldChange, y = -log10(padj), color = significance)) +
    geom_point(alpha = 0.6, size = 0.8) +
    scale_color_manual(values = c("Up-regulated" = "#FF6B6B", 
                                 "Down-regulated" = "#4ECDC4", 
                                 "Not Significant" = "#CCCCCC")) +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "gray50") +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "gray50") +
    labs(title = sprintf("%s vs Others\nVolcano Plot", tissue_name),
         subtitle = sprintf("Up: %d, Down: %d, Total DEGs: %d", n_up, n_down, n_total),
         x = "Log2 Fold Change",
         y = "-Log10 (Adjusted P-value)",
         color = "Significance") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 12),
          legend.position = "bottom")
  
  # 保存图片
  ggsave(sprintf("../10_Annotation/%s_volcano_plot.png", output_prefix), 
         plot = p, width = 8, height = 6, dpi = 300)
  ggsave(sprintf("../10_Annotation/%s_volcano_plot.pdf", output_prefix), 
         plot = p, width = 8, height = 6)
  
  cat(sprintf("   - 火山图已保存: %s_volcano_plot.png/pdf\n", output_prefix))
  
  return(p)
}

# 创建表达热图函数
create_expression_heatmap <- function() {
  cat("创建表达热图...\n")
  
  # 读取表达数据
  count_data <- read.table("../08_Results/hagfish_expression_matrix.txt", 
                          header = TRUE, row.names = 1, sep = "\t")
  
  # 收集所有显著基因
  all_sig_genes <- c()
  tissues <- c("gill", "intestine", "kidney", "leukocyte")
  
  for(tissue in tissues) {
    sig_file <- sprintf("../10_Annotation/%s_significant_DEGs.txt", tissue)
    if(file.exists(sig_file)) {
      sig_data <- read.table(sig_file, header = TRUE, sep = "\t")
      if(nrow(sig_data) > 0) {
        # 取top 20基因
        top_genes <- head(sig_data[order(abs(sig_data$log2FoldChange), decreasing = TRUE), ], 20)
        all_sig_genes <- c(all_sig_genes, top_genes$gene_id)
      }
    }
  }
  
  if(length(all_sig_genes) == 0) {
    cat("   - 没有找到显著差异基因\n")
    return(NULL)
  }
  
  # 去重并筛选存在的基因
  all_sig_genes <- unique(all_sig_genes)
  all_sig_genes <- all_sig_genes[all_sig_genes %in% rownames(count_data)]
  
  if(length(all_sig_genes) == 0) {
    cat("   - 没有匹配的基因ID\n")
    return(NULL)
  }
  
  cat(sprintf("   - 选择了 %d 个显著差异基因用于热图\n", length(all_sig_genes)))
  
  # 提取表达数据并log转换
  heatmap_data <- count_data[all_sig_genes, ]
  heatmap_data_log <- log2(heatmap_data + 1)
  
  # 样本注释
  sample_annotation <- data.frame(
    Tissue = c(rep("Gill", 6), "Other", "Intestine", rep("Other", 3), 
              rep("Other", 4), "Other", "Other", "Other", "Kidney", 
              "Leukocyte", "Other", "Other"),
    row.names = colnames(heatmap_data)
  )
  
  # 颜色设置
  tissue_colors <- list(Tissue = c("Gill" = "#FF6B6B", "Intestine" = "#4ECDC4", 
                                  "Kidney" = "#45B7D1", "Leukocyte" = "#96CEB4", 
                                  "Other" = "#CCCCCC"))
  
  # 创建热图
  pheatmap(heatmap_data_log,
           annotation_col = sample_annotation,
           annotation_colors = tissue_colors,
           scale = "row",
           clustering_distance_rows = "correlation",
           clustering_distance_cols = "correlation",
           color = colorRampPalette(c("blue", "white", "red"))(100),
           show_rownames = FALSE,
           show_colnames = TRUE,
           main = "Expression Heatmap of Significant DEGs",
           filename = "../10_Annotation/expression_heatmap.png",
           width = 10, height = 8)
  
  pheatmap(heatmap_data_log,
           annotation_col = sample_annotation,
           annotation_colors = tissue_colors,
           scale = "row",
           clustering_distance_rows = "correlation",
           clustering_distance_cols = "correlation",
           color = colorRampPalette(c("blue", "white", "red"))(100),
           show_rownames = FALSE,
           show_colnames = TRUE,
           main = "Expression Heatmap of Significant DEGs",
           filename = "../10_Annotation/expression_heatmap.pdf",
           width = 10, height = 8)
  
  cat("   - 表达热图已保存: expression_heatmap.png/pdf\n")
}

# 创建汇总柱状图
create_summary_barplot <- function() {
  cat("创建汇总柱状图...\n")
  
  # 读取汇总数据
  if(!file.exists("../10_Annotation/DESeq2_analysis_summary.txt")) {
    cat("   - 汇总文件不存在\n")
    return(NULL)
  }
  
  summary_data <- read.table("../10_Annotation/DESeq2_analysis_summary.txt", 
                            header = TRUE, sep = "\t")
  
  # 创建柱状图
  p <- ggplot(summary_data, aes(x = tissue, y = significant_genes, fill = tissue)) +
    geom_bar(stat = "identity", alpha = 0.8, color = "black") +
    geom_text(aes(label = significant_genes), vjust = -0.5, size = 4, fontface = "bold") +
    scale_fill_manual(values = c("gill" = "#FF6B6B", "intestine" = "#4ECDC4", 
                                "kidney" = "#45B7D1", "leukocyte" = "#96CEB4")) +
    labs(title = "Number of Significant DEGs by Tissue",
         subtitle = "Tissue vs Others (DESeq2, padj < 0.05, |log2FC| > 1)",
         x = "Tissue",
         y = "Number of Significant DEGs") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 12),
          legend.position = "none",
          axis.text.x = element_text(angle = 45, hjust = 1))
  
  ggsave("../10_Annotation/DEG_summary_barplot.png", plot = p, width = 8, height = 6, dpi = 300)
  ggsave("../10_Annotation/DEG_summary_barplot.pdf", plot = p, width = 8, height = 6)
  
  cat("   - 汇总柱状图已保存: DEG_summary_barplot.png/pdf\n")
}

# 主函数
main <- function() {
  # 等待DESeq2结果文件
  tissues <- c("gill", "intestine", "kidney", "leukocyte")
  tissue_names <- c("Gill", "Intestine", "Kidney", "Leukocyte")
  
  # 创建火山图
  for(i in 1:length(tissues)) {
    results_file <- sprintf("../10_Annotation/%s_vs_others_DESeq2.txt", tissues[i])
    create_volcano_plot(tissue_names[i], results_file, tissues[i])
  }
  
  # 创建热图
  create_expression_heatmap()
  
  # 创建汇总图
  create_summary_barplot()
  
  cat("\n=== 所有图表创建完成 ===\n")
}

# 运行主函数
main()
