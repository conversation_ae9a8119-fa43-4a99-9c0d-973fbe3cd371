#!/usr/bin/env Rscript

# 简化版盲鳗组织特异性表达分析
cat("=== 盲鳗组织特异性表达分析 (简化版) ===\n")

# 检查并安装必要的包
required_packages <- c("DESeq2", "dplyr", "ggplot2")
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]

if(length(missing_packages) > 0) {
  cat("安装缺失的R包...\n")
  if (!requireNamespace("BiocManager", quietly = TRUE))
    install.packages("BiocManager")
  BiocManager::install(missing_packages)
}

# 加载包
suppressMessages({
  library(DESeq2)
  library(dplyr)
  library(ggplot2)
})

cat("1. 读取表达数据...\n")

# 读取表达矩阵 (Count数据)
expr_data <- read.table("../08_Results/hagfish_expression_matrix.txt", 
                       header = TRUE, row.names = 1, sep = "\t")

cat(sprintf("   - 表达矩阵: %d 基因 x %d 样本\n", nrow(expr_data), ncol(expr_data)))

# 样本信息
sample_info <- data.frame(
  sample = colnames(expr_data),
  tissue = c("gill", "gill", "gill", "gill", "gill", "gill",        # hg3L1-L6 (鳃)
            "reproductive", "intestine", "structural", "nervous",    # hg3sex, hg3intes, hg3noto, hg3axon
            "circulatory", "muscular", "digestive", "digestive",     # hg3heart, hg3muscl, hg3gall, hg3liver
            "nervous", "unknown", "integumentary", "kidney",         # hg3brain, hg3solo, hg3skin1, hg3ki
            "leukocyte", "integumentary", "reproductive"),           # hg3leu, hg3skin2, hg3ovary
  group = c(rep("gill", 6),                                         # L1-L6归为鳃组
           "other", "intestine", "other", "other",                   # 其他组织
           "other", "other", "other", "other", 
           "other", "other", "other", "kidney",                      # 肾脏
           "leukocyte", "other", "other"),                          # 白细胞
  stringsAsFactors = FALSE
)

cat("2. 样本信息:\n")
print(sample_info)

cat("\n3. 数据预处理...\n")
# 过滤低表达基因
keep_genes <- rowSums(expr_data >= 10) >= 3  # 至少3个样本中表达量>=10
expr_filtered <- expr_data[keep_genes, ]
cat(sprintf("   - 过滤后保留: %d 基因\n", nrow(expr_filtered)))

# 简化的差异表达分析函数
perform_simple_analysis <- function(target_group, group_name, expr_data, sample_info) {
  cat(sprintf("\n=== %s vs 其他组织分析 ===\n", group_name))
  
  # 创建分组信息
  sample_info$comparison <- ifelse(sample_info$group == target_group, 
                                  group_name, "Other")
  
  # 检查样本数
  target_n <- sum(sample_info$comparison == group_name)
  other_n <- sum(sample_info$comparison == "Other")
  cat(sprintf("   - %s组: %d 样本\n", group_name, target_n))
  cat(sprintf("   - 其他组: %d 样本\n", other_n))
  
  if(target_n < 2) {
    cat("   - 警告: 目标组样本数少于2，使用简化分析\n")
    
    # 对于单样本，计算fold change
    target_samples <- sample_info$sample[sample_info$comparison == group_name]
    other_samples <- sample_info$sample[sample_info$comparison == "Other"]
    
    target_mean <- rowMeans(expr_data[, target_samples, drop = FALSE])
    other_mean <- rowMeans(expr_data[, other_samples, drop = FALSE])
    
    # 添加伪计数避免除零
    log2fc <- log2((target_mean + 1) / (other_mean + 1))
    
    # 简单的差异基因筛选
    high_expr_in_target <- target_mean > 50  # 在目标组织中高表达
    fold_change_threshold <- abs(log2fc) > 2  # 4倍差异
    
    sig_genes <- which(high_expr_in_target & fold_change_threshold)
    
    result_df <- data.frame(
      gene_id = rownames(expr_data),
      target_mean = target_mean,
      other_mean = other_mean,
      log2FoldChange = log2fc,
      significant = high_expr_in_target & fold_change_threshold,
      stringsAsFactors = FALSE
    )
    
    cat(sprintf("   - 高表达差异基因: %d\n", length(sig_genes)))
    
  } else {
    # 多样本使用DESeq2
    cat("   - 使用DESeq2进行差异表达分析\n")
    
    dds <- DESeqDataSetFromMatrix(countData = round(expr_data),
                                 colData = sample_info,
                                 design = ~ comparison)
    
    # 设置参考水平
    dds$comparison <- relevel(dds$comparison, ref = "Other")
    
    # 运行DESeq2
    dds <- DESeq(dds)
    res <- results(dds, contrast = c("comparison", group_name, "Other"))
    
    # 整理结果
    result_df <- as.data.frame(res)
    result_df$gene_id <- rownames(result_df)
    result_df <- result_df[!is.na(result_df$padj), ]
    
    # 筛选显著差异基因
    sig_genes <- result_df[result_df$padj < 0.05 & abs(result_df$log2FoldChange) > 1, ]
    
    cat(sprintf("   - 显著差异基因: %d\n", nrow(sig_genes)))
  }
  
  # 保存结果
  output_file <- sprintf("../10_Annotation/%s_vs_others_results.txt", tolower(group_name))
  write.table(result_df, output_file, sep = "\t", row.names = FALSE, quote = FALSE)
  cat(sprintf("   - 结果已保存: %s\n", output_file))
  
  return(result_df)
}

# 4. 执行分析
cat("\n4. 执行组织特异性分析...\n")

# 分析目标组织
results <- list()

# 鳃 (L1-L6合并)
results$gill <- perform_simple_analysis("gill", "Gill", expr_filtered, sample_info)

# 肠道
results$intestine <- perform_simple_analysis("intestine", "Intestine", expr_filtered, sample_info)

# 肾脏
results$kidney <- perform_simple_analysis("kidney", "Kidney", expr_filtered, sample_info)

# 白细胞
results$leukocyte <- perform_simple_analysis("leukocyte", "Leukocyte", expr_filtered, sample_info)

cat("\n=== 分析完成 ===\n")
cat("结果文件已保存到 ../10_Annotation/ 目录\n")
