from pathlib import Path
from Bio import SeqIO

# 输入文件路径
transcripts_fa = Path("transcripts.fa")
cds_fa = Path("transcripts.fa.transdecoder.cds")
output_fa = cds_fa.with_name("transcripts.cds_full_length.fa")

# 读取全长转录本
transcripts_dict = SeqIO.to_dict(SeqIO.parse(str(transcripts_fa), "fasta"))

# 匹配并输出
matched_records = []
for record in SeqIO.parse(str(cds_fa), "fasta"):
    ref_id = record.id.split(".p")[0]
    if ref_id in transcripts_dict:
        full_seq = transcripts_dict[ref_id]
        full_seq.id = record.id
        full_seq.description = record.description
        matched_records.append(full_seq)

# 写出结果
SeqIO.write(matched_records, str(output_fa), "fasta")
