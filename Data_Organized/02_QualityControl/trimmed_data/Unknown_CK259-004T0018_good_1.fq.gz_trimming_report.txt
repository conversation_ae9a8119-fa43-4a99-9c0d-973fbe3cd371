
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 2). Second best hit was Nextera (count: 1)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              21,575,590
Reads with adapters:                    67,985 (0.3%)
Reads written (passing filters):    21,575,590 (100.0%)

Total basepairs processed: 3,210,381,880 bp
Quality-trimmed:               3,430,122 bp (0.1%)
Total written (filtered):  3,205,846,902 bp (99.9%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 67985 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.4%
  C: 21.5%
  G: 27.2%
  T: 26.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	40668	21069.9	0	40668
6	10511	5267.5	0	10511
7	3460	1316.9	0	3460
8	672	329.2	0	672
9	1493	82.3	0	169 1324
10	1345	20.6	1	56 1289
11	321	5.1	1	0 321
12	78	1.3	1	0 78
13	49	1.3	1	0 49
14	49	1.3	1	0 49
15	37	1.3	1	0 37
16	79	1.3	1	0 79
17	77	1.3	1	0 77
18	58	1.3	1	0 58
19	66	1.3	1	0 66
20	75	1.3	1	0 75
21	40	1.3	1	0 40
22	57	1.3	1	0 57
23	79	1.3	1	0 79
24	63	1.3	1	1 62
25	68	1.3	1	0 68
26	34	1.3	1	0 34
27	54	1.3	1	0 54
28	38	1.3	1	0 38
29	44	1.3	1	0 44
30	47	1.3	1	0 47
31	50	1.3	1	0 50
32	60	1.3	1	1 59
33	68	1.3	1	1 67
34	46	1.3	1	0 46
35	60	1.3	1	0 60
36	69	1.3	1	0 69
37	82	1.3	1	0 82
38	48	1.3	1	0 48
39	50	1.3	1	1 49
40	95	1.3	1	1 94
41	80	1.3	1	0 80
42	69	1.3	1	1 68
43	56	1.3	1	0 56
44	58	1.3	1	0 58
45	69	1.3	1	0 69
46	58	1.3	1	0 58
47	82	1.3	1	0 82
48	51	1.3	1	1 50
49	104	1.3	1	0 104
50	71	1.3	1	0 71
51	92	1.3	1	0 92
52	68	1.3	1	0 68
53	75	1.3	1	1 74
54	65	1.3	1	0 65
55	71	1.3	1	0 71
56	62	1.3	1	0 62
57	55	1.3	1	0 55
58	128	1.3	1	0 128
59	58	1.3	1	0 58
60	70	1.3	1	0 70
61	67	1.3	1	0 67
62	80	1.3	1	0 80
63	71	1.3	1	0 71
64	82	1.3	1	0 82
65	92	1.3	1	0 92
66	59	1.3	1	3 56
67	69	1.3	1	0 69
68	50	1.3	1	0 50
69	57	1.3	1	4 53
70	63	1.3	1	0 63
71	50	1.3	1	0 50
72	92	1.3	1	0 92
73	54	1.3	1	0 54
74	72	1.3	1	0 72
75	97	1.3	1	0 97
76	91	1.3	1	0 91
77	110	1.3	1	0 110
78	63	1.3	1	0 63
79	101	1.3	1	2 99
80	71	1.3	1	0 71
81	62	1.3	1	0 62
82	106	1.3	1	0 106
83	108	1.3	1	0 108
84	69	1.3	1	0 69
85	73	1.3	1	0 73
86	53	1.3	1	0 53
87	54	1.3	1	3 51
88	79	1.3	1	0 79
89	59	1.3	1	0 59
90	58	1.3	1	0 58
91	75	1.3	1	0 75
92	103	1.3	1	0 103
93	78	1.3	1	0 78
94	50	1.3	1	1 49
95	73	1.3	1	0 73
96	79	1.3	1	5 74
97	98	1.3	1	0 98
98	67	1.3	1	1 66
99	74	1.3	1	0 74
100	108	1.3	1	0 108
101	79	1.3	1	0 79
102	131	1.3	1	0 131
103	90	1.3	1	0 90
104	82	1.3	1	0 82
105	39	1.3	1	0 39
106	83	1.3	1	0 83
107	60	1.3	1	2 58
108	80	1.3	1	0 80
109	75	1.3	1	0 75
110	63	1.3	1	1 62
111	49	1.3	1	0 49
112	79	1.3	1	1 78
113	50	1.3	1	0 50
114	54	1.3	1	2 52
115	83	1.3	1	0 83
116	52	1.3	1	0 52
117	89	1.3	1	0 89
118	66	1.3	1	0 66
119	66	1.3	1	3 63
120	45	1.3	1	0 45
121	58	1.3	1	0 58
122	45	1.3	1	0 45
123	79	1.3	1	0 79
124	68	1.3	1	0 68
125	73	1.3	1	0 73
126	53	1.3	1	1 52
127	62	1.3	1	2 60
128	73	1.3	1	2 71
129	63	1.3	1	0 63
130	85	1.3	1	0 85
131	69	1.3	1	0 69
132	63	1.3	1	0 63
133	51	1.3	1	0 51
134	57	1.3	1	0 57
135	52	1.3	1	0 52
136	65	1.3	1	0 65
137	49	1.3	1	0 49
138	68	1.3	1	0 68
139	54	1.3	1	0 54
140	51	1.3	1	0 51
141	68	1.3	1	0 68
142	58	1.3	1	0 58
143	36	1.3	1	0 36
144	107	1.3	1	0 107
145	19	1.3	1	0 19
146	20	1.3	1	0 20
147	40	1.3	1	0 40
148	71	1.3	1	0 71
149	179	1.3	1	3 176
150	85	1.3	1	1 84

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz
=============================================
21575590 sequences processed in total

