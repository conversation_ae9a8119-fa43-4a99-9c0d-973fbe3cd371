
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 11). Second best hit was Illumina (count: 3)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,260,764
Reads with adapters:                    69,130 (0.3%)
Reads written (passing filters):    22,260,764 (100.0%)

Total basepairs processed: 3,312,220,319 bp
Quality-trimmed:                  41,123 bp (0.0%)
Total written (filtered):  3,311,017,354 bp (100.0%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 69130 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.3%
  C: 23.9%
  G: 24.6%
  T: 27.1%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	42822	21739.0	0	42822
6	10020	5434.8	0	10020
7	2835	1358.7	0	2835
8	569	339.7	0	569
9	1106	84.9	0	226 880
10	1214	21.2	1	27 1187
11	417	5.3	1	28 389
12	100	1.3	1	20 80
13	97	1.3	1	9 88
14	55	1.3	1	5 50
15	56	1.3	1	0 56
16	72	1.3	1	0 72
17	47	1.3	1	0 47
18	45	1.3	1	0 45
19	79	1.3	1	1 78
20	71	1.3	1	0 71
21	57	1.3	1	2 55
22	65	1.3	1	2 63
23	86	1.3	1	0 86
24	55	1.3	1	0 55
25	68	1.3	1	1 67
26	40	1.3	1	1 39
27	73	1.3	1	1 72
28	47	1.3	1	0 47
29	84	1.3	1	2 82
30	90	1.3	1	4 86
31	97	1.3	1	4 93
32	76	1.3	1	3 73
33	67	1.3	1	2 65
34	60	1.3	1	0 60
35	68	1.3	1	3 65
36	82	1.3	1	5 77
37	70	1.3	1	1 69
38	57	1.3	1	0 57
39	62	1.3	1	4 58
40	82	1.3	1	1 81
41	56	1.3	1	0 56
42	76	1.3	1	1 75
43	76	1.3	1	0 76
44	74	1.3	1	5 69
45	62	1.3	1	0 62
46	94	1.3	1	9 85
47	56	1.3	1	2 54
48	88	1.3	1	1 87
49	87	1.3	1	1 86
50	72	1.3	1	1 71
51	63	1.3	1	2 61
52	82	1.3	1	2 80
53	80	1.3	1	1 79
54	82	1.3	1	1 81
55	86	1.3	1	3 83
56	67	1.3	1	1 66
57	68	1.3	1	0 68
58	95	1.3	1	3 92
59	77	1.3	1	1 76
60	83	1.3	1	0 83
61	80	1.3	1	1 79
62	63	1.3	1	0 63
63	90	1.3	1	1 89
64	74	1.3	1	0 74
65	80	1.3	1	1 79
66	47	1.3	1	1 46
67	69	1.3	1	0 69
68	55	1.3	1	2 53
69	54	1.3	1	0 54
70	53	1.3	1	0 53
71	53	1.3	1	0 53
72	79	1.3	1	0 79
73	69	1.3	1	0 69
74	71	1.3	1	0 71
75	99	1.3	1	1 98
76	90	1.3	1	0 90
77	77	1.3	1	0 77
78	68	1.3	1	2 66
79	93	1.3	1	0 93
80	69	1.3	1	2 67
81	85	1.3	1	0 85
82	62	1.3	1	0 62
83	68	1.3	1	1 67
84	75	1.3	1	1 74
85	44	1.3	1	1 43
86	76	1.3	1	0 76
87	63	1.3	1	0 63
88	85	1.3	1	0 85
89	72	1.3	1	0 72
90	67	1.3	1	1 66
91	53	1.3	1	2 51
92	64	1.3	1	1 63
93	61	1.3	1	0 61
94	57	1.3	1	1 56
95	56	1.3	1	0 56
96	76	1.3	1	2 74
97	74	1.3	1	0 74
98	68	1.3	1	1 67
99	79	1.3	1	0 79
100	44	1.3	1	0 44
101	70	1.3	1	0 70
102	87	1.3	1	0 87
103	82	1.3	1	0 82
104	74	1.3	1	0 74
105	112	1.3	1	0 112
106	65	1.3	1	0 65
107	84	1.3	1	1 83
108	101	1.3	1	1 100
109	71	1.3	1	0 71
110	61	1.3	1	0 61
111	68	1.3	1	0 68
112	73	1.3	1	0 73
113	77	1.3	1	0 77
114	64	1.3	1	1 63
115	78	1.3	1	0 78
116	66	1.3	1	0 66
117	76	1.3	1	1 75
118	63	1.3	1	0 63
119	45	1.3	1	3 42
120	73	1.3	1	2 71
121	56	1.3	1	2 54
122	66	1.3	1	0 66
123	73	1.3	1	0 73
124	97	1.3	1	0 97
125	79	1.3	1	1 78
126	75	1.3	1	3 72
127	109	1.3	1	3 106
128	59	1.3	1	0 59
129	73	1.3	1	1 72
130	87	1.3	1	2 85
131	99	1.3	1	2 97
132	78	1.3	1	7 71
133	89	1.3	1	1 88
134	70	1.3	1	4 66
135	63	1.3	1	0 63
136	69	1.3	1	0 69
137	89	1.3	1	0 89
138	58	1.3	1	4 54
139	72	1.3	1	20 52
140	46	1.3	1	0 46
141	94	1.3	1	23 71
142	48	1.3	1	4 44
143	70	1.3	1	2 68
144	96	1.3	1	4 92
145	30	1.3	1	0 30
146	19	1.3	1	0 19
147	54	1.3	1	1 53
148	99	1.3	1	1 98
149	258	1.3	1	20 238
150	88	1.3	1	3 85

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz
=============================================
22260764 sequences processed in total

