
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Unable to auto-detect most prominent adapter from the first specified file (count Nextera: 2, count smallRNA: 2, count Illumina: 0)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior.
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,076,969
Reads with adapters:                    44,405 (0.2%)
Reads written (passing filters):    20,076,969 (100.0%)

Total basepairs processed: 2,995,126,104 bp
Quality-trimmed:                  54,298 bp (0.0%)
Total written (filtered):  2,993,949,118 bp (100.0%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 44405 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 33.4%
  C: 16.5%
  G: 22.8%
  T: 27.2%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	21681	19606.4	0	21681
6	7151	4901.6	0	7151
7	1893	1225.4	0	1893
8	569	306.4	0	569
9	261	76.6	0	70 191
10	433	19.1	1	4 429
11	157	4.8	1	0 157
12	36	1.2	1	0 36
13	56	1.2	1	0 56
14	73	1.2	1	0 73
15	83	1.2	1	0 83
16	63	1.2	1	1 62
17	394	1.2	1	1 393
18	44	1.2	1	0 44
19	134	1.2	1	0 134
20	88	1.2	1	0 88
21	49	1.2	1	0 49
22	130	1.2	1	2 128
23	62	1.2	1	0 62
24	459	1.2	1	1 458
25	179	1.2	1	0 179
26	83	1.2	1	0 83
27	60	1.2	1	0 60
28	48	1.2	1	0 48
29	227	1.2	1	0 227
30	87	1.2	1	1 86
31	83	1.2	1	0 83
32	37	1.2	1	0 37
33	51	1.2	1	0 51
34	127	1.2	1	0 127
35	89	1.2	1	0 89
36	67	1.2	1	0 67
37	57	1.2	1	2 55
38	72	1.2	1	0 72
39	91	1.2	1	0 91
40	42	1.2	1	0 42
41	43	1.2	1	0 43
42	35	1.2	1	0 35
43	27	1.2	1	0 27
44	97	1.2	1	0 97
45	171	1.2	1	0 171
46	134	1.2	1	1 133
47	29	1.2	1	0 29
48	28	1.2	1	0 28
49	36	1.2	1	0 36
50	23	1.2	1	0 23
51	83	1.2	1	0 83
52	49	1.2	1	0 49
53	231	1.2	1	0 231
54	29	1.2	1	0 29
55	83	1.2	1	0 83
56	22	1.2	1	0 22
57	32	1.2	1	0 32
58	25	1.2	1	0 25
59	46	1.2	1	1 45
60	84	1.2	1	0 84
61	43	1.2	1	0 43
62	91	1.2	1	2 89
63	60	1.2	1	0 60
64	93	1.2	1	0 93
65	90	1.2	1	0 90
66	42	1.2	1	0 42
67	64	1.2	1	0 64
68	49	1.2	1	0 49
69	90	1.2	1	0 90
70	69	1.2	1	0 69
71	173	1.2	1	0 173
72	141	1.2	1	0 141
73	109	1.2	1	0 109
74	73	1.2	1	0 73
75	100	1.2	1	0 100
76	270	1.2	1	0 270
77	91	1.2	1	0 91
78	81	1.2	1	0 81
79	39	1.2	1	1 38
80	437	1.2	1	0 437
81	94	1.2	1	0 94
82	261	1.2	1	0 261
83	96	1.2	1	0 96
84	59	1.2	1	0 59
85	119	1.2	1	0 119
86	97	1.2	1	0 97
87	262	1.2	1	1 261
88	149	1.2	1	0 149
89	89	1.2	1	0 89
90	75	1.2	1	0 75
91	46	1.2	1	0 46
92	132	1.2	1	0 132
93	163	1.2	1	1 162
94	89	1.2	1	0 89
95	52	1.2	1	1 51
96	42	1.2	1	0 42
97	50	1.2	1	0 50
98	95	1.2	1	0 95
99	90	1.2	1	0 90
100	72	1.2	1	0 72
101	51	1.2	1	0 51
102	117	1.2	1	0 117
103	36	1.2	1	0 36
104	68	1.2	1	0 68
105	49	1.2	1	0 49
106	52	1.2	1	0 52
107	94	1.2	1	0 94
108	73	1.2	1	0 73
109	38	1.2	1	0 38
110	67	1.2	1	0 67
111	42	1.2	1	3 39
112	46	1.2	1	0 46
113	20	1.2	1	0 20
114	63	1.2	1	0 63
115	23	1.2	1	0 23
116	21	1.2	1	0 21
117	65	1.2	1	0 65
118	75	1.2	1	0 75
119	44	1.2	1	0 44
120	23	1.2	1	0 23
121	15	1.2	1	0 15
122	26	1.2	1	0 26
123	44	1.2	1	0 44
124	70	1.2	1	0 70
125	38	1.2	1	4 34
126	100	1.2	1	0 100
127	248	1.2	1	0 248
128	89	1.2	1	0 89
129	51	1.2	1	3 48
130	83	1.2	1	0 83
131	70	1.2	1	0 70
132	52	1.2	1	0 52
133	58	1.2	1	1 57
134	222	1.2	1	1 221
135	272	1.2	1	0 272
136	64	1.2	1	0 64
137	48	1.2	1	0 48
138	41	1.2	1	0 41
139	111	1.2	1	1 110
140	43	1.2	1	0 43
141	29	1.2	1	0 29
142	31	1.2	1	0 31
143	193	1.2	1	0 193
144	187	1.2	1	0 187
145	35	1.2	1	0 35
146	36	1.2	1	0 36
147	54	1.2	1	0 54
148	43	1.2	1	0 43
149	84	1.2	1	0 84
150	41	1.2	1	0 41

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz
=============================================
20076969 sequences processed in total

