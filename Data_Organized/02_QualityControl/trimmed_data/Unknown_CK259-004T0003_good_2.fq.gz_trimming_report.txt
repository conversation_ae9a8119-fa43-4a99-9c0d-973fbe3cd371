
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 2, count Nextera: 2, count Illumina: 1)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior.
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,435,036
Reads with adapters:                    35,281 (0.2%)
Reads written (passing filters):    20,435,036 (100.0%)

Total basepairs processed: 3,043,932,072 bp
Quality-trimmed:               1,952,730 bp (0.1%)
Total written (filtered):  3,041,247,968 bp (99.9%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 35281 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 20.0%
  C: 24.0%
  G: 25.8%
  T: 30.1%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	19556	19956.1	0	19556
6	5365	4989.0	0	5365
7	1654	1247.3	0	1654
8	639	311.8	0	639
9	375	78.0	0	93 282
10	442	19.5	1	9 433
11	216	4.9	1	2 214
12	64	1.2	1	0 64
13	53	1.2	1	0 53
14	58	1.2	1	1 57
15	43	1.2	1	0 43
16	34	1.2	1	0 34
17	53	1.2	1	0 53
18	31	1.2	1	0 31
19	44	1.2	1	0 44
20	38	1.2	1	0 38
21	33	1.2	1	0 33
22	41	1.2	1	0 41
23	40	1.2	1	0 40
24	32	1.2	1	0 32
25	57	1.2	1	1 56
26	42	1.2	1	1 41
27	46	1.2	1	0 46
28	39	1.2	1	0 39
29	36	1.2	1	0 36
30	54	1.2	1	0 54
31	64	1.2	1	0 64
32	36	1.2	1	1 35
33	54	1.2	1	1 53
34	75	1.2	1	0 75
35	57	1.2	1	0 57
36	37	1.2	1	0 37
37	38	1.2	1	0 38
38	27	1.2	1	0 27
39	41	1.2	1	2 39
40	41	1.2	1	0 41
41	37	1.2	1	1 36
42	42	1.2	1	0 42
43	25	1.2	1	0 25
44	31	1.2	1	0 31
45	99	1.2	1	0 99
46	75	1.2	1	1 74
47	41	1.2	1	0 41
48	47	1.2	1	0 47
49	51	1.2	1	0 51
50	38	1.2	1	0 38
51	39	1.2	1	1 38
52	54	1.2	1	0 54
53	103	1.2	1	0 103
54	44	1.2	1	0 44
55	86	1.2	1	1 85
56	39	1.2	1	0 39
57	43	1.2	1	0 43
58	30	1.2	1	0 30
59	28	1.2	1	0 28
60	34	1.2	1	0 34
61	47	1.2	1	1 46
62	47	1.2	1	0 47
63	66	1.2	1	0 66
64	70	1.2	1	0 70
65	46	1.2	1	0 46
66	38	1.2	1	0 38
67	61	1.2	1	0 61
68	47	1.2	1	0 47
69	48	1.2	1	0 48
70	57	1.2	1	0 57
71	44	1.2	1	1 43
72	45	1.2	1	0 45
73	58	1.2	1	0 58
74	60	1.2	1	0 60
75	79	1.2	1	1 78
76	93	1.2	1	0 93
77	55	1.2	1	0 55
78	101	1.2	1	0 101
79	42	1.2	1	0 42
80	78	1.2	1	2 76
81	107	1.2	1	0 107
82	67	1.2	1	2 65
83	50	1.2	1	0 50
84	52	1.2	1	2 50
85	37	1.2	1	0 37
86	86	1.2	1	0 86
87	56	1.2	1	0 56
88	30	1.2	1	0 30
89	40	1.2	1	0 40
90	53	1.2	1	0 53
91	53	1.2	1	0 53
92	51	1.2	1	0 51
93	102	1.2	1	1 101
94	46	1.2	1	0 46
95	61	1.2	1	0 61
96	45	1.2	1	0 45
97	69	1.2	1	0 69
98	40	1.2	1	0 40
99	42	1.2	1	0 42
100	38	1.2	1	0 38
101	42	1.2	1	0 42
102	59	1.2	1	0 59
103	34	1.2	1	0 34
104	46	1.2	1	0 46
105	29	1.2	1	0 29
106	52	1.2	1	1 51
107	77	1.2	1	0 77
108	41	1.2	1	1 40
109	34	1.2	1	0 34
110	50	1.2	1	0 50
111	31	1.2	1	0 31
112	61	1.2	1	0 61
113	26	1.2	1	0 26
114	62	1.2	1	0 62
115	29	1.2	1	1 28
116	26	1.2	1	0 26
117	43	1.2	1	0 43
118	73	1.2	1	0 73
119	52	1.2	1	0 52
120	70	1.2	1	2 68
121	44	1.2	1	0 44
122	54	1.2	1	0 54
123	58	1.2	1	0 58
124	60	1.2	1	0 60
125	38	1.2	1	0 38
126	47	1.2	1	0 47
127	66	1.2	1	0 66
128	69	1.2	1	0 69
129	44	1.2	1	0 44
130	47	1.2	1	0 47
131	35	1.2	1	0 35
132	43	1.2	1	0 43
133	63	1.2	1	0 63
134	58	1.2	1	0 58
135	78	1.2	1	0 78
136	48	1.2	1	1 47
137	50	1.2	1	1 49
138	35	1.2	1	0 35
139	39	1.2	1	0 39
140	60	1.2	1	0 60
141	42	1.2	1	0 42
142	55	1.2	1	0 55
143	74	1.2	1	0 74
144	29	1.2	1	0 29
145	54	1.2	1	0 54
146	24	1.2	1	0 24
147	39	1.2	1	0 39
148	23	1.2	1	0 23
149	76	1.2	1	0 76
150	44	1.2	1	0 44

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz
=============================================
20435036 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3731 (0.02%)
Total number of sequences analysed for the sequence pair length validation: 20435036

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 14889 (0.07%)
