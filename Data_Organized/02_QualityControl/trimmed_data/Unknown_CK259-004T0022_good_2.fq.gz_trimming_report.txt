
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Unable to auto-detect most prominent adapter from the first specified file (count Nextera: 2, count smallRNA: 2, count Illumina: 1)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior.
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,040,680
Reads with adapters:                    41,633 (0.2%)
Reads written (passing filters):    20,040,680 (100.0%)

Total basepairs processed: 2,988,780,931 bp
Quality-trimmed:              13,518,529 bp (0.5%)
Total written (filtered):  2,973,955,034 bp (99.5%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41633 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 45.0%
  C: 17.3%
  G: 17.9%
  T: 19.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	17760	19571.0	0	17760
6	5308	4892.7	0	5308
7	1936	1223.2	0	1936
8	712	305.8	0	712
9	399	76.4	0	90 309
10	431	19.1	1	3 428
11	125	4.8	1	0 125
12	105	1.2	1	0 105
13	77	1.2	1	0 77
14	91	1.2	1	0 91
15	76	1.2	1	0 76
16	78	1.2	1	0 78
17	357	1.2	1	0 357
18	82	1.2	1	0 82
19	104	1.2	1	0 104
20	63	1.2	1	0 63
21	64	1.2	1	0 64
22	154	1.2	1	0 154
23	135	1.2	1	0 135
24	535	1.2	1	1 534
25	227	1.2	1	0 227
26	155	1.2	1	0 155
27	74	1.2	1	0 74
28	81	1.2	1	0 81
29	298	1.2	1	2 296
30	94	1.2	1	0 94
31	40	1.2	1	0 40
32	41	1.2	1	0 41
33	44	1.2	1	0 44
34	115	1.2	1	0 115
35	165	1.2	1	0 165
36	80	1.2	1	0 80
37	53	1.2	1	0 53
38	137	1.2	1	0 137
39	73	1.2	1	0 73
40	57	1.2	1	0 57
41	50	1.2	1	0 50
42	41	1.2	1	0 41
43	51	1.2	1	1 50
44	188	1.2	1	0 188
45	228	1.2	1	0 228
46	130	1.2	1	0 130
47	41	1.2	1	0 41
48	15	1.2	1	0 15
49	43	1.2	1	0 43
50	33	1.2	1	0 33
51	132	1.2	1	0 132
52	87	1.2	1	0 87
53	177	1.2	1	0 177
54	44	1.2	1	0 44
55	78	1.2	1	0 78
56	29	1.2	1	0 29
57	22	1.2	1	0 22
58	12	1.2	1	0 12
59	39	1.2	1	0 39
60	193	1.2	1	0 193
61	92	1.2	1	0 92
62	163	1.2	1	0 163
63	93	1.2	1	0 93
64	140	1.2	1	1 139
65	71	1.2	1	0 71
66	62	1.2	1	0 62
67	80	1.2	1	0 80
68	75	1.2	1	0 75
69	93	1.2	1	0 93
70	104	1.2	1	0 104
71	195	1.2	1	0 195
72	93	1.2	1	0 93
73	176	1.2	1	0 176
74	89	1.2	1	0 89
75	90	1.2	1	0 90
76	276	1.2	1	0 276
77	147	1.2	1	0 147
78	103	1.2	1	0 103
79	109	1.2	1	0 109
80	695	1.2	1	0 695
81	144	1.2	1	4 140
82	174	1.2	1	0 174
83	189	1.2	1	0 189
84	31	1.2	1	0 31
85	94	1.2	1	0 94
86	119	1.2	1	0 119
87	368	1.2	1	0 368
88	142	1.2	1	0 142
89	83	1.2	1	0 83
90	107	1.2	1	0 107
91	68	1.2	1	0 68
92	132	1.2	1	0 132
93	209	1.2	1	0 209
94	70	1.2	1	0 70
95	46	1.2	1	0 46
96	80	1.2	1	0 80
97	143	1.2	1	0 143
98	39	1.2	1	0 39
99	37	1.2	1	0 37
100	69	1.2	1	0 69
101	89	1.2	1	0 89
102	132	1.2	1	0 132
103	34	1.2	1	0 34
104	68	1.2	1	0 68
105	70	1.2	1	0 70
106	45	1.2	1	0 45
107	92	1.2	1	0 92
108	81	1.2	1	0 81
109	31	1.2	1	0 31
110	58	1.2	1	0 58
111	21	1.2	1	0 21
112	87	1.2	1	0 87
113	15	1.2	1	3 12
114	43	1.2	1	0 43
115	37	1.2	1	0 37
116	35	1.2	1	0 35
117	146	1.2	1	0 146
118	131	1.2	1	0 131
119	35	1.2	1	0 35
120	17	1.2	1	0 17
121	22	1.2	1	0 22
122	31	1.2	1	0 31
123	30	1.2	1	0 30
124	92	1.2	1	0 92
125	131	1.2	1	0 131
126	136	1.2	1	0 136
127	352	1.2	1	9 343
128	78	1.2	1	0 78
129	51	1.2	1	0 51
130	81	1.2	1	0 81
131	43	1.2	1	0 43
132	35	1.2	1	5 30
133	79	1.2	1	0 79
134	301	1.2	1	0 301
135	298	1.2	1	1 297
136	85	1.2	1	0 85
137	49	1.2	1	0 49
138	29	1.2	1	0 29
139	106	1.2	1	0 106
140	48	1.2	1	0 48
141	77	1.2	1	0 77
142	77	1.2	1	0 77
143	402	1.2	1	1 401
144	122	1.2	1	0 122
145	29	1.2	1	0 29
146	38	1.2	1	0 38
147	59	1.2	1	0 59
148	24	1.2	1	0 24
149	173	1.2	1	0 173
150	39	1.2	1	0 39

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz
=============================================
20040680 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 21458 (0.11%)
Total number of sequences analysed for the sequence pair length validation: 20040680

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 95251 (0.48%)
