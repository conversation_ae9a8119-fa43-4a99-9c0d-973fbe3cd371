
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 3). Second best hit was Nextera (count: 0)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,354,161
Reads with adapters:                    56,515 (0.3%)
Reads written (passing filters):    20,354,161 (100.0%)

Total basepairs processed: 3,037,532,327 bp
Quality-trimmed:                  50,592 bp (0.0%)
Total written (filtered):  3,036,875,528 bp (100.0%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 56515 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.5%
  C: 30.2%
  G: 22.7%
  T: 23.5%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	37422	19877.1	0	37422
6	8440	4969.3	0	8440
7	2962	1242.3	0	2962
8	416	310.6	0	416
9	1322	77.6	0	261 1061
10	1905	19.4	1	29 1876
11	209	4.9	1	1 208
12	28	1.2	1	0 28
13	25	1.2	1	0 25
14	27	1.2	1	0 27
15	17	1.2	1	0 17
16	16	1.2	1	0 16
17	21	1.2	1	1 20
18	30	1.2	1	0 30
19	24	1.2	1	0 24
20	29	1.2	1	0 29
21	23	1.2	1	0 23
22	25	1.2	1	0 25
23	40	1.2	1	0 40
24	41	1.2	1	0 41
25	24	1.2	1	0 24
26	17	1.2	1	0 17
27	34	1.2	1	0 34
28	20	1.2	1	0 20
29	24	1.2	1	0 24
30	20	1.2	1	0 20
31	27	1.2	1	0 27
32	32	1.2	1	0 32
33	41	1.2	1	0 41
34	25	1.2	1	0 25
35	36	1.2	1	0 36
36	24	1.2	1	1 23
37	44	1.2	1	2 42
38	17	1.2	1	1 16
39	39	1.2	1	0 39
40	29	1.2	1	0 29
41	19	1.2	1	0 19
42	40	1.2	1	0 40
43	21	1.2	1	0 21
44	24	1.2	1	0 24
45	24	1.2	1	0 24
46	20	1.2	1	0 20
47	23	1.2	1	0 23
48	18	1.2	1	0 18
49	47	1.2	1	0 47
50	39	1.2	1	0 39
51	16	1.2	1	0 16
52	17	1.2	1	0 17
53	38	1.2	1	0 38
54	30	1.2	1	0 30
55	45	1.2	1	0 45
56	35	1.2	1	0 35
57	17	1.2	1	0 17
58	41	1.2	1	0 41
59	27	1.2	1	0 27
60	37	1.2	1	3 34
61	24	1.2	1	0 24
62	37	1.2	1	0 37
63	44	1.2	1	1 43
64	26	1.2	1	0 26
65	32	1.2	1	0 32
66	19	1.2	1	0 19
67	22	1.2	1	0 22
68	14	1.2	1	0 14
69	36	1.2	1	0 36
70	23	1.2	1	0 23
71	19	1.2	1	0 19
72	24	1.2	1	1 23
73	37	1.2	1	0 37
74	25	1.2	1	0 25
75	52	1.2	1	0 52
76	32	1.2	1	0 32
77	19	1.2	1	0 19
78	29	1.2	1	1 28
79	32	1.2	1	0 32
80	18	1.2	1	0 18
81	31	1.2	1	0 31
82	20	1.2	1	0 20
83	38	1.2	1	0 38
84	19	1.2	1	0 19
85	23	1.2	1	0 23
86	26	1.2	1	0 26
87	31	1.2	1	0 31
88	22	1.2	1	0 22
89	30	1.2	1	0 30
90	29	1.2	1	0 29
91	24	1.2	1	0 24
92	32	1.2	1	0 32
93	34	1.2	1	0 34
94	20	1.2	1	0 20
95	26	1.2	1	0 26
96	23	1.2	1	0 23
97	33	1.2	1	0 33
98	34	1.2	1	0 34
99	27	1.2	1	0 27
100	33	1.2	1	0 33
101	21	1.2	1	0 21
102	30	1.2	1	0 30
103	24	1.2	1	0 24
104	28	1.2	1	0 28
105	17	1.2	1	0 17
106	29	1.2	1	0 29
107	26	1.2	1	0 26
108	25	1.2	1	0 25
109	27	1.2	1	0 27
110	25	1.2	1	0 25
111	30	1.2	1	0 30
112	26	1.2	1	0 26
113	20	1.2	1	0 20
114	32	1.2	1	0 32
115	11	1.2	1	0 11
116	31	1.2	1	0 31
117	30	1.2	1	0 30
118	31	1.2	1	0 31
119	20	1.2	1	0 20
120	27	1.2	1	0 27
121	40	1.2	1	2 38
122	27	1.2	1	0 27
123	33	1.2	1	0 33
124	30	1.2	1	2 28
125	28	1.2	1	0 28
126	15	1.2	1	0 15
127	18	1.2	1	0 18
128	17	1.2	1	0 17
129	38	1.2	1	0 38
130	24	1.2	1	0 24
131	22	1.2	1	0 22
132	22	1.2	1	3 19
133	31	1.2	1	0 31
134	18	1.2	1	1 17
135	14	1.2	1	0 14
136	25	1.2	1	0 25
137	18	1.2	1	0 18
138	27	1.2	1	0 27
139	16	1.2	1	0 16
140	9	1.2	1	0 9
141	35	1.2	1	2 33
142	30	1.2	1	2 28
143	25	1.2	1	0 25
144	65	1.2	1	0 65
145	9	1.2	1	0 9
146	13	1.2	1	0 13
147	33	1.2	1	0 33
148	28	1.2	1	0 28
149	86	1.2	1	1 85
150	37	1.2	1	1 36

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz
=============================================
20354161 sequences processed in total

