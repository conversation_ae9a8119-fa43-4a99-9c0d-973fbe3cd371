
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 2). Second best hit was Illumina (count: 1)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,366,828
Reads with adapters:                    63,044 (0.3%)
Reads written (passing filters):    19,366,828 (100.0%)

Total basepairs processed: 2,883,179,601 bp
Quality-trimmed:               2,052,873 bp (0.1%)
Total written (filtered):  2,880,092,022 bp (99.9%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 63044 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.7%
  C: 23.3%
  G: 24.9%
  T: 26.9%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	38190	18912.9	0	38190
6	9847	4728.2	0	9847
7	2886	1182.1	0	2886
8	548	295.5	0	548
9	1079	73.9	0	195 884
10	1270	18.5	1	27 1243
11	356	4.6	1	6 350
12	61	1.2	1	0 61
13	54	1.2	1	0 54
14	25	1.2	1	0 25
15	55	1.2	1	0 55
16	67	1.2	1	0 67
17	56	1.2	1	1 55
18	58	1.2	1	0 58
19	93	1.2	1	0 93
20	67	1.2	1	0 67
21	43	1.2	1	0 43
22	42	1.2	1	0 42
23	56	1.2	1	0 56
24	70	1.2	1	0 70
25	72	1.2	1	0 72
26	43	1.2	1	2 41
27	55	1.2	1	1 54
28	29	1.2	1	0 29
29	69	1.2	1	1 68
30	73	1.2	1	0 73
31	61	1.2	1	0 61
32	68	1.2	1	0 68
33	76	1.2	1	0 76
34	67	1.2	1	0 67
35	64	1.2	1	0 64
36	54	1.2	1	4 50
37	58	1.2	1	0 58
38	46	1.2	1	1 45
39	72	1.2	1	0 72
40	100	1.2	1	0 100
41	54	1.2	1	0 54
42	43	1.2	1	0 43
43	70	1.2	1	0 70
44	48	1.2	1	0 48
45	53	1.2	1	0 53
46	76	1.2	1	0 76
47	63	1.2	1	0 63
48	56	1.2	1	0 56
49	56	1.2	1	0 56
50	58	1.2	1	0 58
51	45	1.2	1	0 45
52	49	1.2	1	0 49
53	72	1.2	1	0 72
54	74	1.2	1	0 74
55	85	1.2	1	1 84
56	61	1.2	1	0 61
57	61	1.2	1	0 61
58	113	1.2	1	0 113
59	70	1.2	1	0 70
60	58	1.2	1	0 58
61	59	1.2	1	0 59
62	51	1.2	1	0 51
63	36	1.2	1	0 36
64	77	1.2	1	0 77
65	70	1.2	1	0 70
66	52	1.2	1	0 52
67	47	1.2	1	0 47
68	40	1.2	1	0 40
69	68	1.2	1	0 68
70	48	1.2	1	0 48
71	37	1.2	1	0 37
72	79	1.2	1	0 79
73	68	1.2	1	0 68
74	71	1.2	1	0 71
75	66	1.2	1	0 66
76	66	1.2	1	0 66
77	61	1.2	1	0 61
78	69	1.2	1	2 67
79	78	1.2	1	0 78
80	83	1.2	1	0 83
81	71	1.2	1	1 70
82	75	1.2	1	0 75
83	71	1.2	1	1 70
84	79	1.2	1	0 79
85	49	1.2	1	0 49
86	50	1.2	1	1 49
87	60	1.2	1	1 59
88	84	1.2	1	0 84
89	88	1.2	1	2 86
90	63	1.2	1	0 63
91	58	1.2	1	0 58
92	63	1.2	1	1 62
93	86	1.2	1	0 86
94	63	1.2	1	0 63
95	45	1.2	1	0 45
96	37	1.2	1	0 37
97	76	1.2	1	0 76
98	43	1.2	1	1 42
99	86	1.2	1	0 86
100	71	1.2	1	0 71
101	65	1.2	1	0 65
102	59	1.2	1	0 59
103	79	1.2	1	0 79
104	57	1.2	1	0 57
105	55	1.2	1	0 55
106	61	1.2	1	0 61
107	75	1.2	1	0 75
108	131	1.2	1	0 131
109	55	1.2	1	0 55
110	90	1.2	1	0 90
111	47	1.2	1	0 47
112	83	1.2	1	0 83
113	66	1.2	1	0 66
114	50	1.2	1	0 50
115	41	1.2	1	0 41
116	76	1.2	1	0 76
117	85	1.2	1	1 84
118	59	1.2	1	0 59
119	51	1.2	1	0 51
120	47	1.2	1	0 47
121	51	1.2	1	0 51
122	70	1.2	1	1 69
123	53	1.2	1	0 53
124	78	1.2	1	0 78
125	76	1.2	1	1 75
126	64	1.2	1	0 64
127	58	1.2	1	0 58
128	34	1.2	1	0 34
129	84	1.2	1	1 83
130	68	1.2	1	1 67
131	88	1.2	1	0 88
132	63	1.2	1	0 63
133	45	1.2	1	1 44
134	50	1.2	1	0 50
135	63	1.2	1	0 63
136	62	1.2	1	0 62
137	59	1.2	1	0 59
138	50	1.2	1	0 50
139	41	1.2	1	0 41
140	51	1.2	1	0 51
141	87	1.2	1	0 87
142	44	1.2	1	0 44
143	50	1.2	1	0 50
144	69	1.2	1	0 69
145	15	1.2	1	0 15
146	20	1.2	1	0 20
147	37	1.2	1	0 37
148	93	1.2	1	0 93
149	258	1.2	1	0 258
150	92	1.2	1	0 92

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz
=============================================
19366828 sequences processed in total

