
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 5). Second best hit was Nextera (count: 2)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,917,243
Reads with adapters:                    60,606 (0.3%)
Reads written (passing filters):    19,917,243 (100.0%)

Total basepairs processed: 2,932,683,807 bp
Quality-trimmed:               4,577,674 bp (0.2%)
Total written (filtered):  2,927,072,056 bp (99.8%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 60606 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.6%
  C: 22.4%
  G: 25.1%
  T: 27.6%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	36746	19450.4	0	36746
6	9158	4862.6	0	9158
7	2501	1215.7	0	2501
8	426	303.9	0	426
9	1098	76.0	0	157 941
10	1082	19.0	1	49 1033
11	400	4.7	1	5 395
12	125	1.2	1	1 124
13	61	1.2	1	0 61
14	37	1.2	1	0 37
15	43	1.2	1	0 43
16	60	1.2	1	0 60
17	65	1.2	1	0 65
18	36	1.2	1	1 35
19	118	1.2	1	0 118
20	41	1.2	1	1 40
21	38	1.2	1	0 38
22	30	1.2	1	0 30
23	73	1.2	1	0 73
24	70	1.2	1	2 68
25	66	1.2	1	0 66
26	37	1.2	1	0 37
27	97	1.2	1	0 97
28	61	1.2	1	0 61
29	52	1.2	1	0 52
30	70	1.2	1	0 70
31	72	1.2	1	0 72
32	98	1.2	1	0 98
33	131	1.2	1	9 122
34	45	1.2	1	4 41
35	94	1.2	1	0 94
36	64	1.2	1	0 64
37	54	1.2	1	1 53
38	58	1.2	1	1 57
39	64	1.2	1	0 64
40	51	1.2	1	0 51
41	50	1.2	1	1 49
42	82	1.2	1	2 80
43	55	1.2	1	11 44
44	67	1.2	1	0 67
45	47	1.2	1	0 47
46	61	1.2	1	0 61
47	86	1.2	1	0 86
48	78	1.2	1	0 78
49	124	1.2	1	0 124
50	42	1.2	1	0 42
51	70	1.2	1	0 70
52	83	1.2	1	0 83
53	56	1.2	1	1 55
54	51	1.2	1	6 45
55	87	1.2	1	0 87
56	52	1.2	1	1 51
57	44	1.2	1	0 44
58	98	1.2	1	0 98
59	82	1.2	1	0 82
60	68	1.2	1	0 68
61	55	1.2	1	0 55
62	62	1.2	1	0 62
63	57	1.2	1	0 57
64	42	1.2	1	1 41
65	55	1.2	1	0 55
66	31	1.2	1	0 31
67	71	1.2	1	0 71
68	42	1.2	1	0 42
69	114	1.2	1	0 114
70	60	1.2	1	0 60
71	45	1.2	1	0 45
72	77	1.2	1	0 77
73	41	1.2	1	0 41
74	59	1.2	1	0 59
75	45	1.2	1	0 45
76	93	1.2	1	0 93
77	33	1.2	1	0 33
78	55	1.2	1	0 55
79	58	1.2	1	0 58
80	88	1.2	1	0 88
81	55	1.2	1	0 55
82	52	1.2	1	0 52
83	51	1.2	1	0 51
84	64	1.2	1	0 64
85	58	1.2	1	0 58
86	64	1.2	1	0 64
87	112	1.2	1	12 100
88	54	1.2	1	0 54
89	78	1.2	1	0 78
90	49	1.2	1	0 49
91	42	1.2	1	0 42
92	108	1.2	1	21 87
93	87	1.2	1	0 87
94	50	1.2	1	0 50
95	82	1.2	1	0 82
96	78	1.2	1	0 78
97	71	1.2	1	0 71
98	79	1.2	1	0 79
99	65	1.2	1	0 65
100	100	1.2	1	0 100
101	43	1.2	1	0 43
102	90	1.2	1	0 90
103	68	1.2	1	0 68
104	74	1.2	1	0 74
105	49	1.2	1	0 49
106	57	1.2	1	0 57
107	38	1.2	1	0 38
108	74	1.2	1	0 74
109	93	1.2	1	0 93
110	59	1.2	1	0 59
111	29	1.2	1	0 29
112	91	1.2	1	0 91
113	79	1.2	1	0 79
114	38	1.2	1	0 38
115	49	1.2	1	0 49
116	101	1.2	1	0 101
117	62	1.2	1	1 61
118	68	1.2	1	0 68
119	71	1.2	1	0 71
120	64	1.2	1	0 64
121	52	1.2	1	0 52
122	61	1.2	1	0 61
123	81	1.2	1	0 81
124	59	1.2	1	0 59
125	81	1.2	1	3 78
126	33	1.2	1	0 33
127	52	1.2	1	0 52
128	79	1.2	1	0 79
129	99	1.2	1	0 99
130	102	1.2	1	0 102
131	65	1.2	1	0 65
132	68	1.2	1	7 61
133	74	1.2	1	0 74
134	86	1.2	1	0 86
135	70	1.2	1	0 70
136	55	1.2	1	0 55
137	60	1.2	1	0 60
138	43	1.2	1	0 43
139	75	1.2	1	0 75
140	66	1.2	1	0 66
141	99	1.2	1	0 99
142	63	1.2	1	0 63
143	38	1.2	1	0 38
144	50	1.2	1	0 50
145	7	1.2	1	0 7
146	17	1.2	1	0 17
147	25	1.2	1	1 24
148	77	1.2	1	0 77
149	173	1.2	1	3 170
150	112	1.2	1	0 112

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz
=============================================
19917243 sequences processed in total

