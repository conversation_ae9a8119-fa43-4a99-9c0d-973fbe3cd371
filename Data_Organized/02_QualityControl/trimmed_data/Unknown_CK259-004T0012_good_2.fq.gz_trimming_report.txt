
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 3). Second best hit was Nextera (count: 0)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,354,161
Reads with adapters:                    14,153 (0.1%)
Reads written (passing filters):    20,354,161 (100.0%)

Total basepairs processed: 3,037,494,842 bp
Quality-trimmed:                 152,946 bp (0.0%)
Total written (filtered):  3,037,105,733 bp (100.0%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 14153 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 17.8%
  C: 12.0%
  G: 25.8%
  T: 44.4%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
5	6695	19877.1	0	6695
6	3754	4969.3	0	3754
7	762	1242.3	0	762
8	344	310.6	0	344
9	153	77.6	0	9 144
10	229	19.4	1	0 229
11	50	4.9	1	0 50
12	21	1.2	1	6 15
13	15	1.2	1	0 15
14	12	1.2	1	0 12
15	12	1.2	1	0 12
16	26	1.2	1	0 26
17	22	1.2	1	3 19
18	12	1.2	1	3 9
19	21	1.2	1	5 16
20	17	1.2	1	1 16
21	8	1.2	1	1 7
22	16	1.2	1	2 14
23	31	1.2	1	1 30
24	20	1.2	1	0 20
25	16	1.2	1	0 16
26	15	1.2	1	0 15
27	18	1.2	1	2 16
28	10	1.2	1	0 10
29	16	1.2	1	1 15
30	16	1.2	1	0 16
31	8	1.2	1	0 8
32	18	1.2	1	0 18
33	18	1.2	1	6 12
34	11	1.2	1	2 9
35	21	1.2	1	0 21
36	17	1.2	1	2 15
37	14	1.2	1	1 13
38	17	1.2	1	0 17
39	36	1.2	1	3 33
40	10	1.2	1	0 10
41	18	1.2	1	0 18
42	7	1.2	1	0 7
43	19	1.2	1	0 19
44	24	1.2	1	0 24
45	15	1.2	1	0 15
46	10	1.2	1	0 10
47	17	1.2	1	2 15
48	13	1.2	1	0 13
49	13	1.2	1	0 13
50	4	1.2	1	0 4
51	17	1.2	1	1 16
52	20	1.2	1	0 20
53	16	1.2	1	0 16
54	15	1.2	1	0 15
55	21	1.2	1	0 21
56	15	1.2	1	0 15
57	15	1.2	1	0 15
58	23	1.2	1	0 23
59	6	1.2	1	0 6
60	10	1.2	1	0 10
61	31	1.2	1	4 27
62	16	1.2	1	0 16
63	13	1.2	1	1 12
64	19	1.2	1	9 10
65	20	1.2	1	0 20
66	17	1.2	1	1 16
67	12	1.2	1	0 12
68	23	1.2	1	7 16
69	9	1.2	1	1 8
70	20	1.2	1	0 20
71	14	1.2	1	3 11
72	8	1.2	1	0 8
73	14	1.2	1	0 14
74	22	1.2	1	0 22
75	12	1.2	1	0 12
76	19	1.2	1	0 19
77	12	1.2	1	0 12
78	21	1.2	1	0 21
79	19	1.2	1	0 19
80	18	1.2	1	3 15
81	24	1.2	1	3 21
82	27	1.2	1	0 27
83	15	1.2	1	0 15
84	14	1.2	1	2 12
85	21	1.2	1	0 21
86	21	1.2	1	0 21
87	16	1.2	1	1 15
88	24	1.2	1	0 24
89	21	1.2	1	0 21
90	23	1.2	1	1 22
91	14	1.2	1	0 14
92	12	1.2	1	0 12
93	18	1.2	1	0 18
94	10	1.2	1	1 9
95	13	1.2	1	2 11
96	9	1.2	1	0 9
97	13	1.2	1	0 13
98	5	1.2	1	0 5
99	11	1.2	1	0 11
100	17	1.2	1	0 17
101	23	1.2	1	2 21
102	10	1.2	1	4 6
103	11	1.2	1	0 11
104	12	1.2	1	4 8
105	15	1.2	1	0 15
106	24	1.2	1	1 23
107	12	1.2	1	3 9
108	23	1.2	1	1 22
109	12	1.2	1	1 11
110	19	1.2	1	1 18
111	14	1.2	1	1 13
112	34	1.2	1	0 34
113	14	1.2	1	3 11
114	15	1.2	1	0 15
115	13	1.2	1	0 13
116	17	1.2	1	0 17
117	12	1.2	1	2 10
118	4	1.2	1	0 4
119	24	1.2	1	0 24
120	26	1.2	1	1 25
121	13	1.2	1	0 13
122	26	1.2	1	0 26
123	15	1.2	1	4 11
124	7	1.2	1	0 7
125	12	1.2	1	0 12
126	17	1.2	1	0 17
127	10	1.2	1	0 10
128	19	1.2	1	0 19
129	11	1.2	1	0 11
130	17	1.2	1	2 15
131	11	1.2	1	0 11
132	21	1.2	1	8 13
133	11	1.2	1	1 10
134	10	1.2	1	0 10
135	14	1.2	1	0 14
136	22	1.2	1	0 22
137	10	1.2	1	0 10
138	12	1.2	1	2 10
139	8	1.2	1	0 8
140	8	1.2	1	0 8
141	7	1.2	1	0 7
142	8	1.2	1	0 8
143	14	1.2	1	2 12
144	7	1.2	1	0 7
145	18	1.2	1	0 18
146	17	1.2	1	0 17
147	5	1.2	1	0 5
148	4	1.2	1	0 4
149	13	1.2	1	2 11
150	6	1.2	1	0 6

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz
=============================================
20354161 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 2172 (0.01%)
Total number of sequences analysed for the sequence pair length validation: 20354161

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 3647 (0.02%)
