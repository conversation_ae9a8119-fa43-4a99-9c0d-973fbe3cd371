
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 1, count Nextera: 1, count Illumina: 0)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior.
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,373,579
Reads with adapters:                    41,502 (0.2%)
Reads written (passing filters):    23,373,579 (100.0%)

Total basepairs processed: 3,483,983,799 bp
Quality-trimmed:               5,738,507 bp (0.2%)
Total written (filtered):  3,477,462,322 bp (99.8%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41502 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.7%
  C: 20.4%
  G: 25.6%
  T: 32.1%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	22610	22825.8	0	22610
6	7113	5706.4	0	7113
7	2270	1426.6	0	2270
8	774	356.7	0	774
9	473	89.2	0	74 399
10	570	22.3	1	1 569
11	257	5.6	1	1 256
12	77	1.4	1	0 77
13	63	1.4	1	0 63
14	91	1.4	1	2 89
15	64	1.4	1	0 64
16	51	1.4	1	0 51
17	70	1.4	1	0 70
18	47	1.4	1	0 47
19	49	1.4	1	0 49
20	50	1.4	1	0 50
21	40	1.4	1	0 40
22	54	1.4	1	0 54
23	69	1.4	1	0 69
24	52	1.4	1	0 52
25	93	1.4	1	0 93
26	24	1.4	1	0 24
27	38	1.4	1	0 38
28	52	1.4	1	1 51
29	40	1.4	1	0 40
30	57	1.4	1	0 57
31	57	1.4	1	0 57
32	65	1.4	1	0 65
33	36	1.4	1	0 36
34	117	1.4	1	0 117
35	83	1.4	1	0 83
36	64	1.4	1	0 64
37	37	1.4	1	0 37
38	30	1.4	1	0 30
39	30	1.4	1	0 30
40	35	1.4	1	0 35
41	40	1.4	1	0 40
42	49	1.4	1	0 49
43	48	1.4	1	1 47
44	58	1.4	1	0 58
45	54	1.4	1	3 51
46	73	1.4	1	0 73
47	28	1.4	1	0 28
48	42	1.4	1	0 42
49	51	1.4	1	0 51
50	51	1.4	1	1 50
51	78	1.4	1	2 76
52	64	1.4	1	0 64
53	59	1.4	1	0 59
54	51	1.4	1	0 51
55	90	1.4	1	0 90
56	50	1.4	1	1 49
57	45	1.4	1	0 45
58	36	1.4	1	0 36
59	52	1.4	1	0 52
60	46	1.4	1	0 46
61	51	1.4	1	0 51
62	59	1.4	1	0 59
63	67	1.4	1	0 67
64	56	1.4	1	0 56
65	78	1.4	1	0 78
66	64	1.4	1	1 63
67	55	1.4	1	0 55
68	53	1.4	1	0 53
69	34	1.4	1	0 34
70	53	1.4	1	0 53
71	51	1.4	1	0 51
72	43	1.4	1	0 43
73	49	1.4	1	0 49
74	38	1.4	1	0 38
75	66	1.4	1	0 66
76	76	1.4	1	0 76
77	64	1.4	1	0 64
78	36	1.4	1	0 36
79	45	1.4	1	0 45
80	43	1.4	1	0 43
81	53	1.4	1	0 53
82	44	1.4	1	0 44
83	31	1.4	1	0 31
84	57	1.4	1	0 57
85	48	1.4	1	0 48
86	90	1.4	1	0 90
87	72	1.4	1	1 71
88	56	1.4	1	0 56
89	44	1.4	1	0 44
90	47	1.4	1	0 47
91	58	1.4	1	1 57
92	50	1.4	1	0 50
93	49	1.4	1	0 49
94	57	1.4	1	0 57
95	54	1.4	1	0 54
96	67	1.4	1	0 67
97	77	1.4	1	0 77
98	42	1.4	1	0 42
99	50	1.4	1	1 49
100	54	1.4	1	2 52
101	45	1.4	1	0 45
102	28	1.4	1	0 28
103	38	1.4	1	0 38
104	45	1.4	1	0 45
105	52	1.4	1	0 52
106	49	1.4	1	0 49
107	61	1.4	1	0 61
108	69	1.4	1	0 69
109	39	1.4	1	0 39
110	83	1.4	1	0 83
111	32	1.4	1	0 32
112	50	1.4	1	0 50
113	32	1.4	1	0 32
114	68	1.4	1	0 68
115	64	1.4	1	0 64
116	41	1.4	1	0 41
117	51	1.4	1	0 51
118	58	1.4	1	3 55
119	48	1.4	1	0 48
120	37	1.4	1	0 37
121	55	1.4	1	0 55
122	62	1.4	1	0 62
123	57	1.4	1	0 57
124	55	1.4	1	0 55
125	43	1.4	1	0 43
126	43	1.4	1	0 43
127	49	1.4	1	3 46
128	39	1.4	1	0 39
129	39	1.4	1	0 39
130	47	1.4	1	1 46
131	48	1.4	1	0 48
132	49	1.4	1	0 49
133	85	1.4	1	0 85
134	59	1.4	1	0 59
135	47	1.4	1	2 45
136	39	1.4	1	0 39
137	46	1.4	1	0 46
138	66	1.4	1	0 66
139	69	1.4	1	0 69
140	51	1.4	1	0 51
141	27	1.4	1	0 27
142	57	1.4	1	0 57
143	69	1.4	1	1 68
144	57	1.4	1	0 57
145	56	1.4	1	0 56
146	22	1.4	1	0 22
147	47	1.4	1	0 47
148	33	1.4	1	0 33
149	101	1.4	1	1 100
150	47	1.4	1	0 47

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz
=============================================
23373579 sequences processed in total

