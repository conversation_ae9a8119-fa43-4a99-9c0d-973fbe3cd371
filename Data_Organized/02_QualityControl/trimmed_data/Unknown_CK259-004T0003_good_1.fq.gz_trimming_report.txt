
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 2, count Nextera: 2, count Illumina: 1)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior.
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,435,036
Reads with adapters:                    39,007 (0.2%)
Reads written (passing filters):    20,435,036 (100.0%)

Total basepairs processed: 3,044,382,574 bp
Quality-trimmed:                 824,031 bp (0.0%)
Total written (filtered):  3,042,723,565 bp (99.9%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 39007 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 19.0%
  C: 23.4%
  G: 24.9%
  T: 32.5%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	20831	19956.1	0	20831
6	6039	4989.0	0	6039
7	2032	1247.3	0	2032
8	849	311.8	0	849
9	405	78.0	0	107 298
10	478	19.5	1	6 472
11	228	4.9	1	2 226
12	84	1.2	1	0 84
13	63	1.2	1	0 63
14	71	1.2	1	0 71
15	58	1.2	1	0 58
16	31	1.2	1	0 31
17	70	1.2	1	0 70
18	42	1.2	1	0 42
19	40	1.2	1	0 40
20	29	1.2	1	0 29
21	46	1.2	1	0 46
22	42	1.2	1	0 42
23	45	1.2	1	0 45
24	44	1.2	1	0 44
25	55	1.2	1	1 54
26	53	1.2	1	0 53
27	51	1.2	1	0 51
28	46	1.2	1	1 45
29	47	1.2	1	0 47
30	69	1.2	1	1 68
31	86	1.2	1	2 84
32	65	1.2	1	3 62
33	44	1.2	1	0 44
34	97	1.2	1	0 97
35	61	1.2	1	0 61
36	51	1.2	1	0 51
37	34	1.2	1	0 34
38	40	1.2	1	0 40
39	50	1.2	1	3 47
40	39	1.2	1	0 39
41	47	1.2	1	0 47
42	44	1.2	1	0 44
43	41	1.2	1	1 40
44	34	1.2	1	0 34
45	110	1.2	1	0 110
46	96	1.2	1	0 96
47	41	1.2	1	0 41
48	56	1.2	1	1 55
49	38	1.2	1	0 38
50	29	1.2	1	0 29
51	46	1.2	1	1 45
52	55	1.2	1	0 55
53	118	1.2	1	0 118
54	49	1.2	1	2 47
55	83	1.2	1	0 83
56	42	1.2	1	0 42
57	41	1.2	1	0 41
58	26	1.2	1	0 26
59	40	1.2	1	0 40
60	37	1.2	1	0 37
61	46	1.2	1	0 46
62	72	1.2	1	0 72
63	73	1.2	1	0 73
64	92	1.2	1	0 92
65	68	1.2	1	1 67
66	68	1.2	1	0 68
67	84	1.2	1	0 84
68	51	1.2	1	0 51
69	49	1.2	1	0 49
70	54	1.2	1	0 54
71	38	1.2	1	0 38
72	60	1.2	1	0 60
73	56	1.2	1	0 56
74	67	1.2	1	0 67
75	81	1.2	1	1 80
76	129	1.2	1	1 128
77	71	1.2	1	0 71
78	117	1.2	1	0 117
79	56	1.2	1	0 56
80	82	1.2	1	0 82
81	131	1.2	1	0 131
82	75	1.2	1	0 75
83	60	1.2	1	0 60
84	42	1.2	1	1 41
85	51	1.2	1	0 51
86	112	1.2	1	0 112
87	63	1.2	1	0 63
88	48	1.2	1	0 48
89	50	1.2	1	0 50
90	44	1.2	1	0 44
91	71	1.2	1	0 71
92	48	1.2	1	0 48
93	72	1.2	1	0 72
94	52	1.2	1	0 52
95	60	1.2	1	0 60
96	60	1.2	1	0 60
97	88	1.2	1	0 88
98	48	1.2	1	0 48
99	53	1.2	1	0 53
100	51	1.2	1	1 50
101	47	1.2	1	0 47
102	95	1.2	1	0 95
103	41	1.2	1	0 41
104	46	1.2	1	0 46
105	67	1.2	1	0 67
106	55	1.2	1	1 54
107	62	1.2	1	0 62
108	62	1.2	1	0 62
109	36	1.2	1	0 36
110	47	1.2	1	0 47
111	38	1.2	1	0 38
112	71	1.2	1	0 71
113	22	1.2	1	0 22
114	68	1.2	1	0 68
115	60	1.2	1	0 60
116	48	1.2	1	0 48
117	57	1.2	1	0 57
118	103	1.2	1	1 102
119	68	1.2	1	0 68
120	56	1.2	1	0 56
121	52	1.2	1	0 52
122	39	1.2	1	0 39
123	93	1.2	1	0 93
124	59	1.2	1	1 58
125	42	1.2	1	0 42
126	55	1.2	1	0 55
127	87	1.2	1	1 86
128	65	1.2	1	0 65
129	52	1.2	1	2 50
130	57	1.2	1	1 56
131	46	1.2	1	0 46
132	61	1.2	1	0 61
133	70	1.2	1	0 70
134	51	1.2	1	0 51
135	83	1.2	1	0 83
136	56	1.2	1	0 56
137	40	1.2	1	3 37
138	50	1.2	1	0 50
139	68	1.2	1	0 68
140	65	1.2	1	0 65
141	42	1.2	1	0 42
142	54	1.2	1	0 54
143	50	1.2	1	0 50
144	54	1.2	1	0 54
145	39	1.2	1	0 39
146	29	1.2	1	0 29
147	46	1.2	1	1 45
148	35	1.2	1	0 35
149	84	1.2	1	0 84
150	53	1.2	1	0 53

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz
=============================================
20435036 sequences processed in total

