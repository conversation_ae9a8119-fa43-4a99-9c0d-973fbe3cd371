
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 1, count Nextera: 1, count Illumina: 0)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior.
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,373,579
Reads with adapters:                    41,005 (0.2%)
Reads written (passing filters):    23,373,579 (100.0%)

Total basepairs processed: 3,484,037,713 bp
Quality-trimmed:              12,696,095 bp (0.4%)
Total written (filtered):  3,470,585,541 bp (99.6%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41005 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.5%
  C: 20.4%
  G: 24.9%
  T: 33.0%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	22306	22825.8	0	22306
6	6973	5706.4	0	6973
7	2404	1426.6	0	2404
8	841	356.7	0	841
9	509	89.2	0	71 438
10	573	22.3	1	1 572
11	251	5.6	1	0 251
12	70	1.4	1	0 70
13	65	1.4	1	0 65
14	71	1.4	1	1 70
15	66	1.4	1	0 66
16	35	1.4	1	0 35
17	56	1.4	1	0 56
18	38	1.4	1	0 38
19	59	1.4	1	0 59
20	51	1.4	1	0 51
21	38	1.4	1	0 38
22	51	1.4	1	0 51
23	62	1.4	1	0 62
24	58	1.4	1	0 58
25	65	1.4	1	0 65
26	56	1.4	1	0 56
27	31	1.4	1	0 31
28	60	1.4	1	1 59
29	59	1.4	1	0 59
30	45	1.4	1	0 45
31	69	1.4	1	0 69
32	81	1.4	1	1 80
33	49	1.4	1	0 49
34	108	1.4	1	0 108
35	57	1.4	1	0 57
36	51	1.4	1	0 51
37	45	1.4	1	0 45
38	40	1.4	1	0 40
39	50	1.4	1	0 50
40	29	1.4	1	1 28
41	48	1.4	1	0 48
42	38	1.4	1	0 38
43	36	1.4	1	1 35
44	43	1.4	1	0 43
45	68	1.4	1	0 68
46	57	1.4	1	0 57
47	47	1.4	1	0 47
48	65	1.4	1	0 65
49	57	1.4	1	0 57
50	25	1.4	1	0 25
51	62	1.4	1	0 62
52	82	1.4	1	0 82
53	82	1.4	1	0 82
54	53	1.4	1	0 53
55	90	1.4	1	0 90
56	64	1.4	1	1 63
57	42	1.4	1	0 42
58	38	1.4	1	0 38
59	48	1.4	1	0 48
60	27	1.4	1	0 27
61	26	1.4	1	0 26
62	35	1.4	1	0 35
63	63	1.4	1	0 63
64	53	1.4	1	0 53
65	53	1.4	1	0 53
66	62	1.4	1	0 62
67	55	1.4	1	0 55
68	62	1.4	1	0 62
69	48	1.4	1	0 48
70	45	1.4	1	0 45
71	39	1.4	1	0 39
72	41	1.4	1	0 41
73	37	1.4	1	0 37
74	43	1.4	1	0 43
75	66	1.4	1	0 66
76	42	1.4	1	0 42
77	60	1.4	1	0 60
78	38	1.4	1	0 38
79	37	1.4	1	0 37
80	36	1.4	1	0 36
81	71	1.4	1	0 71
82	30	1.4	1	0 30
83	66	1.4	1	0 66
84	51	1.4	1	0 51
85	34	1.4	1	0 34
86	67	1.4	1	0 67
87	72	1.4	1	0 72
88	42	1.4	1	0 42
89	62	1.4	1	0 62
90	73	1.4	1	0 73
91	28	1.4	1	0 28
92	50	1.4	1	0 50
93	65	1.4	1	0 65
94	39	1.4	1	0 39
95	67	1.4	1	0 67
96	67	1.4	1	0 67
97	74	1.4	1	0 74
98	48	1.4	1	0 48
99	38	1.4	1	1 37
100	72	1.4	1	0 72
101	45	1.4	1	0 45
102	28	1.4	1	0 28
103	34	1.4	1	0 34
104	51	1.4	1	0 51
105	41	1.4	1	0 41
106	46	1.4	1	0 46
107	73	1.4	1	0 73
108	52	1.4	1	0 52
109	35	1.4	1	0 35
110	59	1.4	1	0 59
111	48	1.4	1	0 48
112	52	1.4	1	0 52
113	51	1.4	1	0 51
114	60	1.4	1	0 60
115	58	1.4	1	0 58
116	48	1.4	1	0 48
117	44	1.4	1	0 44
118	45	1.4	1	0 45
119	52	1.4	1	0 52
120	18	1.4	1	0 18
121	35	1.4	1	0 35
122	35	1.4	1	0 35
123	56	1.4	1	0 56
124	70	1.4	1	2 68
125	39	1.4	1	0 39
126	48	1.4	1	0 48
127	36	1.4	1	2 34
128	49	1.4	1	0 49
129	20	1.4	1	0 20
130	45	1.4	1	0 45
131	56	1.4	1	0 56
132	33	1.4	1	0 33
133	80	1.4	1	1 79
134	56	1.4	1	0 56
135	40	1.4	1	2 38
136	40	1.4	1	0 40
137	52	1.4	1	0 52
138	54	1.4	1	0 54
139	71	1.4	1	0 71
140	48	1.4	1	0 48
141	32	1.4	1	0 32
142	56	1.4	1	0 56
143	64	1.4	1	0 64
144	68	1.4	1	0 68
145	45	1.4	1	0 45
146	34	1.4	1	0 34
147	52	1.4	1	0 52
148	34	1.4	1	2 32
149	70	1.4	1	0 70
150	46	1.4	1	0 46

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz
=============================================
23373579 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 19994 (0.09%)
Total number of sequences analysed for the sequence pair length validation: 23373579

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 82860 (0.35%)
