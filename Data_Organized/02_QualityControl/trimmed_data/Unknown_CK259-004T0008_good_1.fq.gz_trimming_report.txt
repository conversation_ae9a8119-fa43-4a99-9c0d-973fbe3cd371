
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 4). Second best hit was Nextera (count: 1)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,604,430
Reads with adapters:                    87,789 (0.4%)
Reads written (passing filters):    23,604,430 (100.0%)

Total basepairs processed: 3,517,362,851 bp
Quality-trimmed:               5,526,204 bp (0.2%)
Total written (filtered):  3,510,503,230 bp (99.8%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 87789 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 22.3%
  C: 20.4%
  G: 31.4%
  T: 25.8%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	60235	23051.2	0	60235
6	10222	5762.8	0	10222
7	2866	1440.7	0	2866
8	652	360.2	0	652
9	1256	90.0	0	191 1065
10	1157	22.5	1	41 1116
11	331	5.6	1	2 329
12	68	1.4	1	0 68
13	78	1.4	1	6 72
14	40	1.4	1	0 40
15	35	1.4	1	0 35
16	46	1.4	1	0 46
17	32	1.4	1	0 32
18	36	1.4	1	0 36
19	51	1.4	1	0 51
20	55	1.4	1	0 55
21	39	1.4	1	0 39
22	121	1.4	1	0 121
23	82	1.4	1	0 82
24	89	1.4	1	3 86
25	56	1.4	1	0 56
26	21	1.4	1	0 21
27	76	1.4	1	0 76
28	54	1.4	1	0 54
29	55	1.4	1	0 55
30	40	1.4	1	0 40
31	50	1.4	1	0 50
32	58	1.4	1	0 58
33	57	1.4	1	1 56
34	56	1.4	1	0 56
35	69	1.4	1	0 69
36	94	1.4	1	0 94
37	52	1.4	1	0 52
38	93	1.4	1	2 91
39	62	1.4	1	0 62
40	126	1.4	1	0 126
41	82	1.4	1	0 82
42	81	1.4	1	1 80
43	85	1.4	1	1 84
44	66	1.4	1	0 66
45	105	1.4	1	0 105
46	71	1.4	1	1 70
47	87	1.4	1	4 83
48	52	1.4	1	0 52
49	50	1.4	1	0 50
50	67	1.4	1	0 67
51	78	1.4	1	1 77
52	85	1.4	1	0 85
53	176	1.4	1	0 176
54	79	1.4	1	0 79
55	54	1.4	1	1 53
56	59	1.4	1	2 57
57	92	1.4	1	0 92
58	81	1.4	1	0 81
59	65	1.4	1	3 62
60	46	1.4	1	0 46
61	93	1.4	1	0 93
62	112	1.4	1	0 112
63	74	1.4	1	1 73
64	71	1.4	1	0 71
65	47	1.4	1	0 47
66	46	1.4	1	0 46
67	61	1.4	1	0 61
68	42	1.4	1	0 42
69	32	1.4	1	0 32
70	96	1.4	1	0 96
71	86	1.4	1	0 86
72	93	1.4	1	0 93
73	66	1.4	1	0 66
74	56	1.4	1	0 56
75	174	1.4	1	0 174
76	113	1.4	1	0 113
77	126	1.4	1	0 126
78	98	1.4	1	0 98
79	91	1.4	1	0 91
80	85	1.4	1	0 85
81	62	1.4	1	0 62
82	187	1.4	1	0 187
83	54	1.4	1	0 54
84	88	1.4	1	2 86
85	52	1.4	1	0 52
86	59	1.4	1	0 59
87	78	1.4	1	1 77
88	84	1.4	1	0 84
89	175	1.4	1	0 175
90	81	1.4	1	0 81
91	63	1.4	1	0 63
92	147	1.4	1	0 147
93	125	1.4	1	0 125
94	135	1.4	1	0 135
95	174	1.4	1	0 174
96	153	1.4	1	0 153
97	143	1.4	1	0 143
98	58	1.4	1	2 56
99	86	1.4	1	0 86
100	340	1.4	1	2 338
101	141	1.4	1	0 141
102	263	1.4	1	0 263
103	167	1.4	1	0 167
104	123	1.4	1	0 123
105	57	1.4	1	0 57
106	63	1.4	1	0 63
107	49	1.4	1	0 49
108	68	1.4	1	0 68
109	92	1.4	1	4 88
110	48	1.4	1	2 46
111	71	1.4	1	0 71
112	64	1.4	1	1 63
113	61	1.4	1	0 61
114	52	1.4	1	0 52
115	48	1.4	1	0 48
116	70	1.4	1	0 70
117	107	1.4	1	0 107
118	54	1.4	1	0 54
119	48	1.4	1	0 48
120	59	1.4	1	0 59
121	49	1.4	1	0 49
122	73	1.4	1	0 73
123	51	1.4	1	0 51
124	107	1.4	1	1 106
125	75	1.4	1	0 75
126	49	1.4	1	0 49
127	45	1.4	1	0 45
128	60	1.4	1	0 60
129	92	1.4	1	0 92
130	51	1.4	1	0 51
131	87	1.4	1	0 87
132	67	1.4	1	0 67
133	51	1.4	1	0 51
134	47	1.4	1	0 47
135	67	1.4	1	0 67
136	75	1.4	1	1 74
137	65	1.4	1	0 65
138	53	1.4	1	0 53
139	58	1.4	1	0 58
140	51	1.4	1	0 51
141	84	1.4	1	0 84
142	62	1.4	1	0 62
143	66	1.4	1	0 66
144	67	1.4	1	0 67
145	14	1.4	1	0 14
146	47	1.4	1	0 47
147	54	1.4	1	0 54
148	71	1.4	1	0 71
149	198	1.4	1	2 196
150	71	1.4	1	0 71

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz
=============================================
23604430 sequences processed in total

