Note: gsnap.avx512 does not exist.  For faster speed, may want to compile package on an AVX512 machine
Note: gsnap.avx2 does not exist.  For faster speed, may want to compile package on an AVX2 machine
GSNAP version 2024-11-20 called with args: gsnap.sse42 -D /public/home/<USER>/2025hagfish/scRNA/20250308_GMAPalign/EbuGenome_DB/ -d Ebu3.2top_library --gunzip trim_galore/Unknown_CK259-004T0001_good_1_val_1.fq.gz trim_galore/Unknown_CK259-004T0001_good_2_val_2.fq.gz --novelsplicing 1 -t 10
Novel splicing (-N) turned on => assume reads are RNA-Seq
Checking compiler assumptions for SSE2: 6B8B4567 327B23C6 xor=59F066A1
Checking compiler assumptions for SSE4.1: -103 -58 max=198 => compiler zero extends
Checking compiler assumptions for SSE4.2 options: 6B8B4567 __builtin_clz=1 __builtin_ctz=0 _mm_popcnt_u32=17 __builtin_popcount=17 
Finished checking compiler assumptions
Allocating memory for compressed genome (oligos)...done (978,143,840 bytes, 0.60 sec)
Allocating memory for compressed genome (bits)...done (978,143,856 bytes, 0.60 sec)
Looking for genome Ebu3.2top_library in directory /public/home/<USER>/2025hagfish/scRNA/20250308_GMAPalign/EbuGenome_DB//Ebu3.2top_library
Looking for index files in directory /public/home/<USER>/2025hagfish/scRNA/20250308_GMAPalign/EbuGenome_DB//Ebu3.2top_library
  Pointers file is Ebu3.2top_library.ref153offsets64meta
  Offsets file is Ebu3.2top_library.ref153offsets64strm
  Positions file is Ebu3.2top_library.ref153positions
Offsets compression type: bitpack64
Allocating memory for ref offset pointers, kmer 15, interval 3...done (134,217,744 bytes, 0.08 sec)
Allocating memory for ref offsets, kmer 15, interval 3...done (450,361,408 bytes, 0.28 sec)
Allocating memory for ref positions, kmer 15, interval 3...done (1,907,488,208 bytes, 1.19 sec)
Allocating memory for localdb...done (10,464,101,080 bytes, 2.87 sec)
Starting alignment
Signal received: SIGSEGV
Calling Access_emergency_cleanup
Problem sequence: A00159:3002:HHNM2DSX3:3:1101:32160:6886 (150 bp)
>A00159:3002:HHNM2DSX3:3:1101:32160:6886 1:N:0:GGCTCCAA+GATCAGAC
CATATCTTATATCAACGCCTGAGCCCTTTCATCAACCATTCTAGCAACAGCCTTAACCACAATTTACAGTACTCGTATAATTTTACTTTCCTTAACCTTACACCCTAATATAAATACCTTAATTTCAATCAAAAAAAACAAAAACCTCTT
GGGAAATATCCTGATATTGAGGTGAATATAAAGGATGGTAGGATTATGGATTTTAGGTTGTTAATGTAGAATAGTGTTAGTGATGAAGAGACGATAAGTAGTATTATTGGTATAATTTTGTTGTTGAAGTTAATTGAAAAGTTAGGTTGT
