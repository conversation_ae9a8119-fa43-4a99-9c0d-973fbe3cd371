检测到以下样本：
Unknown_CK259-004T0001_good
Unknown_CK259-004T0002_good
Unknown_CK259-004T0003_good
Unknown_CK259-004T0004_good
Unknown_CK259-004T0005_good
Unknown_CK259-004T0006_good
Unknown_CK259-004T0007_good
Unknown_CK259-004T0008_good
Unknown_CK259-004T0009_good
Unknown_CK259-004T0010_good
Unknown_CK259-004T0011_good
Unknown_CK259-004T0012_good
Unknown_CK259-004T0013_good
Unknown_CK259-004T0014_good
Unknown_CK259-004T0015_good
Unknown_CK259-004T0016_good
Unknown_CK259-004T0017_good
Unknown_CK259-004T0018_good
Unknown_CK259-004T0019_good
Unknown_CK259-004T0020_good
Unknown_CK259-004T0021_good
Unknown_CK259-004T0022_good
Unknown_CK259-004T0023_good
Unknown_CK259-004T0024_good
正在处理样本 Unknown_CK259-004T0001_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Illumina	1	AGATCGGAAGAGC	1000000	0.00
Nextera	0	CTGTCTCTTATA	1000000	0.00
Using smallRNA adapter for trimming (count: 2). Second best hit was Illumina (count: 1)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0001_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0001_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,366,828
Reads with adapters:                    63,044 (0.3%)
Reads written (passing filters):    19,366,828 (100.0%)

Total basepairs processed: 2,883,179,601 bp
Quality-trimmed:               2,052,873 bp (0.1%)
Total written (filtered):  2,880,092,022 bp (99.9%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 63044 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.7%
  C: 23.3%
  G: 24.9%
  T: 26.9%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	38190	18912.9	0	38190
6	9847	4728.2	0	9847
7	2886	1182.1	0	2886
8	548	295.5	0	548
9	1079	73.9	0	195 884
10	1270	18.5	1	27 1243
11	356	4.6	1	6 350
12	61	1.2	1	0 61
13	54	1.2	1	0 54
14	25	1.2	1	0 25
15	55	1.2	1	0 55
16	67	1.2	1	0 67
17	56	1.2	1	1 55
18	58	1.2	1	0 58
19	93	1.2	1	0 93
20	67	1.2	1	0 67
21	43	1.2	1	0 43
22	42	1.2	1	0 42
23	56	1.2	1	0 56
24	70	1.2	1	0 70
25	72	1.2	1	0 72
26	43	1.2	1	2 41
27	55	1.2	1	1 54
28	29	1.2	1	0 29
29	69	1.2	1	1 68
30	73	1.2	1	0 73
31	61	1.2	1	0 61
32	68	1.2	1	0 68
33	76	1.2	1	0 76
34	67	1.2	1	0 67
35	64	1.2	1	0 64
36	54	1.2	1	4 50
37	58	1.2	1	0 58
38	46	1.2	1	1 45
39	72	1.2	1	0 72
40	100	1.2	1	0 100
41	54	1.2	1	0 54
42	43	1.2	1	0 43
43	70	1.2	1	0 70
44	48	1.2	1	0 48
45	53	1.2	1	0 53
46	76	1.2	1	0 76
47	63	1.2	1	0 63
48	56	1.2	1	0 56
49	56	1.2	1	0 56
50	58	1.2	1	0 58
51	45	1.2	1	0 45
52	49	1.2	1	0 49
53	72	1.2	1	0 72
54	74	1.2	1	0 74
55	85	1.2	1	1 84
56	61	1.2	1	0 61
57	61	1.2	1	0 61
58	113	1.2	1	0 113
59	70	1.2	1	0 70
60	58	1.2	1	0 58
61	59	1.2	1	0 59
62	51	1.2	1	0 51
63	36	1.2	1	0 36
64	77	1.2	1	0 77
65	70	1.2	1	0 70
66	52	1.2	1	0 52
67	47	1.2	1	0 47
68	40	1.2	1	0 40
69	68	1.2	1	0 68
70	48	1.2	1	0 48
71	37	1.2	1	0 37
72	79	1.2	1	0 79
73	68	1.2	1	0 68
74	71	1.2	1	0 71
75	66	1.2	1	0 66
76	66	1.2	1	0 66
77	61	1.2	1	0 61
78	69	1.2	1	2 67
79	78	1.2	1	0 78
80	83	1.2	1	0 83
81	71	1.2	1	1 70
82	75	1.2	1	0 75
83	71	1.2	1	1 70
84	79	1.2	1	0 79
85	49	1.2	1	0 49
86	50	1.2	1	1 49
87	60	1.2	1	1 59
88	84	1.2	1	0 84
89	88	1.2	1	2 86
90	63	1.2	1	0 63
91	58	1.2	1	0 58
92	63	1.2	1	1 62
93	86	1.2	1	0 86
94	63	1.2	1	0 63
95	45	1.2	1	0 45
96	37	1.2	1	0 37
97	76	1.2	1	0 76
98	43	1.2	1	1 42
99	86	1.2	1	0 86
100	71	1.2	1	0 71
101	65	1.2	1	0 65
102	59	1.2	1	0 59
103	79	1.2	1	0 79
104	57	1.2	1	0 57
105	55	1.2	1	0 55
106	61	1.2	1	0 61
107	75	1.2	1	0 75
108	131	1.2	1	0 131
109	55	1.2	1	0 55
110	90	1.2	1	0 90
111	47	1.2	1	0 47
112	83	1.2	1	0 83
113	66	1.2	1	0 66
114	50	1.2	1	0 50
115	41	1.2	1	0 41
116	76	1.2	1	0 76
117	85	1.2	1	1 84
118	59	1.2	1	0 59
119	51	1.2	1	0 51
120	47	1.2	1	0 47
121	51	1.2	1	0 51
122	70	1.2	1	1 69
123	53	1.2	1	0 53
124	78	1.2	1	0 78
125	76	1.2	1	1 75
126	64	1.2	1	0 64
127	58	1.2	1	0 58
128	34	1.2	1	0 34
129	84	1.2	1	1 83
130	68	1.2	1	1 67
131	88	1.2	1	0 88
132	63	1.2	1	0 63
133	45	1.2	1	1 44
134	50	1.2	1	0 50
135	63	1.2	1	0 63
136	62	1.2	1	0 62
137	59	1.2	1	0 59
138	50	1.2	1	0 50
139	41	1.2	1	0 41
140	51	1.2	1	0 51
141	87	1.2	1	0 87
142	44	1.2	1	0 44
143	50	1.2	1	0 50
144	69	1.2	1	0 69
145	15	1.2	1	0 15
146	20	1.2	1	0 20
147	37	1.2	1	0 37
148	93	1.2	1	0 93
149	258	1.2	1	0 258
150	92	1.2	1	0 92

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_1.fq.gz
=============================================
19366828 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0001_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0001_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,366,828
Reads with adapters:                    18,247 (0.1%)
Reads written (passing filters):    19,366,828 (100.0%)

Total basepairs processed: 2,882,904,501 bp
Quality-trimmed:               4,111,418 bp (0.1%)
Total written (filtered):  2,878,387,666 bp (99.8%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 18247 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 14.8%
  C: 20.6%
  G: 26.1%
  T: 38.5%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	9108	18912.9	0	9108
6	3352	4728.2	0	3352
7	692	1182.1	0	692
8	195	295.5	0	195
9	261	73.9	0	43 218
10	407	18.5	1	6 401
11	124	4.6	1	1 123
12	54	1.2	1	12 42
13	20	1.2	1	0 20
14	24	1.2	1	3 21
15	29	1.2	1	1 28
16	25	1.2	1	0 25
17	47	1.2	1	10 37
18	42	1.2	1	4 38
19	38	1.2	1	5 33
20	34	1.2	1	0 34
21	23	1.2	1	1 22
22	27	1.2	1	1 26
23	33	1.2	1	1 32
24	31	1.2	1	1 30
25	29	1.2	1	1 28
26	39	1.2	1	0 39
27	22	1.2	1	0 22
28	24	1.2	1	2 22
29	28	1.2	1	0 28
30	26	1.2	1	1 25
31	27	1.2	1	0 27
32	13	1.2	1	1 12
33	23	1.2	1	1 22
34	27	1.2	1	0 27
35	37	1.2	1	0 37
36	33	1.2	1	1 32
37	24	1.2	1	2 22
38	44	1.2	1	1 43
39	54	1.2	1	2 52
40	29	1.2	1	2 27
41	38	1.2	1	0 38
42	25	1.2	1	1 24
43	36	1.2	1	3 33
44	42	1.2	1	3 39
45	24	1.2	1	0 24
46	28	1.2	1	2 26
47	34	1.2	1	1 33
48	27	1.2	1	1 26
49	30	1.2	1	1 29
50	27	1.2	1	0 27
51	45	1.2	1	0 45
52	47	1.2	1	0 47
53	34	1.2	1	1 33
54	29	1.2	1	0 29
55	44	1.2	1	1 43
56	34	1.2	1	1 33
57	36	1.2	1	1 35
58	38	1.2	1	2 36
59	38	1.2	1	0 38
60	20	1.2	1	2 18
61	29	1.2	1	2 27
62	22	1.2	1	1 21
63	36	1.2	1	0 36
64	27	1.2	1	4 23
65	14	1.2	1	2 12
66	31	1.2	1	2 29
67	24	1.2	1	0 24
68	27	1.2	1	3 24
69	24	1.2	1	4 20
70	21	1.2	1	0 21
71	31	1.2	1	3 28
72	27	1.2	1	0 27
73	25	1.2	1	0 25
74	34	1.2	1	0 34
75	20	1.2	1	0 20
76	35	1.2	1	0 35
77	17	1.2	1	0 17
78	20	1.2	1	0 20
79	34	1.2	1	3 31
80	18	1.2	1	1 17
81	27	1.2	1	1 26
82	41	1.2	1	1 40
83	21	1.2	1	0 21
84	17	1.2	1	0 17
85	36	1.2	1	0 36
86	32	1.2	1	1 31
87	27	1.2	1	0 27
88	52	1.2	1	1 51
89	25	1.2	1	1 24
90	33	1.2	1	1 32
91	26	1.2	1	0 26
92	30	1.2	1	0 30
93	31	1.2	1	0 31
94	30	1.2	1	2 28
95	20	1.2	1	0 20
96	24	1.2	1	0 24
97	18	1.2	1	0 18
98	27	1.2	1	0 27
99	29	1.2	1	1 28
100	23	1.2	1	2 21
101	20	1.2	1	1 19
102	40	1.2	1	7 33
103	13	1.2	1	0 13
104	22	1.2	1	1 21
105	21	1.2	1	1 20
106	25	1.2	1	0 25
107	34	1.2	1	8 26
108	34	1.2	1	2 32
109	32	1.2	1	2 30
110	24	1.2	1	0 24
111	26	1.2	1	0 26
112	30	1.2	1	0 30
113	26	1.2	1	1 25
114	28	1.2	1	0 28
115	26	1.2	1	0 26
116	37	1.2	1	1 36
117	26	1.2	1	1 25
118	21	1.2	1	1 20
119	40	1.2	1	0 40
120	23	1.2	1	1 22
121	43	1.2	1	3 40
122	32	1.2	1	1 31
123	22	1.2	1	1 21
124	19	1.2	1	4 15
125	22	1.2	1	3 19
126	75	1.2	1	10 65
127	36	1.2	1	2 34
128	37	1.2	1	7 30
129	44	1.2	1	5 39
130	37	1.2	1	2 35
131	28	1.2	1	5 23
132	33	1.2	1	10 23
133	27	1.2	1	4 23
134	46	1.2	1	10 36
135	31	1.2	1	3 28
136	29	1.2	1	1 28
137	21	1.2	1	0 21
138	29	1.2	1	2 27
139	32	1.2	1	2 30
140	17	1.2	1	2 15
141	26	1.2	1	1 25
142	27	1.2	1	0 27
143	11	1.2	1	0 11
144	21	1.2	1	0 21
145	11	1.2	1	0 11
146	67	1.2	1	0 67
147	16	1.2	1	0 16
148	17	1.2	1	0 17
149	21	1.2	1	0 21
150	16	1.2	1	1 15

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0001_good_2.fq.gz
=============================================
19366828 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0001_good_1_trimmed.fq.gz and Unknown_CK259-004T0001_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0001_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0001_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0001_good_1_trimmed.fq.gz and Unknown_CK259-004T0001_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0001_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0001_good_2_val_2.fq.gz

Total number of sequences analysed: 19366828

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 31922 (0.16%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 9302 (0.05%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0001_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0001_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0001_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0001_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0001_good_1_trimmed.fq.gz and Unknown_CK259-004T0001_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0002_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	2	AGATCGGAAGAGC	1000000	0.00
Nextera	1	CTGTCTCTTATA	1000000	0.00
smallRNA	0	TGGAATTCTCGG	1000000	0.00
Using Illumina adapter for trimming (count: 2). Second best hit was Nextera (count: 1)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0002_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0002_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,421,407
Reads with adapters:                     3,807 (0.0%)
Reads written (passing filters):    20,421,407 (100.0%)

Total basepairs processed: 3,037,216,594 bp
Quality-trimmed:               1,498,589 bp (0.0%)
Total written (filtered):  3,035,497,577 bp (99.9%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3807 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 26.5%
  C: 27.4%
  G: 35.9%
  T: 9.7%
  none/other: 0.4%

Overview of removed sequences
length	count	expect	max.err	error counts
5	424	19942.8	0	424
6	50	4985.7	0	50
7	5	1246.4	0	5
8	1	311.6	0	1
9	465	77.9	0	2 463
10	126	19.5	1	1 125
11	90	4.9	1	0 90
12	39	1.2	1	0 39
13	22	0.3	1	0 22
14	16	0.3	1	0 16
15	9	0.3	1	0 9
16	17	0.3	1	1 16
17	15	0.3	1	0 15
18	13	0.3	1	0 13
19	10	0.3	1	0 10
20	26	0.3	1	0 26
21	9	0.3	1	1 8
22	24	0.3	1	1 23
23	30	0.3	1	1 29
24	32	0.3	1	0 32
25	26	0.3	1	0 26
26	20	0.3	1	2 18
27	21	0.3	1	0 21
28	19	0.3	1	2 17
29	16	0.3	1	0 16
30	23	0.3	1	1 22
31	11	0.3	1	0 11
32	19	0.3	1	0 19
33	11	0.3	1	0 11
34	20	0.3	1	1 19
35	17	0.3	1	3 14
36	28	0.3	1	0 28
37	33	0.3	1	1 32
38	17	0.3	1	0 17
39	19	0.3	1	1 18
40	15	0.3	1	1 14
41	13	0.3	1	0 13
42	17	0.3	1	0 17
43	22	0.3	1	1 21
44	10	0.3	1	1 9
45	7	0.3	1	0 7
46	8	0.3	1	0 8
47	18	0.3	1	2 16
48	13	0.3	1	1 12
49	33	0.3	1	2 31
50	24	0.3	1	0 24
51	19	0.3	1	1 18
52	28	0.3	1	1 27
53	19	0.3	1	1 18
54	17	0.3	1	0 17
55	31	0.3	1	0 31
56	14	0.3	1	0 14
57	12	0.3	1	0 12
58	16	0.3	1	1 15
59	24	0.3	1	0 24
60	36	0.3	1	0 36
61	15	0.3	1	0 15
62	23	0.3	1	0 23
63	17	0.3	1	0 17
64	18	0.3	1	0 18
65	17	0.3	1	0 17
66	19	0.3	1	1 18
67	17	0.3	1	0 17
68	12	0.3	1	0 12
69	16	0.3	1	0 16
70	28	0.3	1	0 28
71	18	0.3	1	0 18
72	38	0.3	1	1 37
73	35	0.3	1	1 34
74	21	0.3	1	0 21
75	8	0.3	1	0 8
76	11	0.3	1	0 11
77	10	0.3	1	0 10
78	16	0.3	1	0 16
79	16	0.3	1	0 16
80	10	0.3	1	0 10
81	20	0.3	1	1 19
82	20	0.3	1	1 19
83	27	0.3	1	1 26
84	25	0.3	1	0 25
85	29	0.3	1	0 29
86	13	0.3	1	0 13
87	20	0.3	1	0 20
88	21	0.3	1	4 17
89	23	0.3	1	1 22
90	23	0.3	1	2 21
91	16	0.3	1	0 16
92	20	0.3	1	1 19
93	16	0.3	1	0 16
94	11	0.3	1	0 11
95	15	0.3	1	0 15
96	27	0.3	1	0 27
97	24	0.3	1	1 23
98	24	0.3	1	1 23
99	20	0.3	1	0 20
100	17	0.3	1	0 17
101	26	0.3	1	0 26
102	26	0.3	1	1 25
103	15	0.3	1	1 14
104	18	0.3	1	0 18
105	18	0.3	1	0 18
106	12	0.3	1	0 12
107	17	0.3	1	0 17
108	20	0.3	1	0 20
109	16	0.3	1	0 16
110	20	0.3	1	0 20
111	16	0.3	1	0 16
112	14	0.3	1	0 14
113	25	0.3	1	0 25
114	14	0.3	1	0 14
115	10	0.3	1	0 10
116	18	0.3	1	0 18
117	28	0.3	1	0 28
118	27	0.3	1	0 27
119	25	0.3	1	0 25
120	18	0.3	1	0 18
121	12	0.3	1	0 12
122	16	0.3	1	0 16
123	17	0.3	1	0 17
124	16	0.3	1	1 15
125	19	0.3	1	1 18
126	14	0.3	1	1 13
127	18	0.3	1	0 18
128	14	0.3	1	0 14
129	24	0.3	1	0 24
130	23	0.3	1	0 23
131	12	0.3	1	0 12
132	21	0.3	1	0 21
133	22	0.3	1	0 22
134	9	0.3	1	0 9
135	12	0.3	1	0 12
136	19	0.3	1	0 19
137	23	0.3	1	0 23
138	26	0.3	1	1 25
139	17	0.3	1	0 17
140	12	0.3	1	0 12
141	27	0.3	1	0 27
142	11	0.3	1	0 11
143	7	0.3	1	0 7
144	3	0.3	1	0 3
145	35	0.3	1	0 35
146	13	0.3	1	0 13
147	51	0.3	1	0 51
148	22	0.3	1	0 22
149	4	0.3	1	0 4
150	10	0.3	1	0 10

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_1.fq.gz
=============================================
20421407 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0002_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0002_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,421,407
Reads with adapters:                     4,045 (0.0%)
Reads written (passing filters):    20,421,407 (100.0%)

Total basepairs processed: 3,036,828,391 bp
Quality-trimmed:               2,967,735 bp (0.1%)
Total written (filtered):  3,033,642,827 bp (99.9%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 4045 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 26.6%
  C: 28.4%
  G: 35.7%
  T: 9.2%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	544	19942.8	0	544
6	79	4985.7	0	79
7	7	1246.4	0	7
9	545	77.9	0	0 545
10	153	19.5	1	1 152
11	134	4.9	1	0 134
12	37	1.2	1	0 37
13	17	0.3	1	1 16
14	14	0.3	1	0 14
15	22	0.3	1	1 21
16	11	0.3	1	0 11
17	12	0.3	1	1 11
18	14	0.3	1	0 14
19	23	0.3	1	3 20
20	10	0.3	1	0 10
21	9	0.3	1	0 9
22	22	0.3	1	0 22
23	22	0.3	1	4 18
24	21	0.3	1	1 20
25	30	0.3	1	1 29
26	19	0.3	1	1 18
27	26	0.3	1	2 24
28	20	0.3	1	1 19
29	18	0.3	1	3 15
30	12	0.3	1	0 12
31	13	0.3	1	0 13
32	21	0.3	1	0 21
33	11	0.3	1	3 8
34	21	0.3	1	2 19
35	23	0.3	1	2 21
36	11	0.3	1	0 11
37	20	0.3	1	2 18
38	16	0.3	1	0 16
39	18	0.3	1	4 14
40	19	0.3	1	1 18
41	15	0.3	1	0 15
42	25	0.3	1	0 25
43	20	0.3	1	1 19
44	16	0.3	1	1 15
45	11	0.3	1	0 11
46	13	0.3	1	0 13
47	20	0.3	1	1 19
48	15	0.3	1	3 12
49	17	0.3	1	2 15
50	21	0.3	1	1 20
51	16	0.3	1	0 16
52	37	0.3	1	0 37
53	24	0.3	1	0 24
54	18	0.3	1	0 18
55	19	0.3	1	0 19
56	24	0.3	1	1 23
57	12	0.3	1	0 12
58	13	0.3	1	0 13
59	19	0.3	1	0 19
60	25	0.3	1	0 25
61	22	0.3	1	0 22
62	23	0.3	1	0 23
63	16	0.3	1	1 15
64	19	0.3	1	1 18
65	15	0.3	1	1 14
66	21	0.3	1	1 20
67	33	0.3	1	0 33
68	13	0.3	1	1 12
69	10	0.3	1	1 9
70	29	0.3	1	0 29
71	14	0.3	1	0 14
72	30	0.3	1	0 30
73	26	0.3	1	0 26
74	16	0.3	1	1 15
75	15	0.3	1	2 13
76	18	0.3	1	0 18
77	17	0.3	1	1 16
78	20	0.3	1	0 20
79	15	0.3	1	1 14
80	12	0.3	1	0 12
81	26	0.3	1	0 26
82	13	0.3	1	1 12
83	17	0.3	1	0 17
84	22	0.3	1	0 22
85	20	0.3	1	0 20
86	26	0.3	1	1 25
87	16	0.3	1	0 16
88	30	0.3	1	3 27
89	17	0.3	1	1 16
90	17	0.3	1	2 15
91	20	0.3	1	1 19
92	27	0.3	1	1 26
93	25	0.3	1	0 25
94	13	0.3	1	0 13
95	22	0.3	1	0 22
96	28	0.3	1	1 27
97	15	0.3	1	0 15
98	17	0.3	1	0 17
99	23	0.3	1	1 22
100	12	0.3	1	0 12
101	24	0.3	1	0 24
102	46	0.3	1	1 45
103	22	0.3	1	1 21
104	12	0.3	1	0 12
105	17	0.3	1	0 17
106	6	0.3	1	0 6
107	16	0.3	1	0 16
108	14	0.3	1	0 14
109	8	0.3	1	0 8
110	12	0.3	1	0 12
111	16	0.3	1	0 16
112	16	0.3	1	0 16
113	23	0.3	1	0 23
114	18	0.3	1	0 18
115	16	0.3	1	0 16
116	12	0.3	1	0 12
117	21	0.3	1	0 21
118	35	0.3	1	1 34
119	20	0.3	1	0 20
120	21	0.3	1	0 21
121	10	0.3	1	0 10
122	17	0.3	1	0 17
123	15	0.3	1	1 14
124	20	0.3	1	0 20
125	17	0.3	1	0 17
126	11	0.3	1	0 11
127	20	0.3	1	0 20
128	13	0.3	1	0 13
129	21	0.3	1	0 21
130	16	0.3	1	0 16
131	24	0.3	1	0 24
132	8	0.3	1	0 8
133	25	0.3	1	0 25
134	10	0.3	1	1 9
135	16	0.3	1	0 16
136	11	0.3	1	0 11
137	19	0.3	1	0 19
138	15	0.3	1	0 15
139	17	0.3	1	0 17
140	20	0.3	1	0 20
141	41	0.3	1	0 41
142	9	0.3	1	0 9
143	17	0.3	1	0 17
144	2	0.3	1	0 2
145	24	0.3	1	0 24
146	11	0.3	1	0 11
147	45	0.3	1	0 45
148	21	0.3	1	0 21
149	6	0.3	1	0 6
150	5	0.3	1	0 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0002_good_2.fq.gz
=============================================
20421407 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0002_good_1_trimmed.fq.gz and Unknown_CK259-004T0002_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0002_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0002_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0002_good_1_trimmed.fq.gz and Unknown_CK259-004T0002_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0002_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0002_good_2_val_2.fq.gz

Total number of sequences analysed: 20421407

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 21063 (0.10%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 6891 (0.03%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0002_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0002_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0002_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0002_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0002_good_1_trimmed.fq.gz and Unknown_CK259-004T0002_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0003_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Illumina	1	AGATCGGAAGAGC	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 2, count Nextera: 2, count Illumina: 1)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior).
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0003_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0003_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,435,036
Reads with adapters:                    39,007 (0.2%)
Reads written (passing filters):    20,435,036 (100.0%)

Total basepairs processed: 3,044,382,574 bp
Quality-trimmed:                 824,031 bp (0.0%)
Total written (filtered):  3,042,723,565 bp (99.9%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 39007 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 19.0%
  C: 23.4%
  G: 24.9%
  T: 32.5%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	20831	19956.1	0	20831
6	6039	4989.0	0	6039
7	2032	1247.3	0	2032
8	849	311.8	0	849
9	405	78.0	0	107 298
10	478	19.5	1	6 472
11	228	4.9	1	2 226
12	84	1.2	1	0 84
13	63	1.2	1	0 63
14	71	1.2	1	0 71
15	58	1.2	1	0 58
16	31	1.2	1	0 31
17	70	1.2	1	0 70
18	42	1.2	1	0 42
19	40	1.2	1	0 40
20	29	1.2	1	0 29
21	46	1.2	1	0 46
22	42	1.2	1	0 42
23	45	1.2	1	0 45
24	44	1.2	1	0 44
25	55	1.2	1	1 54
26	53	1.2	1	0 53
27	51	1.2	1	0 51
28	46	1.2	1	1 45
29	47	1.2	1	0 47
30	69	1.2	1	1 68
31	86	1.2	1	2 84
32	65	1.2	1	3 62
33	44	1.2	1	0 44
34	97	1.2	1	0 97
35	61	1.2	1	0 61
36	51	1.2	1	0 51
37	34	1.2	1	0 34
38	40	1.2	1	0 40
39	50	1.2	1	3 47
40	39	1.2	1	0 39
41	47	1.2	1	0 47
42	44	1.2	1	0 44
43	41	1.2	1	1 40
44	34	1.2	1	0 34
45	110	1.2	1	0 110
46	96	1.2	1	0 96
47	41	1.2	1	0 41
48	56	1.2	1	1 55
49	38	1.2	1	0 38
50	29	1.2	1	0 29
51	46	1.2	1	1 45
52	55	1.2	1	0 55
53	118	1.2	1	0 118
54	49	1.2	1	2 47
55	83	1.2	1	0 83
56	42	1.2	1	0 42
57	41	1.2	1	0 41
58	26	1.2	1	0 26
59	40	1.2	1	0 40
60	37	1.2	1	0 37
61	46	1.2	1	0 46
62	72	1.2	1	0 72
63	73	1.2	1	0 73
64	92	1.2	1	0 92
65	68	1.2	1	1 67
66	68	1.2	1	0 68
67	84	1.2	1	0 84
68	51	1.2	1	0 51
69	49	1.2	1	0 49
70	54	1.2	1	0 54
71	38	1.2	1	0 38
72	60	1.2	1	0 60
73	56	1.2	1	0 56
74	67	1.2	1	0 67
75	81	1.2	1	1 80
76	129	1.2	1	1 128
77	71	1.2	1	0 71
78	117	1.2	1	0 117
79	56	1.2	1	0 56
80	82	1.2	1	0 82
81	131	1.2	1	0 131
82	75	1.2	1	0 75
83	60	1.2	1	0 60
84	42	1.2	1	1 41
85	51	1.2	1	0 51
86	112	1.2	1	0 112
87	63	1.2	1	0 63
88	48	1.2	1	0 48
89	50	1.2	1	0 50
90	44	1.2	1	0 44
91	71	1.2	1	0 71
92	48	1.2	1	0 48
93	72	1.2	1	0 72
94	52	1.2	1	0 52
95	60	1.2	1	0 60
96	60	1.2	1	0 60
97	88	1.2	1	0 88
98	48	1.2	1	0 48
99	53	1.2	1	0 53
100	51	1.2	1	1 50
101	47	1.2	1	0 47
102	95	1.2	1	0 95
103	41	1.2	1	0 41
104	46	1.2	1	0 46
105	67	1.2	1	0 67
106	55	1.2	1	1 54
107	62	1.2	1	0 62
108	62	1.2	1	0 62
109	36	1.2	1	0 36
110	47	1.2	1	0 47
111	38	1.2	1	0 38
112	71	1.2	1	0 71
113	22	1.2	1	0 22
114	68	1.2	1	0 68
115	60	1.2	1	0 60
116	48	1.2	1	0 48
117	57	1.2	1	0 57
118	103	1.2	1	1 102
119	68	1.2	1	0 68
120	56	1.2	1	0 56
121	52	1.2	1	0 52
122	39	1.2	1	0 39
123	93	1.2	1	0 93
124	59	1.2	1	1 58
125	42	1.2	1	0 42
126	55	1.2	1	0 55
127	87	1.2	1	1 86
128	65	1.2	1	0 65
129	52	1.2	1	2 50
130	57	1.2	1	1 56
131	46	1.2	1	0 46
132	61	1.2	1	0 61
133	70	1.2	1	0 70
134	51	1.2	1	0 51
135	83	1.2	1	0 83
136	56	1.2	1	0 56
137	40	1.2	1	3 37
138	50	1.2	1	0 50
139	68	1.2	1	0 68
140	65	1.2	1	0 65
141	42	1.2	1	0 42
142	54	1.2	1	0 54
143	50	1.2	1	0 50
144	54	1.2	1	0 54
145	39	1.2	1	0 39
146	29	1.2	1	0 29
147	46	1.2	1	1 45
148	35	1.2	1	0 35
149	84	1.2	1	0 84
150	53	1.2	1	0 53

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_1.fq.gz
=============================================
20435036 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0003_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0003_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,435,036
Reads with adapters:                    35,281 (0.2%)
Reads written (passing filters):    20,435,036 (100.0%)

Total basepairs processed: 3,043,932,072 bp
Quality-trimmed:               1,952,730 bp (0.1%)
Total written (filtered):  3,041,247,968 bp (99.9%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 35281 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 20.0%
  C: 24.0%
  G: 25.8%
  T: 30.1%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	19556	19956.1	0	19556
6	5365	4989.0	0	5365
7	1654	1247.3	0	1654
8	639	311.8	0	639
9	375	78.0	0	93 282
10	442	19.5	1	9 433
11	216	4.9	1	2 214
12	64	1.2	1	0 64
13	53	1.2	1	0 53
14	58	1.2	1	1 57
15	43	1.2	1	0 43
16	34	1.2	1	0 34
17	53	1.2	1	0 53
18	31	1.2	1	0 31
19	44	1.2	1	0 44
20	38	1.2	1	0 38
21	33	1.2	1	0 33
22	41	1.2	1	0 41
23	40	1.2	1	0 40
24	32	1.2	1	0 32
25	57	1.2	1	1 56
26	42	1.2	1	1 41
27	46	1.2	1	0 46
28	39	1.2	1	0 39
29	36	1.2	1	0 36
30	54	1.2	1	0 54
31	64	1.2	1	0 64
32	36	1.2	1	1 35
33	54	1.2	1	1 53
34	75	1.2	1	0 75
35	57	1.2	1	0 57
36	37	1.2	1	0 37
37	38	1.2	1	0 38
38	27	1.2	1	0 27
39	41	1.2	1	2 39
40	41	1.2	1	0 41
41	37	1.2	1	1 36
42	42	1.2	1	0 42
43	25	1.2	1	0 25
44	31	1.2	1	0 31
45	99	1.2	1	0 99
46	75	1.2	1	1 74
47	41	1.2	1	0 41
48	47	1.2	1	0 47
49	51	1.2	1	0 51
50	38	1.2	1	0 38
51	39	1.2	1	1 38
52	54	1.2	1	0 54
53	103	1.2	1	0 103
54	44	1.2	1	0 44
55	86	1.2	1	1 85
56	39	1.2	1	0 39
57	43	1.2	1	0 43
58	30	1.2	1	0 30
59	28	1.2	1	0 28
60	34	1.2	1	0 34
61	47	1.2	1	1 46
62	47	1.2	1	0 47
63	66	1.2	1	0 66
64	70	1.2	1	0 70
65	46	1.2	1	0 46
66	38	1.2	1	0 38
67	61	1.2	1	0 61
68	47	1.2	1	0 47
69	48	1.2	1	0 48
70	57	1.2	1	0 57
71	44	1.2	1	1 43
72	45	1.2	1	0 45
73	58	1.2	1	0 58
74	60	1.2	1	0 60
75	79	1.2	1	1 78
76	93	1.2	1	0 93
77	55	1.2	1	0 55
78	101	1.2	1	0 101
79	42	1.2	1	0 42
80	78	1.2	1	2 76
81	107	1.2	1	0 107
82	67	1.2	1	2 65
83	50	1.2	1	0 50
84	52	1.2	1	2 50
85	37	1.2	1	0 37
86	86	1.2	1	0 86
87	56	1.2	1	0 56
88	30	1.2	1	0 30
89	40	1.2	1	0 40
90	53	1.2	1	0 53
91	53	1.2	1	0 53
92	51	1.2	1	0 51
93	102	1.2	1	1 101
94	46	1.2	1	0 46
95	61	1.2	1	0 61
96	45	1.2	1	0 45
97	69	1.2	1	0 69
98	40	1.2	1	0 40
99	42	1.2	1	0 42
100	38	1.2	1	0 38
101	42	1.2	1	0 42
102	59	1.2	1	0 59
103	34	1.2	1	0 34
104	46	1.2	1	0 46
105	29	1.2	1	0 29
106	52	1.2	1	1 51
107	77	1.2	1	0 77
108	41	1.2	1	1 40
109	34	1.2	1	0 34
110	50	1.2	1	0 50
111	31	1.2	1	0 31
112	61	1.2	1	0 61
113	26	1.2	1	0 26
114	62	1.2	1	0 62
115	29	1.2	1	1 28
116	26	1.2	1	0 26
117	43	1.2	1	0 43
118	73	1.2	1	0 73
119	52	1.2	1	0 52
120	70	1.2	1	2 68
121	44	1.2	1	0 44
122	54	1.2	1	0 54
123	58	1.2	1	0 58
124	60	1.2	1	0 60
125	38	1.2	1	0 38
126	47	1.2	1	0 47
127	66	1.2	1	0 66
128	69	1.2	1	0 69
129	44	1.2	1	0 44
130	47	1.2	1	0 47
131	35	1.2	1	0 35
132	43	1.2	1	0 43
133	63	1.2	1	0 63
134	58	1.2	1	0 58
135	78	1.2	1	0 78
136	48	1.2	1	1 47
137	50	1.2	1	1 49
138	35	1.2	1	0 35
139	39	1.2	1	0 39
140	60	1.2	1	0 60
141	42	1.2	1	0 42
142	55	1.2	1	0 55
143	74	1.2	1	0 74
144	29	1.2	1	0 29
145	54	1.2	1	0 54
146	24	1.2	1	0 24
147	39	1.2	1	0 39
148	23	1.2	1	0 23
149	76	1.2	1	0 76
150	44	1.2	1	0 44

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0003_good_2.fq.gz
=============================================
20435036 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0003_good_1_trimmed.fq.gz and Unknown_CK259-004T0003_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0003_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0003_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0003_good_1_trimmed.fq.gz and Unknown_CK259-004T0003_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0003_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0003_good_2_val_2.fq.gz

Total number of sequences analysed: 20435036

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 14889 (0.07%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3731 (0.02%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0003_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0003_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0003_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0003_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0003_good_1_trimmed.fq.gz and Unknown_CK259-004T0003_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0004_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	2	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count Illumina: 2, count Nextera: 2, count smallRNA: 1)
Defaulting to Illumina universal adapter ( AGATCGGAAGAGC ). Specify -a SEQUENCE to avoid this behavior).

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0004_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0004_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,968,340
Reads with adapters:                     3,683 (0.0%)
Reads written (passing filters):    19,968,340 (100.0%)

Total basepairs processed: 2,973,606,603 bp
Quality-trimmed:               1,021,842 bp (0.0%)
Total written (filtered):  2,972,367,975 bp (100.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3683 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 25.8%
  C: 28.6%
  G: 36.5%
  T: 8.8%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	366	19500.3	0	366
6	43	4875.1	0	43
7	1	1218.8	0	1
9	445	76.2	0	0 445
10	143	19.0	1	0 143
11	85	4.8	1	0 85
12	25	1.2	1	0 25
13	15	0.3	1	0 15
14	13	0.3	1	0 13
15	14	0.3	1	0 14
16	12	0.3	1	0 12
17	15	0.3	1	1 14
18	13	0.3	1	0 13
19	9	0.3	1	0 9
20	12	0.3	1	2 10
21	20	0.3	1	0 20
22	14	0.3	1	2 12
23	27	0.3	1	0 27
24	25	0.3	1	0 25
25	31	0.3	1	2 29
26	24	0.3	1	0 24
27	15	0.3	1	1 14
28	25	0.3	1	1 24
29	20	0.3	1	0 20
30	27	0.3	1	0 27
31	8	0.3	1	1 7
32	9	0.3	1	0 9
33	6	0.3	1	0 6
34	18	0.3	1	1 17
35	18	0.3	1	0 18
36	27	0.3	1	0 27
37	24	0.3	1	0 24
38	18	0.3	1	1 17
39	28	0.3	1	1 27
40	20	0.3	1	0 20
41	10	0.3	1	0 10
42	17	0.3	1	0 17
43	13	0.3	1	0 13
44	13	0.3	1	1 12
45	20	0.3	1	2 18
46	11	0.3	1	0 11
47	9	0.3	1	2 7
48	14	0.3	1	0 14
49	29	0.3	1	2 27
50	16	0.3	1	0 16
51	19	0.3	1	3 16
52	22	0.3	1	0 22
53	45	0.3	1	0 45
54	24	0.3	1	0 24
55	16	0.3	1	0 16
56	23	0.3	1	1 22
57	15	0.3	1	0 15
58	10	0.3	1	0 10
59	20	0.3	1	0 20
60	28	0.3	1	0 28
61	17	0.3	1	1 16
62	27	0.3	1	0 27
63	29	0.3	1	0 29
64	22	0.3	1	0 22
65	18	0.3	1	0 18
66	27	0.3	1	0 27
67	27	0.3	1	0 27
68	13	0.3	1	0 13
69	12	0.3	1	0 12
70	19	0.3	1	0 19
71	19	0.3	1	1 18
72	26	0.3	1	0 26
73	32	0.3	1	0 32
74	25	0.3	1	0 25
75	17	0.3	1	0 17
76	14	0.3	1	0 14
77	13	0.3	1	0 13
78	20	0.3	1	0 20
79	15	0.3	1	1 14
80	21	0.3	1	0 21
81	24	0.3	1	0 24
82	11	0.3	1	0 11
83	21	0.3	1	0 21
84	29	0.3	1	0 29
85	19	0.3	1	1 18
86	18	0.3	1	4 14
87	18	0.3	1	0 18
88	21	0.3	1	3 18
89	25	0.3	1	1 24
90	28	0.3	1	4 24
91	20	0.3	1	1 19
92	27	0.3	1	0 27
93	22	0.3	1	0 22
94	20	0.3	1	0 20
95	21	0.3	1	1 20
96	17	0.3	1	0 17
97	15	0.3	1	0 15
98	16	0.3	1	0 16
99	15	0.3	1	0 15
100	15	0.3	1	0 15
101	22	0.3	1	1 21
102	31	0.3	1	1 30
103	21	0.3	1	1 20
104	11	0.3	1	0 11
105	17	0.3	1	0 17
106	8	0.3	1	0 8
107	17	0.3	1	1 16
108	16	0.3	1	1 15
109	16	0.3	1	0 16
110	13	0.3	1	0 13
111	25	0.3	1	0 25
112	14	0.3	1	0 14
113	24	0.3	1	0 24
114	18	0.3	1	0 18
115	8	0.3	1	0 8
116	16	0.3	1	0 16
117	15	0.3	1	0 15
118	13	0.3	1	0 13
119	23	0.3	1	0 23
120	14	0.3	1	0 14
121	15	0.3	1	0 15
122	30	0.3	1	0 30
123	15	0.3	1	0 15
124	21	0.3	1	0 21
125	23	0.3	1	0 23
126	17	0.3	1	0 17
127	22	0.3	1	0 22
128	32	0.3	1	0 32
129	20	0.3	1	0 20
130	28	0.3	1	0 28
131	12	0.3	1	1 11
132	13	0.3	1	0 13
133	19	0.3	1	0 19
134	12	0.3	1	0 12
135	14	0.3	1	0 14
136	13	0.3	1	0 13
137	26	0.3	1	0 26
138	23	0.3	1	0 23
139	10	0.3	1	0 10
140	12	0.3	1	0 12
141	42	0.3	1	1 41
142	11	0.3	1	0 11
143	12	0.3	1	0 12
144	12	0.3	1	0 12
145	12	0.3	1	0 12
146	9	0.3	1	0 9
147	33	0.3	1	0 33
148	11	0.3	1	0 11
149	13	0.3	1	0 13
150	5	0.3	1	0 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_1.fq.gz
=============================================
19968340 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0004_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0004_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,968,340
Reads with adapters:                     3,749 (0.0%)
Reads written (passing filters):    19,968,340 (100.0%)

Total basepairs processed: 2,973,173,237 bp
Quality-trimmed:               2,308,364 bp (0.1%)
Total written (filtered):  2,970,660,937 bp (99.9%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3749 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 26.2%
  C: 28.4%
  G: 36.8%
  T: 8.1%
  none/other: 0.4%

Overview of removed sequences
length	count	expect	max.err	error counts
5	428	19500.3	0	428
6	86	4875.1	0	86
7	4	1218.8	0	4
8	1	304.7	0	1
9	495	76.2	0	1 494
10	196	19.0	1	1 195
11	115	4.8	1	0 115
12	26	1.2	1	1 25
13	24	0.3	1	0 24
14	17	0.3	1	0 17
15	19	0.3	1	0 19
16	9	0.3	1	0 9
17	6	0.3	1	0 6
18	9	0.3	1	0 9
19	23	0.3	1	2 21
20	16	0.3	1	0 16
21	15	0.3	1	0 15
22	7	0.3	1	0 7
23	23	0.3	1	3 20
24	21	0.3	1	0 21
25	27	0.3	1	1 26
26	19	0.3	1	0 19
27	19	0.3	1	0 19
28	19	0.3	1	2 17
29	14	0.3	1	0 14
30	15	0.3	1	2 13
31	10	0.3	1	0 10
32	11	0.3	1	2 9
33	13	0.3	1	0 13
34	6	0.3	1	1 5
35	24	0.3	1	0 24
36	22	0.3	1	1 21
37	23	0.3	1	0 23
38	14	0.3	1	0 14
39	19	0.3	1	3 16
40	21	0.3	1	1 20
41	17	0.3	1	1 16
42	13	0.3	1	0 13
43	17	0.3	1	2 15
44	8	0.3	1	1 7
45	25	0.3	1	1 24
46	12	0.3	1	0 12
47	7	0.3	1	0 7
48	6	0.3	1	1 5
49	16	0.3	1	0 16
50	15	0.3	1	0 15
51	15	0.3	1	0 15
52	37	0.3	1	0 37
53	20	0.3	1	0 20
54	23	0.3	1	1 22
55	8	0.3	1	0 8
56	15	0.3	1	1 14
57	18	0.3	1	0 18
58	13	0.3	1	1 12
59	25	0.3	1	0 25
60	29	0.3	1	0 29
61	26	0.3	1	0 26
62	33	0.3	1	0 33
63	24	0.3	1	0 24
64	21	0.3	1	0 21
65	15	0.3	1	0 15
66	13	0.3	1	0 13
67	22	0.3	1	0 22
68	17	0.3	1	0 17
69	13	0.3	1	0 13
70	22	0.3	1	0 22
71	14	0.3	1	0 14
72	26	0.3	1	1 25
73	41	0.3	1	3 38
74	23	0.3	1	0 23
75	20	0.3	1	0 20
76	9	0.3	1	0 9
77	19	0.3	1	0 19
78	19	0.3	1	0 19
79	14	0.3	1	0 14
80	21	0.3	1	1 20
81	21	0.3	1	1 20
82	10	0.3	1	0 10
83	20	0.3	1	0 20
84	15	0.3	1	0 15
85	22	0.3	1	3 19
86	26	0.3	1	4 22
87	18	0.3	1	0 18
88	23	0.3	1	4 19
89	25	0.3	1	1 24
90	30	0.3	1	4 26
91	13	0.3	1	3 10
92	11	0.3	1	0 11
93	21	0.3	1	0 21
94	17	0.3	1	0 17
95	18	0.3	1	1 17
96	34	0.3	1	0 34
97	23	0.3	1	0 23
98	18	0.3	1	0 18
99	11	0.3	1	1 10
100	13	0.3	1	0 13
101	24	0.3	1	3 21
102	17	0.3	1	1 16
103	24	0.3	1	1 23
104	13	0.3	1	0 13
105	14	0.3	1	0 14
106	7	0.3	1	2 5
107	15	0.3	1	0 15
108	13	0.3	1	1 12
109	12	0.3	1	0 12
110	11	0.3	1	0 11
111	16	0.3	1	0 16
112	22	0.3	1	0 22
113	16	0.3	1	0 16
114	19	0.3	1	0 19
115	11	0.3	1	0 11
116	16	0.3	1	0 16
117	16	0.3	1	0 16
118	15	0.3	1	0 15
119	19	0.3	1	0 19
120	17	0.3	1	0 17
121	12	0.3	1	0 12
122	16	0.3	1	0 16
123	17	0.3	1	0 17
124	20	0.3	1	0 20
125	19	0.3	1	0 19
126	10	0.3	1	1 9
127	15	0.3	1	0 15
128	12	0.3	1	0 12
129	30	0.3	1	0 30
130	17	0.3	1	0 17
131	15	0.3	1	0 15
132	11	0.3	1	0 11
133	16	0.3	1	1 15
134	16	0.3	1	0 16
135	13	0.3	1	0 13
136	20	0.3	1	0 20
137	26	0.3	1	0 26
138	17	0.3	1	0 17
139	12	0.3	1	0 12
140	10	0.3	1	0 10
141	32	0.3	1	0 32
142	7	0.3	1	0 7
143	11	0.3	1	0 11
144	2	0.3	1	0 2
145	18	0.3	1	0 18
146	19	0.3	1	0 19
147	32	0.3	1	0 32
148	14	0.3	1	0 14
149	8	0.3	1	0 8
150	9	0.3	1	0 9

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0004_good_2.fq.gz
=============================================
19968340 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0004_good_1_trimmed.fq.gz and Unknown_CK259-004T0004_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0004_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0004_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0004_good_1_trimmed.fq.gz and Unknown_CK259-004T0004_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0004_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0004_good_2_val_2.fq.gz

Total number of sequences analysed: 19968340

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 15583 (0.08%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 4977 (0.02%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0004_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0004_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0004_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0004_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0004_good_1_trimmed.fq.gz and Unknown_CK259-004T0004_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0005_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	2	AGATCGGAAGAGC	1000000	0.00
Nextera	1	CTGTCTCTTATA	1000000	0.00
smallRNA	0	TGGAATTCTCGG	1000000	0.00
Using Illumina adapter for trimming (count: 2). Second best hit was Nextera (count: 1)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0005_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0005_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,993,393
Reads with adapters:                     4,247 (0.0%)
Reads written (passing filters):    23,993,393 (100.0%)

Total basepairs processed: 3,569,764,893 bp
Quality-trimmed:                  40,662 bp (0.0%)
Total written (filtered):  3,569,477,281 bp (100.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 4247 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 25.1%
  C: 28.3%
  G: 36.0%
  T: 10.4%
  none/other: 0.3%

Overview of removed sequences
length	count	expect	max.err	error counts
5	420	23431.0	0	420
6	45	5857.8	0	45
7	2	1464.4	0	2
9	620	91.5	0	0 620
10	181	22.9	1	0 181
11	100	5.7	1	0 100
12	42	1.4	1	0 42
13	26	0.4	1	0 26
14	10	0.4	1	0 10
15	16	0.4	1	0 16
16	9	0.4	1	0 9
17	22	0.4	1	0 22
18	18	0.4	1	0 18
19	19	0.4	1	0 19
20	14	0.4	1	0 14
21	18	0.4	1	0 18
22	24	0.4	1	0 24
23	54	0.4	1	0 54
24	24	0.4	1	0 24
25	34	0.4	1	0 34
26	22	0.4	1	1 21
27	27	0.4	1	1 26
28	18	0.4	1	0 18
29	16	0.4	1	0 16
30	13	0.4	1	0 13
31	9	0.4	1	0 9
32	13	0.4	1	0 13
33	13	0.4	1	0 13
34	13	0.4	1	0 13
35	10	0.4	1	0 10
36	21	0.4	1	0 21
37	24	0.4	1	0 24
38	11	0.4	1	2 9
39	12	0.4	1	0 12
40	18	0.4	1	0 18
41	11	0.4	1	0 11
42	18	0.4	1	0 18
43	14	0.4	1	0 14
44	23	0.4	1	0 23
45	14	0.4	1	0 14
46	12	0.4	1	0 12
47	14	0.4	1	0 14
48	14	0.4	1	0 14
49	27	0.4	1	0 27
50	11	0.4	1	0 11
51	12	0.4	1	0 12
52	29	0.4	1	0 29
53	33	0.4	1	0 33
54	20	0.4	1	0 20
55	18	0.4	1	0 18
56	26	0.4	1	0 26
57	27	0.4	1	0 27
58	13	0.4	1	0 13
59	15	0.4	1	0 15
60	30	0.4	1	1 29
61	12	0.4	1	1 11
62	30	0.4	1	0 30
63	26	0.4	1	0 26
64	25	0.4	1	0 25
65	21	0.4	1	0 21
66	18	0.4	1	0 18
67	41	0.4	1	0 41
68	16	0.4	1	1 15
69	8	0.4	1	1 7
70	37	0.4	1	0 37
71	30	0.4	1	1 29
72	21	0.4	1	0 21
73	34	0.4	1	0 34
74	14	0.4	1	1 13
75	17	0.4	1	0 17
76	9	0.4	1	0 9
77	14	0.4	1	1 13
78	21	0.4	1	0 21
79	18	0.4	1	1 17
80	20	0.4	1	0 20
81	25	0.4	1	1 24
82	16	0.4	1	0 16
83	24	0.4	1	0 24
84	29	0.4	1	0 29
85	26	0.4	1	1 25
86	28	0.4	1	2 26
87	23	0.4	1	2 21
88	29	0.4	1	3 26
89	36	0.4	1	2 34
90	14	0.4	1	2 12
91	27	0.4	1	0 27
92	24	0.4	1	3 21
93	20	0.4	1	3 17
94	23	0.4	1	3 20
95	17	0.4	1	1 16
96	33	0.4	1	3 30
97	24	0.4	1	2 22
98	23	0.4	1	0 23
99	15	0.4	1	0 15
100	12	0.4	1	1 11
101	24	0.4	1	0 24
102	24	0.4	1	0 24
103	24	0.4	1	0 24
104	16	0.4	1	0 16
105	13	0.4	1	0 13
106	20	0.4	1	2 18
107	10	0.4	1	0 10
108	23	0.4	1	0 23
109	14	0.4	1	1 13
110	17	0.4	1	0 17
111	22	0.4	1	0 22
112	17	0.4	1	1 16
113	33	0.4	1	0 33
114	23	0.4	1	0 23
115	6	0.4	1	0 6
116	18	0.4	1	0 18
117	16	0.4	1	0 16
118	33	0.4	1	0 33
119	15	0.4	1	0 15
120	12	0.4	1	0 12
121	19	0.4	1	0 19
122	14	0.4	1	0 14
123	20	0.4	1	0 20
124	24	0.4	1	0 24
125	23	0.4	1	0 23
126	16	0.4	1	0 16
127	14	0.4	1	0 14
128	19	0.4	1	1 18
129	24	0.4	1	0 24
130	33	0.4	1	0 33
131	20	0.4	1	0 20
132	14	0.4	1	0 14
133	28	0.4	1	0 28
134	11	0.4	1	0 11
135	8	0.4	1	0 8
136	17	0.4	1	0 17
137	34	0.4	1	0 34
138	29	0.4	1	0 29
139	17	0.4	1	0 17
140	11	0.4	1	0 11
141	60	0.4	1	0 60
142	27	0.4	1	0 27
143	18	0.4	1	0 18
144	13	0.4	1	0 13
145	28	0.4	1	0 28
146	21	0.4	1	0 21
147	48	0.4	1	0 48
148	13	0.4	1	0 13
149	17	0.4	1	0 17
150	8	0.4	1	0 8

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_1.fq.gz
=============================================
23993393 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0005_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0005_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,993,393
Reads with adapters:                     4,358 (0.0%)
Reads written (passing filters):    23,993,393 (100.0%)

Total basepairs processed: 3,569,499,312 bp
Quality-trimmed:                 145,650 bp (0.0%)
Total written (filtered):  3,569,108,011 bp (100.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 4358 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 26.3%
  C: 27.0%
  G: 36.6%
  T: 9.8%
  none/other: 0.3%

Overview of removed sequences
length	count	expect	max.err	error counts
5	518	23431.0	0	518
6	81	5857.8	0	81
7	5	1464.4	0	5
9	609	91.5	0	0 609
10	164	22.9	1	0 164
11	136	5.7	1	0 136
12	40	1.4	1	0 40
13	18	0.4	1	0 18
14	16	0.4	1	0 16
15	12	0.4	1	0 12
16	13	0.4	1	0 13
17	14	0.4	1	0 14
18	18	0.4	1	0 18
19	17	0.4	1	0 17
20	9	0.4	1	0 9
21	15	0.4	1	0 15
22	12	0.4	1	0 12
23	24	0.4	1	0 24
24	28	0.4	1	0 28
25	31	0.4	1	0 31
26	19	0.4	1	0 19
27	19	0.4	1	0 19
28	19	0.4	1	0 19
29	14	0.4	1	1 13
30	25	0.4	1	0 25
31	11	0.4	1	0 11
32	13	0.4	1	0 13
33	8	0.4	1	1 7
34	13	0.4	1	0 13
35	17	0.4	1	0 17
36	8	0.4	1	0 8
37	37	0.4	1	0 37
38	15	0.4	1	1 14
39	18	0.4	1	0 18
40	25	0.4	1	0 25
41	12	0.4	1	0 12
42	20	0.4	1	0 20
43	12	0.4	1	0 12
44	17	0.4	1	0 17
45	13	0.4	1	0 13
46	15	0.4	1	0 15
47	14	0.4	1	0 14
48	8	0.4	1	1 7
49	19	0.4	1	0 19
50	11	0.4	1	0 11
51	24	0.4	1	1 23
52	32	0.4	1	0 32
53	28	0.4	1	0 28
54	22	0.4	1	0 22
55	30	0.4	1	0 30
56	26	0.4	1	0 26
57	26	0.4	1	0 26
58	19	0.4	1	2 17
59	21	0.4	1	0 21
60	37	0.4	1	0 37
61	13	0.4	1	0 13
62	33	0.4	1	0 33
63	36	0.4	1	0 36
64	22	0.4	1	0 22
65	20	0.4	1	0 20
66	12	0.4	1	0 12
67	33	0.4	1	0 33
68	10	0.4	1	0 10
69	16	0.4	1	0 16
70	33	0.4	1	0 33
71	22	0.4	1	0 22
72	27	0.4	1	1 26
73	41	0.4	1	0 41
74	21	0.4	1	0 21
75	11	0.4	1	0 11
76	10	0.4	1	1 9
77	16	0.4	1	1 15
78	16	0.4	1	1 15
79	20	0.4	1	0 20
80	16	0.4	1	0 16
81	32	0.4	1	0 32
82	21	0.4	1	0 21
83	21	0.4	1	0 21
84	28	0.4	1	0 28
85	14	0.4	1	3 11
86	35	0.4	1	2 33
87	18	0.4	1	2 16
88	31	0.4	1	1 30
89	28	0.4	1	3 25
90	26	0.4	1	2 24
91	27	0.4	1	3 24
92	30	0.4	1	3 27
93	24	0.4	1	7 17
94	23	0.4	1	3 20
95	36	0.4	1	1 35
96	25	0.4	1	0 25
97	30	0.4	1	3 27
98	25	0.4	1	0 25
99	6	0.4	1	0 6
100	18	0.4	1	1 17
101	18	0.4	1	2 16
102	36	0.4	1	0 36
103	25	0.4	1	1 24
104	9	0.4	1	0 9
105	17	0.4	1	0 17
106	13	0.4	1	2 11
107	18	0.4	1	0 18
108	21	0.4	1	0 21
109	13	0.4	1	0 13
110	18	0.4	1	0 18
111	10	0.4	1	0 10
112	22	0.4	1	1 21
113	22	0.4	1	0 22
114	20	0.4	1	1 19
115	18	0.4	1	0 18
116	17	0.4	1	0 17
117	22	0.4	1	0 22
118	27	0.4	1	0 27
119	20	0.4	1	0 20
120	14	0.4	1	0 14
121	21	0.4	1	0 21
122	21	0.4	1	0 21
123	22	0.4	1	0 22
124	20	0.4	1	0 20
125	12	0.4	1	0 12
126	8	0.4	1	0 8
127	18	0.4	1	0 18
128	17	0.4	1	1 16
129	19	0.4	1	1 18
130	28	0.4	1	0 28
131	14	0.4	1	0 14
132	21	0.4	1	0 21
133	17	0.4	1	0 17
134	11	0.4	1	0 11
135	18	0.4	1	0 18
136	9	0.4	1	0 9
137	32	0.4	1	0 32
138	25	0.4	1	0 25
139	14	0.4	1	0 14
140	22	0.4	1	0 22
141	53	0.4	1	0 53
142	14	0.4	1	0 14
143	20	0.4	1	0 20
144	5	0.4	1	0 5
145	31	0.4	1	0 31
146	28	0.4	1	0 28
147	46	0.4	1	0 46
148	13	0.4	1	0 13
149	13	0.4	1	0 13
150	13	0.4	1	0 13

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0005_good_2.fq.gz
=============================================
23993393 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0005_good_1_trimmed.fq.gz and Unknown_CK259-004T0005_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0005_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0005_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0005_good_1_trimmed.fq.gz and Unknown_CK259-004T0005_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0005_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0005_good_2_val_2.fq.gz

Total number of sequences analysed: 23993393

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 1489 (0.01%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 6 (0.00%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0005_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0005_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0005_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0005_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0005_good_1_trimmed.fq.gz and Unknown_CK259-004T0005_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0006_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	2	AGATCGGAAGAGC	1000000	0.00
Nextera	0	CTGTCTCTTATA	1000000	0.00
smallRNA	0	TGGAATTCTCGG	1000000	0.00
Using Illumina adapter for trimming (count: 2). Second best hit was Nextera (count: 0)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0006_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0006_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,242,413
Reads with adapters:                     3,639 (0.0%)
Reads written (passing filters):    19,242,413 (100.0%)

Total basepairs processed: 2,870,688,697 bp
Quality-trimmed:               1,428,207 bp (0.0%)
Total written (filtered):  2,869,061,728 bp (99.9%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3639 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 24.5%
  C: 26.7%
  G: 38.1%
  T: 10.6%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	458	18791.4	0	458
6	50	4697.9	0	50
7	4	1174.5	0	4
9	453	73.4	0	0 453
10	155	18.4	1	0 155
11	109	4.6	1	0 109
12	42	1.1	1	0 42
13	15	0.3	1	0 15
14	16	0.3	1	0 16
15	21	0.3	1	0 21
16	7	0.3	1	0 7
17	14	0.3	1	0 14
18	11	0.3	1	0 11
19	7	0.3	1	1 6
20	17	0.3	1	0 17
21	15	0.3	1	1 14
22	13	0.3	1	0 13
23	22	0.3	1	1 21
24	29	0.3	1	1 28
25	41	0.3	1	0 41
26	14	0.3	1	0 14
27	20	0.3	1	4 16
28	16	0.3	1	2 14
29	11	0.3	1	0 11
30	25	0.3	1	2 23
31	11	0.3	1	1 10
32	14	0.3	1	0 14
33	13	0.3	1	1 12
34	14	0.3	1	0 14
35	9	0.3	1	1 8
36	28	0.3	1	0 28
37	21	0.3	1	0 21
38	12	0.3	1	0 12
39	15	0.3	1	0 15
40	18	0.3	1	0 18
41	11	0.3	1	2 9
42	11	0.3	1	0 11
43	19	0.3	1	0 19
44	9	0.3	1	0 9
45	12	0.3	1	2 10
46	22	0.3	1	1 21
47	22	0.3	1	1 21
48	10	0.3	1	0 10
49	19	0.3	1	1 18
50	19	0.3	1	0 19
51	16	0.3	1	2 14
52	28	0.3	1	0 28
53	31	0.3	1	1 30
54	27	0.3	1	0 27
55	16	0.3	1	0 16
56	20	0.3	1	0 20
57	23	0.3	1	0 23
58	18	0.3	1	0 18
59	20	0.3	1	0 20
60	33	0.3	1	0 33
61	14	0.3	1	0 14
62	26	0.3	1	0 26
63	30	0.3	1	0 30
64	20	0.3	1	0 20
65	18	0.3	1	0 18
66	20	0.3	1	2 18
67	18	0.3	1	0 18
68	14	0.3	1	0 14
69	11	0.3	1	0 11
70	38	0.3	1	2 36
71	17	0.3	1	0 17
72	19	0.3	1	0 19
73	25	0.3	1	0 25
74	10	0.3	1	0 10
75	18	0.3	1	0 18
76	14	0.3	1	0 14
77	7	0.3	1	0 7
78	22	0.3	1	0 22
79	12	0.3	1	0 12
80	22	0.3	1	0 22
81	15	0.3	1	0 15
82	19	0.3	1	0 19
83	22	0.3	1	0 22
84	14	0.3	1	0 14
85	22	0.3	1	1 21
86	18	0.3	1	1 17
87	19	0.3	1	0 19
88	10	0.3	1	2 8
89	16	0.3	1	1 15
90	18	0.3	1	0 18
91	19	0.3	1	0 19
92	23	0.3	1	0 23
93	18	0.3	1	0 18
94	20	0.3	1	0 20
95	11	0.3	1	0 11
96	18	0.3	1	0 18
97	28	0.3	1	0 28
98	12	0.3	1	4 8
99	11	0.3	1	0 11
100	11	0.3	1	0 11
101	21	0.3	1	0 21
102	23	0.3	1	0 23
103	16	0.3	1	0 16
104	16	0.3	1	0 16
105	16	0.3	1	0 16
106	6	0.3	1	1 5
107	11	0.3	1	0 11
108	16	0.3	1	0 16
109	13	0.3	1	0 13
110	11	0.3	1	0 11
111	26	0.3	1	0 26
112	13	0.3	1	0 13
113	25	0.3	1	0 25
114	23	0.3	1	0 23
115	16	0.3	1	0 16
116	12	0.3	1	0 12
117	18	0.3	1	0 18
118	26	0.3	1	0 26
119	14	0.3	1	0 14
120	11	0.3	1	0 11
121	27	0.3	1	1 26
122	9	0.3	1	0 9
123	21	0.3	1	0 21
124	21	0.3	1	0 21
125	14	0.3	1	1 13
126	8	0.3	1	0 8
127	8	0.3	1	0 8
128	18	0.3	1	0 18
129	19	0.3	1	0 19
130	20	0.3	1	0 20
131	12	0.3	1	0 12
132	14	0.3	1	0 14
133	19	0.3	1	0 19
134	9	0.3	1	0 9
135	15	0.3	1	0 15
136	14	0.3	1	0 14
137	16	0.3	1	0 16
138	25	0.3	1	0 25
139	8	0.3	1	0 8
140	16	0.3	1	0 16
141	29	0.3	1	0 29
142	3	0.3	1	0 3
143	15	0.3	1	0 15
144	5	0.3	1	0 5
145	22	0.3	1	0 22
146	12	0.3	1	0 12
147	35	0.3	1	0 35
148	9	0.3	1	0 9
149	13	0.3	1	0 13
150	5	0.3	1	0 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_1.fq.gz
=============================================
19242413 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0006_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0006_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,242,413
Reads with adapters:                     3,650 (0.0%)
Reads written (passing filters):    19,242,413 (100.0%)

Total basepairs processed: 2,870,359,254 bp
Quality-trimmed:               3,146,920 bp (0.1%)
Total written (filtered):  2,867,010,609 bp (99.9%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3650 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 25.3%
  C: 29.3%
  G: 36.7%
  T: 8.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	487	18791.4	0	487
6	99	4697.9	0	99
7	5	1174.5	0	5
8	1	293.6	0	1
9	461	73.4	0	1 460
10	131	18.4	1	0 131
11	110	4.6	1	0 110
12	27	1.1	1	0 27
13	16	0.3	1	0 16
14	9	0.3	1	0 9
15	8	0.3	1	0 8
16	9	0.3	1	0 9
17	13	0.3	1	2 11
18	14	0.3	1	0 14
19	16	0.3	1	3 13
20	12	0.3	1	0 12
21	7	0.3	1	0 7
22	19	0.3	1	4 15
23	29	0.3	1	1 28
24	26	0.3	1	2 24
25	27	0.3	1	0 27
26	18	0.3	1	1 17
27	9	0.3	1	1 8
28	12	0.3	1	0 12
29	12	0.3	1	0 12
30	16	0.3	1	0 16
31	14	0.3	1	0 14
32	12	0.3	1	0 12
33	8	0.3	1	2 6
34	15	0.3	1	1 14
35	17	0.3	1	0 17
36	15	0.3	1	1 14
37	26	0.3	1	2 24
38	12	0.3	1	0 12
39	19	0.3	1	1 18
40	15	0.3	1	1 14
41	16	0.3	1	1 15
42	18	0.3	1	2 16
43	18	0.3	1	0 18
44	14	0.3	1	3 11
45	13	0.3	1	0 13
46	18	0.3	1	1 17
47	11	0.3	1	2 9
48	14	0.3	1	2 12
49	15	0.3	1	1 14
50	16	0.3	1	0 16
51	21	0.3	1	0 21
52	33	0.3	1	0 33
53	30	0.3	1	0 30
54	19	0.3	1	0 19
55	15	0.3	1	0 15
56	21	0.3	1	0 21
57	15	0.3	1	0 15
58	16	0.3	1	0 16
59	12	0.3	1	0 12
60	30	0.3	1	0 30
61	20	0.3	1	1 19
62	15	0.3	1	0 15
63	29	0.3	1	0 29
64	18	0.3	1	0 18
65	8	0.3	1	0 8
66	9	0.3	1	0 9
67	16	0.3	1	0 16
68	12	0.3	1	0 12
69	23	0.3	1	1 22
70	35	0.3	1	0 35
71	15	0.3	1	0 15
72	16	0.3	1	0 16
73	28	0.3	1	0 28
74	11	0.3	1	0 11
75	12	0.3	1	0 12
76	10	0.3	1	0 10
77	15	0.3	1	0 15
78	18	0.3	1	0 18
79	15	0.3	1	0 15
80	16	0.3	1	0 16
81	18	0.3	1	0 18
82	17	0.3	1	1 16
83	22	0.3	1	0 22
84	25	0.3	1	0 25
85	15	0.3	1	0 15
86	10	0.3	1	1 9
87	35	0.3	1	1 34
88	34	0.3	1	1 33
89	18	0.3	1	1 17
90	15	0.3	1	0 15
91	20	0.3	1	1 19
92	27	0.3	1	0 27
93	7	0.3	1	0 7
94	19	0.3	1	0 19
95	15	0.3	1	0 15
96	14	0.3	1	0 14
97	17	0.3	1	1 16
98	24	0.3	1	3 21
99	14	0.3	1	0 14
100	16	0.3	1	0 16
101	18	0.3	1	0 18
102	21	0.3	1	0 21
103	22	0.3	1	0 22
104	8	0.3	1	0 8
105	21	0.3	1	1 20
106	12	0.3	1	1 11
107	6	0.3	1	0 6
108	17	0.3	1	1 16
109	12	0.3	1	0 12
110	11	0.3	1	0 11
111	23	0.3	1	0 23
112	11	0.3	1	0 11
113	21	0.3	1	0 21
114	13	0.3	1	1 12
115	12	0.3	1	0 12
116	10	0.3	1	0 10
117	15	0.3	1	0 15
118	15	0.3	1	0 15
119	23	0.3	1	0 23
120	11	0.3	1	1 10
121	14	0.3	1	1 13
122	14	0.3	1	0 14
123	12	0.3	1	0 12
124	19	0.3	1	0 19
125	11	0.3	1	0 11
126	11	0.3	1	0 11
127	21	0.3	1	0 21
128	17	0.3	1	0 17
129	28	0.3	1	0 28
130	25	0.3	1	0 25
131	13	0.3	1	0 13
132	11	0.3	1	0 11
133	19	0.3	1	0 19
134	9	0.3	1	0 9
135	13	0.3	1	0 13
136	15	0.3	1	0 15
137	25	0.3	1	0 25
138	13	0.3	1	0 13
139	9	0.3	1	0 9
140	20	0.3	1	0 20
141	46	0.3	1	0 46
142	14	0.3	1	0 14
143	8	0.3	1	0 8
144	6	0.3	1	0 6
145	25	0.3	1	0 25
146	19	0.3	1	0 19
147	46	0.3	1	0 46
148	18	0.3	1	0 18
149	10	0.3	1	0 10
150	3	0.3	1	0 3

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0006_good_2.fq.gz
=============================================
19242413 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0006_good_1_trimmed.fq.gz and Unknown_CK259-004T0006_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0006_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0006_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0006_good_1_trimmed.fq.gz and Unknown_CK259-004T0006_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0006_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0006_good_2_val_2.fq.gz

Total number of sequences analysed: 19242413

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 19956 (0.10%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 5090 (0.03%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0006_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0006_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0006_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0006_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0006_good_1_trimmed.fq.gz and Unknown_CK259-004T0006_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0007_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	11	TGGAATTCTCGG	1000000	0.00
Illumina	3	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using smallRNA adapter for trimming (count: 11). Second best hit was Illumina (count: 3)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0007_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0007_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,260,764
Reads with adapters:                    69,130 (0.3%)
Reads written (passing filters):    22,260,764 (100.0%)

Total basepairs processed: 3,312,220,319 bp
Quality-trimmed:                  41,123 bp (0.0%)
Total written (filtered):  3,311,017,354 bp (100.0%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 69130 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.3%
  C: 23.9%
  G: 24.6%
  T: 27.1%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	42822	21739.0	0	42822
6	10020	5434.8	0	10020
7	2835	1358.7	0	2835
8	569	339.7	0	569
9	1106	84.9	0	226 880
10	1214	21.2	1	27 1187
11	417	5.3	1	28 389
12	100	1.3	1	20 80
13	97	1.3	1	9 88
14	55	1.3	1	5 50
15	56	1.3	1	0 56
16	72	1.3	1	0 72
17	47	1.3	1	0 47
18	45	1.3	1	0 45
19	79	1.3	1	1 78
20	71	1.3	1	0 71
21	57	1.3	1	2 55
22	65	1.3	1	2 63
23	86	1.3	1	0 86
24	55	1.3	1	0 55
25	68	1.3	1	1 67
26	40	1.3	1	1 39
27	73	1.3	1	1 72
28	47	1.3	1	0 47
29	84	1.3	1	2 82
30	90	1.3	1	4 86
31	97	1.3	1	4 93
32	76	1.3	1	3 73
33	67	1.3	1	2 65
34	60	1.3	1	0 60
35	68	1.3	1	3 65
36	82	1.3	1	5 77
37	70	1.3	1	1 69
38	57	1.3	1	0 57
39	62	1.3	1	4 58
40	82	1.3	1	1 81
41	56	1.3	1	0 56
42	76	1.3	1	1 75
43	76	1.3	1	0 76
44	74	1.3	1	5 69
45	62	1.3	1	0 62
46	94	1.3	1	9 85
47	56	1.3	1	2 54
48	88	1.3	1	1 87
49	87	1.3	1	1 86
50	72	1.3	1	1 71
51	63	1.3	1	2 61
52	82	1.3	1	2 80
53	80	1.3	1	1 79
54	82	1.3	1	1 81
55	86	1.3	1	3 83
56	67	1.3	1	1 66
57	68	1.3	1	0 68
58	95	1.3	1	3 92
59	77	1.3	1	1 76
60	83	1.3	1	0 83
61	80	1.3	1	1 79
62	63	1.3	1	0 63
63	90	1.3	1	1 89
64	74	1.3	1	0 74
65	80	1.3	1	1 79
66	47	1.3	1	1 46
67	69	1.3	1	0 69
68	55	1.3	1	2 53
69	54	1.3	1	0 54
70	53	1.3	1	0 53
71	53	1.3	1	0 53
72	79	1.3	1	0 79
73	69	1.3	1	0 69
74	71	1.3	1	0 71
75	99	1.3	1	1 98
76	90	1.3	1	0 90
77	77	1.3	1	0 77
78	68	1.3	1	2 66
79	93	1.3	1	0 93
80	69	1.3	1	2 67
81	85	1.3	1	0 85
82	62	1.3	1	0 62
83	68	1.3	1	1 67
84	75	1.3	1	1 74
85	44	1.3	1	1 43
86	76	1.3	1	0 76
87	63	1.3	1	0 63
88	85	1.3	1	0 85
89	72	1.3	1	0 72
90	67	1.3	1	1 66
91	53	1.3	1	2 51
92	64	1.3	1	1 63
93	61	1.3	1	0 61
94	57	1.3	1	1 56
95	56	1.3	1	0 56
96	76	1.3	1	2 74
97	74	1.3	1	0 74
98	68	1.3	1	1 67
99	79	1.3	1	0 79
100	44	1.3	1	0 44
101	70	1.3	1	0 70
102	87	1.3	1	0 87
103	82	1.3	1	0 82
104	74	1.3	1	0 74
105	112	1.3	1	0 112
106	65	1.3	1	0 65
107	84	1.3	1	1 83
108	101	1.3	1	1 100
109	71	1.3	1	0 71
110	61	1.3	1	0 61
111	68	1.3	1	0 68
112	73	1.3	1	0 73
113	77	1.3	1	0 77
114	64	1.3	1	1 63
115	78	1.3	1	0 78
116	66	1.3	1	0 66
117	76	1.3	1	1 75
118	63	1.3	1	0 63
119	45	1.3	1	3 42
120	73	1.3	1	2 71
121	56	1.3	1	2 54
122	66	1.3	1	0 66
123	73	1.3	1	0 73
124	97	1.3	1	0 97
125	79	1.3	1	1 78
126	75	1.3	1	3 72
127	109	1.3	1	3 106
128	59	1.3	1	0 59
129	73	1.3	1	1 72
130	87	1.3	1	2 85
131	99	1.3	1	2 97
132	78	1.3	1	7 71
133	89	1.3	1	1 88
134	70	1.3	1	4 66
135	63	1.3	1	0 63
136	69	1.3	1	0 69
137	89	1.3	1	0 89
138	58	1.3	1	4 54
139	72	1.3	1	20 52
140	46	1.3	1	0 46
141	94	1.3	1	23 71
142	48	1.3	1	4 44
143	70	1.3	1	2 68
144	96	1.3	1	4 92
145	30	1.3	1	0 30
146	19	1.3	1	0 19
147	54	1.3	1	1 53
148	99	1.3	1	1 98
149	258	1.3	1	20 238
150	88	1.3	1	3 85

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_1.fq.gz
=============================================
22260764 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0007_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0007_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,260,764
Reads with adapters:                    22,479 (0.1%)
Reads written (passing filters):    22,260,764 (100.0%)

Total basepairs processed: 3,311,993,880 bp
Quality-trimmed:                 136,653 bp (0.0%)
Total written (filtered):  3,311,303,295 bp (100.0%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 22479 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 16.9%
  C: 22.3%
  G: 27.3%
  T: 33.5%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	10790	21739.0	0	10790
6	3644	5434.8	0	3644
7	827	1358.7	0	827
8	229	339.7	0	229
9	315	84.9	0	55 260
10	493	21.2	1	5 488
11	194	5.3	1	6 188
12	65	1.3	1	18 47
13	40	1.3	1	1 39
14	39	1.3	1	2 37
15	36	1.3	1	5 31
16	53	1.3	1	8 45
17	68	1.3	1	6 62
18	49	1.3	1	1 48
19	50	1.3	1	6 44
20	55	1.3	1	6 49
21	49	1.3	1	0 49
22	44	1.3	1	1 43
23	42	1.3	1	0 42
24	42	1.3	1	2 40
25	31	1.3	1	2 29
26	49	1.3	1	1 48
27	43	1.3	1	4 39
28	54	1.3	1	1 53
29	34	1.3	1	4 30
30	47	1.3	1	3 44
31	31	1.3	1	3 28
32	40	1.3	1	8 32
33	49	1.3	1	8 41
34	42	1.3	1	2 40
35	53	1.3	1	3 50
36	59	1.3	1	4 55
37	52	1.3	1	2 50
38	52	1.3	1	1 51
39	65	1.3	1	0 65
40	51	1.3	1	2 49
41	43	1.3	1	0 43
42	39	1.3	1	3 36
43	32	1.3	1	1 31
44	56	1.3	1	0 56
45	38	1.3	1	0 38
46	38	1.3	1	2 36
47	37	1.3	1	2 35
48	50	1.3	1	5 45
49	50	1.3	1	0 50
50	66	1.3	1	1 65
51	52	1.3	1	0 52
52	72	1.3	1	0 72
53	53	1.3	1	1 52
54	34	1.3	1	2 32
55	61	1.3	1	0 61
56	80	1.3	1	0 80
57	61	1.3	1	0 61
58	54	1.3	1	3 51
59	44	1.3	1	0 44
60	41	1.3	1	2 39
61	57	1.3	1	0 57
62	20	1.3	1	0 20
63	48	1.3	1	1 47
64	53	1.3	1	6 47
65	35	1.3	1	2 33
66	28	1.3	1	2 26
67	34	1.3	1	1 33
68	45	1.3	1	2 43
69	57	1.3	1	5 52
70	44	1.3	1	0 44
71	44	1.3	1	2 42
72	28	1.3	1	0 28
73	26	1.3	1	0 26
74	29	1.3	1	0 29
75	66	1.3	1	1 65
76	49	1.3	1	1 48
77	44	1.3	1	1 43
78	36	1.3	1	2 34
79	68	1.3	1	5 63
80	33	1.3	1	2 31
81	43	1.3	1	1 42
82	52	1.3	1	2 50
83	41	1.3	1	1 40
84	34	1.3	1	0 34
85	38	1.3	1	1 37
86	37	1.3	1	0 37
87	39	1.3	1	0 39
88	51	1.3	1	1 50
89	43	1.3	1	3 40
90	42	1.3	1	2 40
91	26	1.3	1	0 26
92	35	1.3	1	0 35
93	52	1.3	1	0 52
94	42	1.3	1	2 40
95	25	1.3	1	2 23
96	22	1.3	1	0 22
97	46	1.3	1	0 46
98	45	1.3	1	0 45
99	55	1.3	1	0 55
100	59	1.3	1	0 59
101	59	1.3	1	6 53
102	61	1.3	1	13 48
103	22	1.3	1	0 22
104	61	1.3	1	9 52
105	43	1.3	1	0 43
106	49	1.3	1	2 47
107	42	1.3	1	8 34
108	50	1.3	1	3 47
109	53	1.3	1	1 52
110	53	1.3	1	2 51
111	43	1.3	1	0 43
112	38	1.3	1	3 35
113	27	1.3	1	0 27
114	29	1.3	1	0 29
115	27	1.3	1	1 26
116	44	1.3	1	0 44
117	38	1.3	1	2 36
118	60	1.3	1	4 56
119	44	1.3	1	1 43
120	39	1.3	1	0 39
121	37	1.3	1	0 37
122	57	1.3	1	1 56
123	38	1.3	1	6 32
124	34	1.3	1	1 33
125	27	1.3	1	0 27
126	46	1.3	1	0 46
127	35	1.3	1	0 35
128	36	1.3	1	0 36
129	33	1.3	1	1 32
130	31	1.3	1	7 24
131	31	1.3	1	3 28
132	39	1.3	1	11 28
133	22	1.3	1	0 22
134	36	1.3	1	2 34
135	34	1.3	1	1 33
136	48	1.3	1	1 47
137	19	1.3	1	0 19
138	24	1.3	1	5 19
139	28	1.3	1	0 28
140	37	1.3	1	1 36
141	36	1.3	1	0 36
142	53	1.3	1	1 52
143	28	1.3	1	0 28
144	45	1.3	1	0 45
145	35	1.3	1	0 35
146	73	1.3	1	3 70
147	21	1.3	1	1 20
148	23	1.3	1	0 23
149	29	1.3	1	0 29
150	14	1.3	1	0 14

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0007_good_2.fq.gz
=============================================
22260764 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0007_good_1_trimmed.fq.gz and Unknown_CK259-004T0007_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0007_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0007_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0007_good_1_trimmed.fq.gz and Unknown_CK259-004T0007_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0007_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0007_good_2_val_2.fq.gz

Total number of sequences analysed: 22260764

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 4175 (0.02%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 5 (0.00%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0007_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0007_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0007_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0007_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0007_good_1_trimmed.fq.gz and Unknown_CK259-004T0007_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0008_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	4	TGGAATTCTCGG	1000000	0.00
Nextera	1	CTGTCTCTTATA	1000000	0.00
Illumina	1	AGATCGGAAGAGC	1000000	0.00
Using smallRNA adapter for trimming (count: 4). Second best hit was Nextera (count: 1)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0008_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0008_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,604,430
Reads with adapters:                    87,789 (0.4%)
Reads written (passing filters):    23,604,430 (100.0%)

Total basepairs processed: 3,517,362,851 bp
Quality-trimmed:               5,526,204 bp (0.2%)
Total written (filtered):  3,510,503,230 bp (99.8%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 87789 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 22.3%
  C: 20.4%
  G: 31.4%
  T: 25.8%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	60235	23051.2	0	60235
6	10222	5762.8	0	10222
7	2866	1440.7	0	2866
8	652	360.2	0	652
9	1256	90.0	0	191 1065
10	1157	22.5	1	41 1116
11	331	5.6	1	2 329
12	68	1.4	1	0 68
13	78	1.4	1	6 72
14	40	1.4	1	0 40
15	35	1.4	1	0 35
16	46	1.4	1	0 46
17	32	1.4	1	0 32
18	36	1.4	1	0 36
19	51	1.4	1	0 51
20	55	1.4	1	0 55
21	39	1.4	1	0 39
22	121	1.4	1	0 121
23	82	1.4	1	0 82
24	89	1.4	1	3 86
25	56	1.4	1	0 56
26	21	1.4	1	0 21
27	76	1.4	1	0 76
28	54	1.4	1	0 54
29	55	1.4	1	0 55
30	40	1.4	1	0 40
31	50	1.4	1	0 50
32	58	1.4	1	0 58
33	57	1.4	1	1 56
34	56	1.4	1	0 56
35	69	1.4	1	0 69
36	94	1.4	1	0 94
37	52	1.4	1	0 52
38	93	1.4	1	2 91
39	62	1.4	1	0 62
40	126	1.4	1	0 126
41	82	1.4	1	0 82
42	81	1.4	1	1 80
43	85	1.4	1	1 84
44	66	1.4	1	0 66
45	105	1.4	1	0 105
46	71	1.4	1	1 70
47	87	1.4	1	4 83
48	52	1.4	1	0 52
49	50	1.4	1	0 50
50	67	1.4	1	0 67
51	78	1.4	1	1 77
52	85	1.4	1	0 85
53	176	1.4	1	0 176
54	79	1.4	1	0 79
55	54	1.4	1	1 53
56	59	1.4	1	2 57
57	92	1.4	1	0 92
58	81	1.4	1	0 81
59	65	1.4	1	3 62
60	46	1.4	1	0 46
61	93	1.4	1	0 93
62	112	1.4	1	0 112
63	74	1.4	1	1 73
64	71	1.4	1	0 71
65	47	1.4	1	0 47
66	46	1.4	1	0 46
67	61	1.4	1	0 61
68	42	1.4	1	0 42
69	32	1.4	1	0 32
70	96	1.4	1	0 96
71	86	1.4	1	0 86
72	93	1.4	1	0 93
73	66	1.4	1	0 66
74	56	1.4	1	0 56
75	174	1.4	1	0 174
76	113	1.4	1	0 113
77	126	1.4	1	0 126
78	98	1.4	1	0 98
79	91	1.4	1	0 91
80	85	1.4	1	0 85
81	62	1.4	1	0 62
82	187	1.4	1	0 187
83	54	1.4	1	0 54
84	88	1.4	1	2 86
85	52	1.4	1	0 52
86	59	1.4	1	0 59
87	78	1.4	1	1 77
88	84	1.4	1	0 84
89	175	1.4	1	0 175
90	81	1.4	1	0 81
91	63	1.4	1	0 63
92	147	1.4	1	0 147
93	125	1.4	1	0 125
94	135	1.4	1	0 135
95	174	1.4	1	0 174
96	153	1.4	1	0 153
97	143	1.4	1	0 143
98	58	1.4	1	2 56
99	86	1.4	1	0 86
100	340	1.4	1	2 338
101	141	1.4	1	0 141
102	263	1.4	1	0 263
103	167	1.4	1	0 167
104	123	1.4	1	0 123
105	57	1.4	1	0 57
106	63	1.4	1	0 63
107	49	1.4	1	0 49
108	68	1.4	1	0 68
109	92	1.4	1	4 88
110	48	1.4	1	2 46
111	71	1.4	1	0 71
112	64	1.4	1	1 63
113	61	1.4	1	0 61
114	52	1.4	1	0 52
115	48	1.4	1	0 48
116	70	1.4	1	0 70
117	107	1.4	1	0 107
118	54	1.4	1	0 54
119	48	1.4	1	0 48
120	59	1.4	1	0 59
121	49	1.4	1	0 49
122	73	1.4	1	0 73
123	51	1.4	1	0 51
124	107	1.4	1	1 106
125	75	1.4	1	0 75
126	49	1.4	1	0 49
127	45	1.4	1	0 45
128	60	1.4	1	0 60
129	92	1.4	1	0 92
130	51	1.4	1	0 51
131	87	1.4	1	0 87
132	67	1.4	1	0 67
133	51	1.4	1	0 51
134	47	1.4	1	0 47
135	67	1.4	1	0 67
136	75	1.4	1	1 74
137	65	1.4	1	0 65
138	53	1.4	1	0 53
139	58	1.4	1	0 58
140	51	1.4	1	0 51
141	84	1.4	1	0 84
142	62	1.4	1	0 62
143	66	1.4	1	0 66
144	67	1.4	1	0 67
145	14	1.4	1	0 14
146	47	1.4	1	0 47
147	54	1.4	1	0 54
148	71	1.4	1	0 71
149	198	1.4	1	2 196
150	71	1.4	1	0 71

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_1.fq.gz
=============================================
23604430 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0008_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0008_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,604,430
Reads with adapters:                    22,797 (0.1%)
Reads written (passing filters):    23,604,430 (100.0%)

Total basepairs processed: 3,517,526,554 bp
Quality-trimmed:              13,306,446 bp (0.4%)
Total written (filtered):  3,503,816,152 bp (99.6%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 22797 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 10.3%
  C: 25.8%
  G: 21.8%
  T: 42.0%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	13686	23051.2	0	13686
6	4156	5762.8	0	4156
7	621	1440.7	0	621
8	137	360.2	0	137
9	190	90.0	0	21 169
10	295	22.5	1	6 289
11	131	5.6	1	6 125
12	58	1.4	1	27 31
13	36	1.4	1	2 34
14	27	1.4	1	2 25
15	34	1.4	1	7 27
16	48	1.4	1	25 23
17	33	1.4	1	4 29
18	19	1.4	1	6 13
19	33	1.4	1	9 24
20	15	1.4	1	3 12
21	12	1.4	1	0 12
22	13	1.4	1	2 11
23	30	1.4	1	2 28
24	13	1.4	1	0 13
25	24	1.4	1	4 20
26	20	1.4	1	1 19
27	25	1.4	1	2 23
28	15	1.4	1	1 14
29	15	1.4	1	4 11
30	27	1.4	1	1 26
31	13	1.4	1	0 13
32	5	1.4	1	1 4
33	22	1.4	1	9 13
34	23	1.4	1	2 21
35	58	1.4	1	2 56
36	26	1.4	1	2 24
37	14	1.4	1	2 12
38	39	1.4	1	2 37
39	30	1.4	1	0 30
40	45	1.4	1	2 43
41	12	1.4	1	0 12
42	14	1.4	1	1 13
43	10	1.4	1	1 9
44	33	1.4	1	2 31
45	27	1.4	1	2 25
46	18	1.4	1	0 18
47	30	1.4	1	3 27
48	22	1.4	1	4 18
49	15	1.4	1	0 15
50	16	1.4	1	1 15
51	16	1.4	1	2 14
52	26	1.4	1	0 26
53	28	1.4	1	1 27
54	24	1.4	1	0 24
55	31	1.4	1	3 28
56	20	1.4	1	0 20
57	23	1.4	1	1 22
58	39	1.4	1	3 36
59	29	1.4	1	7 22
60	18	1.4	1	1 17
61	29	1.4	1	2 27
62	13	1.4	1	0 13
63	23	1.4	1	1 22
64	27	1.4	1	5 22
65	46	1.4	1	4 42
66	23	1.4	1	3 20
67	6	1.4	1	0 6
68	22	1.4	1	1 21
69	24	1.4	1	1 23
70	16	1.4	1	0 16
71	18	1.4	1	3 15
72	25	1.4	1	1 24
73	16	1.4	1	0 16
74	28	1.4	1	3 25
75	23	1.4	1	0 23
76	22	1.4	1	0 22
77	12	1.4	1	1 11
78	12	1.4	1	2 10
79	16	1.4	1	0 16
80	20	1.4	1	1 19
81	32	1.4	1	0 32
82	38	1.4	1	4 34
83	25	1.4	1	1 24
84	24	1.4	1	1 23
85	22	1.4	1	1 21
86	20	1.4	1	0 20
87	25	1.4	1	0 25
88	18	1.4	1	0 18
89	37	1.4	1	8 29
90	56	1.4	1	2 54
91	4	1.4	1	0 4
92	23	1.4	1	2 21
93	27	1.4	1	0 27
94	21	1.4	1	1 20
95	9	1.4	1	1 8
96	18	1.4	1	1 17
97	19	1.4	1	1 18
98	13	1.4	1	0 13
99	22	1.4	1	0 22
100	19	1.4	1	0 19
101	12	1.4	1	0 12
102	20	1.4	1	7 13
103	18	1.4	1	3 15
104	24	1.4	1	5 19
105	23	1.4	1	2 21
106	14	1.4	1	0 14
107	30	1.4	1	15 15
108	37	1.4	1	6 31
109	15	1.4	1	3 12
110	29	1.4	1	1 28
111	19	1.4	1	0 19
112	23	1.4	1	2 21
113	19	1.4	1	2 17
114	38	1.4	1	1 37
115	21	1.4	1	1 20
116	18	1.4	1	0 18
117	28	1.4	1	2 26
118	27	1.4	1	2 25
119	30	1.4	1	2 28
120	37	1.4	1	3 34
121	29	1.4	1	7 22
122	26	1.4	1	7 19
123	32	1.4	1	6 26
124	31	1.4	1	5 26
125	29	1.4	1	9 20
126	80	1.4	1	28 52
127	50	1.4	1	15 35
128	41	1.4	1	13 28
129	78	1.4	1	21 57
130	51	1.4	1	13 38
131	44	1.4	1	9 35
132	42	1.4	1	9 33
133	39	1.4	1	10 29
134	67	1.4	1	19 48
135	27	1.4	1	2 25
136	30	1.4	1	3 27
137	22	1.4	1	1 21
138	12	1.4	1	3 9
139	25	1.4	1	2 23
140	14	1.4	1	5 9
141	7	1.4	1	0 7
142	28	1.4	1	1 27
143	15	1.4	1	0 15
144	14	1.4	1	0 14
145	14	1.4	1	0 14
146	62	1.4	1	4 58
147	17	1.4	1	0 17
148	7	1.4	1	0 7
149	22	1.4	1	0 22
150	28	1.4	1	7 21

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0008_good_2.fq.gz
=============================================
23604430 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0008_good_1_trimmed.fq.gz and Unknown_CK259-004T0008_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0008_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0008_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0008_good_1_trimmed.fq.gz and Unknown_CK259-004T0008_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0008_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0008_good_2_val_2.fq.gz

Total number of sequences analysed: 23604430

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 100089 (0.42%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 32790 (0.14%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0008_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0008_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0008_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0008_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0008_good_1_trimmed.fq.gz and Unknown_CK259-004T0008_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0009_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Nextera	2	CTGTCTCTTATA	1000000	0.00
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Illumina	0	AGATCGGAAGAGC	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count Nextera: 2, count smallRNA: 2, count Illumina: 0)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior).
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0009_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0009_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,076,969
Reads with adapters:                    44,405 (0.2%)
Reads written (passing filters):    20,076,969 (100.0%)

Total basepairs processed: 2,995,126,104 bp
Quality-trimmed:                  54,298 bp (0.0%)
Total written (filtered):  2,993,949,118 bp (100.0%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 44405 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 33.4%
  C: 16.5%
  G: 22.8%
  T: 27.2%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	21681	19606.4	0	21681
6	7151	4901.6	0	7151
7	1893	1225.4	0	1893
8	569	306.4	0	569
9	261	76.6	0	70 191
10	433	19.1	1	4 429
11	157	4.8	1	0 157
12	36	1.2	1	0 36
13	56	1.2	1	0 56
14	73	1.2	1	0 73
15	83	1.2	1	0 83
16	63	1.2	1	1 62
17	394	1.2	1	1 393
18	44	1.2	1	0 44
19	134	1.2	1	0 134
20	88	1.2	1	0 88
21	49	1.2	1	0 49
22	130	1.2	1	2 128
23	62	1.2	1	0 62
24	459	1.2	1	1 458
25	179	1.2	1	0 179
26	83	1.2	1	0 83
27	60	1.2	1	0 60
28	48	1.2	1	0 48
29	227	1.2	1	0 227
30	87	1.2	1	1 86
31	83	1.2	1	0 83
32	37	1.2	1	0 37
33	51	1.2	1	0 51
34	127	1.2	1	0 127
35	89	1.2	1	0 89
36	67	1.2	1	0 67
37	57	1.2	1	2 55
38	72	1.2	1	0 72
39	91	1.2	1	0 91
40	42	1.2	1	0 42
41	43	1.2	1	0 43
42	35	1.2	1	0 35
43	27	1.2	1	0 27
44	97	1.2	1	0 97
45	171	1.2	1	0 171
46	134	1.2	1	1 133
47	29	1.2	1	0 29
48	28	1.2	1	0 28
49	36	1.2	1	0 36
50	23	1.2	1	0 23
51	83	1.2	1	0 83
52	49	1.2	1	0 49
53	231	1.2	1	0 231
54	29	1.2	1	0 29
55	83	1.2	1	0 83
56	22	1.2	1	0 22
57	32	1.2	1	0 32
58	25	1.2	1	0 25
59	46	1.2	1	1 45
60	84	1.2	1	0 84
61	43	1.2	1	0 43
62	91	1.2	1	2 89
63	60	1.2	1	0 60
64	93	1.2	1	0 93
65	90	1.2	1	0 90
66	42	1.2	1	0 42
67	64	1.2	1	0 64
68	49	1.2	1	0 49
69	90	1.2	1	0 90
70	69	1.2	1	0 69
71	173	1.2	1	0 173
72	141	1.2	1	0 141
73	109	1.2	1	0 109
74	73	1.2	1	0 73
75	100	1.2	1	0 100
76	270	1.2	1	0 270
77	91	1.2	1	0 91
78	81	1.2	1	0 81
79	39	1.2	1	1 38
80	437	1.2	1	0 437
81	94	1.2	1	0 94
82	261	1.2	1	0 261
83	96	1.2	1	0 96
84	59	1.2	1	0 59
85	119	1.2	1	0 119
86	97	1.2	1	0 97
87	262	1.2	1	1 261
88	149	1.2	1	0 149
89	89	1.2	1	0 89
90	75	1.2	1	0 75
91	46	1.2	1	0 46
92	132	1.2	1	0 132
93	163	1.2	1	1 162
94	89	1.2	1	0 89
95	52	1.2	1	1 51
96	42	1.2	1	0 42
97	50	1.2	1	0 50
98	95	1.2	1	0 95
99	90	1.2	1	0 90
100	72	1.2	1	0 72
101	51	1.2	1	0 51
102	117	1.2	1	0 117
103	36	1.2	1	0 36
104	68	1.2	1	0 68
105	49	1.2	1	0 49
106	52	1.2	1	0 52
107	94	1.2	1	0 94
108	73	1.2	1	0 73
109	38	1.2	1	0 38
110	67	1.2	1	0 67
111	42	1.2	1	3 39
112	46	1.2	1	0 46
113	20	1.2	1	0 20
114	63	1.2	1	0 63
115	23	1.2	1	0 23
116	21	1.2	1	0 21
117	65	1.2	1	0 65
118	75	1.2	1	0 75
119	44	1.2	1	0 44
120	23	1.2	1	0 23
121	15	1.2	1	0 15
122	26	1.2	1	0 26
123	44	1.2	1	0 44
124	70	1.2	1	0 70
125	38	1.2	1	4 34
126	100	1.2	1	0 100
127	248	1.2	1	0 248
128	89	1.2	1	0 89
129	51	1.2	1	3 48
130	83	1.2	1	0 83
131	70	1.2	1	0 70
132	52	1.2	1	0 52
133	58	1.2	1	1 57
134	222	1.2	1	1 221
135	272	1.2	1	0 272
136	64	1.2	1	0 64
137	48	1.2	1	0 48
138	41	1.2	1	0 41
139	111	1.2	1	1 110
140	43	1.2	1	0 43
141	29	1.2	1	0 29
142	31	1.2	1	0 31
143	193	1.2	1	0 193
144	187	1.2	1	0 187
145	35	1.2	1	0 35
146	36	1.2	1	0 36
147	54	1.2	1	0 54
148	43	1.2	1	0 43
149	84	1.2	1	0 84
150	41	1.2	1	0 41

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_1.fq.gz
=============================================
20076969 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0009_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0009_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,076,969
Reads with adapters:                    41,004 (0.2%)
Reads written (passing filters):    20,076,969 (100.0%)

Total basepairs processed: 2,995,031,481 bp
Quality-trimmed:                 143,832 bp (0.0%)
Total written (filtered):  2,993,892,112 bp (100.0%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41004 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 32.9%
  C: 17.0%
  G: 23.3%
  T: 26.8%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	20698	19606.4	0	20698
6	6530	4901.6	0	6530
7	1686	1225.4	0	1686
8	538	306.4	0	538
9	278	76.6	0	61 217
10	365	19.1	1	0 365
11	125	4.8	1	0 125
12	51	1.2	1	1 50
13	61	1.2	1	0 61
14	57	1.2	1	0 57
15	70	1.2	1	0 70
16	43	1.2	1	0 43
17	336	1.2	1	0 336
18	34	1.2	1	0 34
19	136	1.2	1	0 136
20	80	1.2	1	0 80
21	63	1.2	1	0 63
22	105	1.2	1	1 104
23	59	1.2	1	0 59
24	369	1.2	1	0 369
25	140	1.2	1	1 139
26	62	1.2	1	1 61
27	55	1.2	1	0 55
28	52	1.2	1	0 52
29	222	1.2	1	0 222
30	73	1.2	1	0 73
31	57	1.2	1	0 57
32	35	1.2	1	0 35
33	35	1.2	1	0 35
34	108	1.2	1	0 108
35	85	1.2	1	0 85
36	69	1.2	1	0 69
37	51	1.2	1	0 51
38	64	1.2	1	0 64
39	81	1.2	1	0 81
40	44	1.2	1	0 44
41	29	1.2	1	0 29
42	22	1.2	1	0 22
43	39	1.2	1	0 39
44	87	1.2	1	0 87
45	161	1.2	1	0 161
46	111	1.2	1	0 111
47	27	1.2	1	0 27
48	28	1.2	1	0 28
49	28	1.2	1	0 28
50	21	1.2	1	0 21
51	73	1.2	1	0 73
52	45	1.2	1	0 45
53	188	1.2	1	0 188
54	24	1.2	1	0 24
55	68	1.2	1	0 68
56	17	1.2	1	0 17
57	24	1.2	1	0 24
58	25	1.2	1	0 25
59	19	1.2	1	2 17
60	117	1.2	1	0 117
61	31	1.2	1	0 31
62	82	1.2	1	0 82
63	52	1.2	1	1 51
64	87	1.2	1	0 87
65	93	1.2	1	0 93
66	50	1.2	1	0 50
67	68	1.2	1	0 68
68	53	1.2	1	0 53
69	63	1.2	1	0 63
70	57	1.2	1	0 57
71	125	1.2	1	0 125
72	140	1.2	1	0 140
73	80	1.2	1	0 80
74	46	1.2	1	0 46
75	68	1.2	1	0 68
76	264	1.2	1	0 264
77	67	1.2	1	0 67
78	76	1.2	1	0 76
79	48	1.2	1	0 48
80	492	1.2	1	1 491
81	70	1.2	1	0 70
82	202	1.2	1	0 202
83	75	1.2	1	0 75
84	56	1.2	1	0 56
85	108	1.2	1	0 108
86	92	1.2	1	0 92
87	221	1.2	1	0 221
88	158	1.2	1	0 158
89	90	1.2	1	0 90
90	66	1.2	1	0 66
91	47	1.2	1	0 47
92	115	1.2	1	0 115
93	134	1.2	1	0 134
94	79	1.2	1	0 79
95	34	1.2	1	1 33
96	29	1.2	1	0 29
97	30	1.2	1	0 30
98	56	1.2	1	0 56
99	67	1.2	1	0 67
100	78	1.2	1	0 78
101	48	1.2	1	0 48
102	110	1.2	1	1 109
103	45	1.2	1	1 44
104	48	1.2	1	0 48
105	55	1.2	1	0 55
106	37	1.2	1	0 37
107	99	1.2	1	0 99
108	67	1.2	1	0 67
109	28	1.2	1	0 28
110	48	1.2	1	0 48
111	29	1.2	1	1 28
112	41	1.2	1	0 41
113	31	1.2	1	0 31
114	55	1.2	1	1 54
115	24	1.2	1	0 24
116	27	1.2	1	0 27
117	37	1.2	1	0 37
118	57	1.2	1	0 57
119	39	1.2	1	0 39
120	13	1.2	1	0 13
121	15	1.2	1	0 15
122	20	1.2	1	0 20
123	37	1.2	1	0 37
124	78	1.2	1	0 78
125	23	1.2	1	0 23
126	65	1.2	1	0 65
127	212	1.2	1	0 212
128	85	1.2	1	0 85
129	41	1.2	1	1 40
130	76	1.2	1	0 76
131	42	1.2	1	0 42
132	43	1.2	1	0 43
133	46	1.2	1	1 45
134	158	1.2	1	1 157
135	220	1.2	1	2 218
136	62	1.2	1	0 62
137	53	1.2	1	0 53
138	38	1.2	1	0 38
139	101	1.2	1	0 101
140	48	1.2	1	0 48
141	28	1.2	1	0 28
142	41	1.2	1	0 41
143	209	1.2	1	0 209
144	154	1.2	1	1 153
145	42	1.2	1	0 42
146	31	1.2	1	0 31
147	52	1.2	1	0 52
148	28	1.2	1	0 28
149	72	1.2	1	0 72
150	27	1.2	1	0 27

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0009_good_2.fq.gz
=============================================
20076969 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0009_good_1_trimmed.fq.gz and Unknown_CK259-004T0009_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0009_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0009_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0009_good_1_trimmed.fq.gz and Unknown_CK259-004T0009_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0009_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0009_good_2_val_2.fq.gz

Total number of sequences analysed: 20076969

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 9983 (0.05%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 4808 (0.02%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0009_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0009_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0009_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0009_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0009_good_1_trimmed.fq.gz and Unknown_CK259-004T0009_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0010_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Nextera	1	CTGTCTCTTATA	1000000	0.00
Illumina	0	AGATCGGAAGAGC	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 1, count Nextera: 1, count Illumina: 0)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior).
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0010_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0010_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,373,579
Reads with adapters:                    41,502 (0.2%)
Reads written (passing filters):    23,373,579 (100.0%)

Total basepairs processed: 3,483,983,799 bp
Quality-trimmed:               5,738,507 bp (0.2%)
Total written (filtered):  3,477,462,322 bp (99.8%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41502 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.7%
  C: 20.4%
  G: 25.6%
  T: 32.1%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	22610	22825.8	0	22610
6	7113	5706.4	0	7113
7	2270	1426.6	0	2270
8	774	356.7	0	774
9	473	89.2	0	74 399
10	570	22.3	1	1 569
11	257	5.6	1	1 256
12	77	1.4	1	0 77
13	63	1.4	1	0 63
14	91	1.4	1	2 89
15	64	1.4	1	0 64
16	51	1.4	1	0 51
17	70	1.4	1	0 70
18	47	1.4	1	0 47
19	49	1.4	1	0 49
20	50	1.4	1	0 50
21	40	1.4	1	0 40
22	54	1.4	1	0 54
23	69	1.4	1	0 69
24	52	1.4	1	0 52
25	93	1.4	1	0 93
26	24	1.4	1	0 24
27	38	1.4	1	0 38
28	52	1.4	1	1 51
29	40	1.4	1	0 40
30	57	1.4	1	0 57
31	57	1.4	1	0 57
32	65	1.4	1	0 65
33	36	1.4	1	0 36
34	117	1.4	1	0 117
35	83	1.4	1	0 83
36	64	1.4	1	0 64
37	37	1.4	1	0 37
38	30	1.4	1	0 30
39	30	1.4	1	0 30
40	35	1.4	1	0 35
41	40	1.4	1	0 40
42	49	1.4	1	0 49
43	48	1.4	1	1 47
44	58	1.4	1	0 58
45	54	1.4	1	3 51
46	73	1.4	1	0 73
47	28	1.4	1	0 28
48	42	1.4	1	0 42
49	51	1.4	1	0 51
50	51	1.4	1	1 50
51	78	1.4	1	2 76
52	64	1.4	1	0 64
53	59	1.4	1	0 59
54	51	1.4	1	0 51
55	90	1.4	1	0 90
56	50	1.4	1	1 49
57	45	1.4	1	0 45
58	36	1.4	1	0 36
59	52	1.4	1	0 52
60	46	1.4	1	0 46
61	51	1.4	1	0 51
62	59	1.4	1	0 59
63	67	1.4	1	0 67
64	56	1.4	1	0 56
65	78	1.4	1	0 78
66	64	1.4	1	1 63
67	55	1.4	1	0 55
68	53	1.4	1	0 53
69	34	1.4	1	0 34
70	53	1.4	1	0 53
71	51	1.4	1	0 51
72	43	1.4	1	0 43
73	49	1.4	1	0 49
74	38	1.4	1	0 38
75	66	1.4	1	0 66
76	76	1.4	1	0 76
77	64	1.4	1	0 64
78	36	1.4	1	0 36
79	45	1.4	1	0 45
80	43	1.4	1	0 43
81	53	1.4	1	0 53
82	44	1.4	1	0 44
83	31	1.4	1	0 31
84	57	1.4	1	0 57
85	48	1.4	1	0 48
86	90	1.4	1	0 90
87	72	1.4	1	1 71
88	56	1.4	1	0 56
89	44	1.4	1	0 44
90	47	1.4	1	0 47
91	58	1.4	1	1 57
92	50	1.4	1	0 50
93	49	1.4	1	0 49
94	57	1.4	1	0 57
95	54	1.4	1	0 54
96	67	1.4	1	0 67
97	77	1.4	1	0 77
98	42	1.4	1	0 42
99	50	1.4	1	1 49
100	54	1.4	1	2 52
101	45	1.4	1	0 45
102	28	1.4	1	0 28
103	38	1.4	1	0 38
104	45	1.4	1	0 45
105	52	1.4	1	0 52
106	49	1.4	1	0 49
107	61	1.4	1	0 61
108	69	1.4	1	0 69
109	39	1.4	1	0 39
110	83	1.4	1	0 83
111	32	1.4	1	0 32
112	50	1.4	1	0 50
113	32	1.4	1	0 32
114	68	1.4	1	0 68
115	64	1.4	1	0 64
116	41	1.4	1	0 41
117	51	1.4	1	0 51
118	58	1.4	1	3 55
119	48	1.4	1	0 48
120	37	1.4	1	0 37
121	55	1.4	1	0 55
122	62	1.4	1	0 62
123	57	1.4	1	0 57
124	55	1.4	1	0 55
125	43	1.4	1	0 43
126	43	1.4	1	0 43
127	49	1.4	1	3 46
128	39	1.4	1	0 39
129	39	1.4	1	0 39
130	47	1.4	1	1 46
131	48	1.4	1	0 48
132	49	1.4	1	0 49
133	85	1.4	1	0 85
134	59	1.4	1	0 59
135	47	1.4	1	2 45
136	39	1.4	1	0 39
137	46	1.4	1	0 46
138	66	1.4	1	0 66
139	69	1.4	1	0 69
140	51	1.4	1	0 51
141	27	1.4	1	0 27
142	57	1.4	1	0 57
143	69	1.4	1	1 68
144	57	1.4	1	0 57
145	56	1.4	1	0 56
146	22	1.4	1	0 22
147	47	1.4	1	0 47
148	33	1.4	1	0 33
149	101	1.4	1	1 100
150	47	1.4	1	0 47

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_1.fq.gz
=============================================
23373579 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0010_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0010_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,373,579
Reads with adapters:                    41,005 (0.2%)
Reads written (passing filters):    23,373,579 (100.0%)

Total basepairs processed: 3,484,037,713 bp
Quality-trimmed:              12,696,095 bp (0.4%)
Total written (filtered):  3,470,585,541 bp (99.6%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41005 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.5%
  C: 20.4%
  G: 24.9%
  T: 33.0%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	22306	22825.8	0	22306
6	6973	5706.4	0	6973
7	2404	1426.6	0	2404
8	841	356.7	0	841
9	509	89.2	0	71 438
10	573	22.3	1	1 572
11	251	5.6	1	0 251
12	70	1.4	1	0 70
13	65	1.4	1	0 65
14	71	1.4	1	1 70
15	66	1.4	1	0 66
16	35	1.4	1	0 35
17	56	1.4	1	0 56
18	38	1.4	1	0 38
19	59	1.4	1	0 59
20	51	1.4	1	0 51
21	38	1.4	1	0 38
22	51	1.4	1	0 51
23	62	1.4	1	0 62
24	58	1.4	1	0 58
25	65	1.4	1	0 65
26	56	1.4	1	0 56
27	31	1.4	1	0 31
28	60	1.4	1	1 59
29	59	1.4	1	0 59
30	45	1.4	1	0 45
31	69	1.4	1	0 69
32	81	1.4	1	1 80
33	49	1.4	1	0 49
34	108	1.4	1	0 108
35	57	1.4	1	0 57
36	51	1.4	1	0 51
37	45	1.4	1	0 45
38	40	1.4	1	0 40
39	50	1.4	1	0 50
40	29	1.4	1	1 28
41	48	1.4	1	0 48
42	38	1.4	1	0 38
43	36	1.4	1	1 35
44	43	1.4	1	0 43
45	68	1.4	1	0 68
46	57	1.4	1	0 57
47	47	1.4	1	0 47
48	65	1.4	1	0 65
49	57	1.4	1	0 57
50	25	1.4	1	0 25
51	62	1.4	1	0 62
52	82	1.4	1	0 82
53	82	1.4	1	0 82
54	53	1.4	1	0 53
55	90	1.4	1	0 90
56	64	1.4	1	1 63
57	42	1.4	1	0 42
58	38	1.4	1	0 38
59	48	1.4	1	0 48
60	27	1.4	1	0 27
61	26	1.4	1	0 26
62	35	1.4	1	0 35
63	63	1.4	1	0 63
64	53	1.4	1	0 53
65	53	1.4	1	0 53
66	62	1.4	1	0 62
67	55	1.4	1	0 55
68	62	1.4	1	0 62
69	48	1.4	1	0 48
70	45	1.4	1	0 45
71	39	1.4	1	0 39
72	41	1.4	1	0 41
73	37	1.4	1	0 37
74	43	1.4	1	0 43
75	66	1.4	1	0 66
76	42	1.4	1	0 42
77	60	1.4	1	0 60
78	38	1.4	1	0 38
79	37	1.4	1	0 37
80	36	1.4	1	0 36
81	71	1.4	1	0 71
82	30	1.4	1	0 30
83	66	1.4	1	0 66
84	51	1.4	1	0 51
85	34	1.4	1	0 34
86	67	1.4	1	0 67
87	72	1.4	1	0 72
88	42	1.4	1	0 42
89	62	1.4	1	0 62
90	73	1.4	1	0 73
91	28	1.4	1	0 28
92	50	1.4	1	0 50
93	65	1.4	1	0 65
94	39	1.4	1	0 39
95	67	1.4	1	0 67
96	67	1.4	1	0 67
97	74	1.4	1	0 74
98	48	1.4	1	0 48
99	38	1.4	1	1 37
100	72	1.4	1	0 72
101	45	1.4	1	0 45
102	28	1.4	1	0 28
103	34	1.4	1	0 34
104	51	1.4	1	0 51
105	41	1.4	1	0 41
106	46	1.4	1	0 46
107	73	1.4	1	0 73
108	52	1.4	1	0 52
109	35	1.4	1	0 35
110	59	1.4	1	0 59
111	48	1.4	1	0 48
112	52	1.4	1	0 52
113	51	1.4	1	0 51
114	60	1.4	1	0 60
115	58	1.4	1	0 58
116	48	1.4	1	0 48
117	44	1.4	1	0 44
118	45	1.4	1	0 45
119	52	1.4	1	0 52
120	18	1.4	1	0 18
121	35	1.4	1	0 35
122	35	1.4	1	0 35
123	56	1.4	1	0 56
124	70	1.4	1	2 68
125	39	1.4	1	0 39
126	48	1.4	1	0 48
127	36	1.4	1	2 34
128	49	1.4	1	0 49
129	20	1.4	1	0 20
130	45	1.4	1	0 45
131	56	1.4	1	0 56
132	33	1.4	1	0 33
133	80	1.4	1	1 79
134	56	1.4	1	0 56
135	40	1.4	1	2 38
136	40	1.4	1	0 40
137	52	1.4	1	0 52
138	54	1.4	1	0 54
139	71	1.4	1	0 71
140	48	1.4	1	0 48
141	32	1.4	1	0 32
142	56	1.4	1	0 56
143	64	1.4	1	0 64
144	68	1.4	1	0 68
145	45	1.4	1	0 45
146	34	1.4	1	0 34
147	52	1.4	1	0 52
148	34	1.4	1	2 32
149	70	1.4	1	0 70
150	46	1.4	1	0 46

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0010_good_2.fq.gz
=============================================
23373579 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0010_good_1_trimmed.fq.gz and Unknown_CK259-004T0010_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0010_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0010_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0010_good_1_trimmed.fq.gz and Unknown_CK259-004T0010_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0010_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0010_good_2_val_2.fq.gz

Total number of sequences analysed: 23373579

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 82860 (0.35%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 19994 (0.09%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0010_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0010_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0010_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0010_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0010_good_1_trimmed.fq.gz and Unknown_CK259-004T0010_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0011_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Nextera	7	CTGTCTCTTATA	1000000	0.00
Illumina	2	AGATCGGAAGAGC	1000000	0.00
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Using Nextera adapter for trimming (count: 7). Second best hit was Illumina (count: 2)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0011_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera Transposase sequence; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0011_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,654,399
Reads with adapters:                    29,612 (0.2%)
Reads written (passing filters):    19,654,399 (100.0%)

Total basepairs processed: 2,927,944,651 bp
Quality-trimmed:                  36,353 bp (0.0%)
Total written (filtered):  2,927,381,500 bp (100.0%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 29612 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 20.8%
  C: 19.3%
  G: 28.1%
  T: 31.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	16760	19193.7	0	16760
6	5350	4798.4	0	5350
7	1349	1199.6	0	1349
8	469	299.9	0	469
9	213	75.0	0	48 165
10	306	18.7	1	2 304
11	166	4.7	1	0 166
12	50	1.2	1	0 50
13	43	1.2	1	0 43
14	66	1.2	1	0 66
15	58	1.2	1	0 58
16	39	1.2	1	1 38
17	77	1.2	1	0 77
18	33	1.2	1	1 32
19	39	1.2	1	1 38
20	19	1.2	1	1 18
21	31	1.2	1	1 30
22	25	1.2	1	1 24
23	58	1.2	1	1 57
24	36	1.2	1	0 36
25	61	1.2	1	0 61
26	35	1.2	1	1 34
27	46	1.2	1	2 44
28	34	1.2	1	0 34
29	37	1.2	1	1 36
30	37	1.2	1	1 36
31	38	1.2	1	2 36
32	40	1.2	1	1 39
33	37	1.2	1	1 36
34	94	1.2	1	0 94
35	38	1.2	1	0 38
36	44	1.2	1	0 44
37	19	1.2	1	0 19
38	17	1.2	1	0 17
39	27	1.2	1	0 27
40	23	1.2	1	0 23
41	38	1.2	1	0 38
42	35	1.2	1	0 35
43	19	1.2	1	0 19
44	23	1.2	1	0 23
45	20	1.2	1	0 20
46	40	1.2	1	0 40
47	35	1.2	1	0 35
48	25	1.2	1	0 25
49	34	1.2	1	0 34
50	32	1.2	1	0 32
51	29	1.2	1	1 28
52	66	1.2	1	0 66
53	60	1.2	1	0 60
54	24	1.2	1	0 24
55	87	1.2	1	2 85
56	27	1.2	1	0 27
57	30	1.2	1	0 30
58	30	1.2	1	0 30
59	31	1.2	1	1 30
60	36	1.2	1	0 36
61	37	1.2	1	2 35
62	27	1.2	1	1 26
63	18	1.2	1	0 18
64	21	1.2	1	0 21
65	25	1.2	1	0 25
66	62	1.2	1	1 61
67	26	1.2	1	0 26
68	35	1.2	1	2 33
69	41	1.2	1	0 41
70	35	1.2	1	0 35
71	19	1.2	1	0 19
72	29	1.2	1	0 29
73	27	1.2	1	0 27
74	31	1.2	1	0 31
75	32	1.2	1	0 32
76	43	1.2	1	3 40
77	36	1.2	1	0 36
78	18	1.2	1	0 18
79	39	1.2	1	0 39
80	32	1.2	1	1 31
81	70	1.2	1	0 70
82	48	1.2	1	0 48
83	43	1.2	1	0 43
84	17	1.2	1	0 17
85	40	1.2	1	0 40
86	58	1.2	1	2 56
87	38	1.2	1	0 38
88	55	1.2	1	0 55
89	21	1.2	1	1 20
90	22	1.2	1	0 22
91	38	1.2	1	1 37
92	29	1.2	1	0 29
93	34	1.2	1	0 34
94	21	1.2	1	0 21
95	38	1.2	1	1 37
96	53	1.2	1	3 50
97	52	1.2	1	5 47
98	29	1.2	1	0 29
99	33	1.2	1	2 31
100	35	1.2	1	0 35
101	38	1.2	1	1 37
102	29	1.2	1	1 28
103	33	1.2	1	1 32
104	24	1.2	1	0 24
105	42	1.2	1	0 42
106	35	1.2	1	0 35
107	31	1.2	1	0 31
108	23	1.2	1	0 23
109	28	1.2	1	0 28
110	30	1.2	1	0 30
111	24	1.2	1	0 24
112	29	1.2	1	0 29
113	28	1.2	1	0 28
114	41	1.2	1	0 41
115	30	1.2	1	1 29
116	45	1.2	1	0 45
117	51	1.2	1	0 51
118	46	1.2	1	0 46
119	33	1.2	1	0 33
120	24	1.2	1	1 23
121	15	1.2	1	0 15
122	32	1.2	1	0 32
123	41	1.2	1	0 41
124	38	1.2	1	0 38
125	31	1.2	1	0 31
126	48	1.2	1	0 48
127	35	1.2	1	1 34
128	37	1.2	1	2 35
129	24	1.2	1	6 18
130	34	1.2	1	2 32
131	16	1.2	1	0 16
132	33	1.2	1	0 33
133	53	1.2	1	4 49
134	50	1.2	1	2 48
135	39	1.2	1	4 35
136	28	1.2	1	2 26
137	37	1.2	1	0 37
138	42	1.2	1	0 42
139	36	1.2	1	3 33
140	37	1.2	1	0 37
141	9	1.2	1	0 9
142	34	1.2	1	0 34
143	32	1.2	1	0 32
144	27	1.2	1	0 27
145	33	1.2	1	1 32
146	14	1.2	1	1 13
147	35	1.2	1	0 35
148	25	1.2	1	0 25
149	55	1.2	1	0 55
150	36	1.2	1	0 36

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_1.fq.gz
=============================================
19654399 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0011_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera Transposase sequence; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0011_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,654,399
Reads with adapters:                    27,106 (0.1%)
Reads written (passing filters):    19,654,399 (100.0%)

Total basepairs processed: 2,927,839,611 bp
Quality-trimmed:                 112,671 bp (0.0%)
Total written (filtered):  2,927,246,873 bp (100.0%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 27106 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.7%
  C: 20.6%
  G: 28.4%
  T: 29.2%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	15821	19193.7	0	15821
6	4680	4798.4	0	4680
7	1001	1199.6	0	1001
8	279	299.9	0	279
9	235	75.0	0	47 188
10	323	18.7	1	1 322
11	166	4.7	1	2 164
12	54	1.2	1	0 54
13	45	1.2	1	0 45
14	72	1.2	1	0 72
15	52	1.2	1	1 51
16	30	1.2	1	1 29
17	70	1.2	1	0 70
18	15	1.2	1	1 14
19	35	1.2	1	2 33
20	25	1.2	1	0 25
21	39	1.2	1	1 38
22	19	1.2	1	1 18
23	45	1.2	1	0 45
24	46	1.2	1	0 46
25	51	1.2	1	0 51
26	33	1.2	1	0 33
27	49	1.2	1	1 48
28	43	1.2	1	1 42
29	27	1.2	1	0 27
30	24	1.2	1	1 23
31	46	1.2	1	0 46
32	54	1.2	1	1 53
33	60	1.2	1	1 59
34	85	1.2	1	0 85
35	44	1.2	1	0 44
36	38	1.2	1	1 37
37	7	1.2	1	1 6
38	19	1.2	1	1 18
39	20	1.2	1	0 20
40	20	1.2	1	0 20
41	17	1.2	1	0 17
42	32	1.2	1	2 30
43	19	1.2	1	3 16
44	36	1.2	1	1 35
45	25	1.2	1	0 25
46	43	1.2	1	1 42
47	18	1.2	1	0 18
48	20	1.2	1	0 20
49	36	1.2	1	0 36
50	42	1.2	1	2 40
51	33	1.2	1	2 31
52	53	1.2	1	0 53
53	53	1.2	1	0 53
54	25	1.2	1	0 25
55	61	1.2	1	1 60
56	25	1.2	1	0 25
57	14	1.2	1	0 14
58	26	1.2	1	0 26
59	33	1.2	1	0 33
60	25	1.2	1	0 25
61	31	1.2	1	1 30
62	31	1.2	1	0 31
63	35	1.2	1	0 35
64	20	1.2	1	0 20
65	17	1.2	1	0 17
66	44	1.2	1	1 43
67	26	1.2	1	0 26
68	27	1.2	1	5 22
69	34	1.2	1	0 34
70	29	1.2	1	1 28
71	14	1.2	1	0 14
72	38	1.2	1	0 38
73	22	1.2	1	0 22
74	19	1.2	1	1 18
75	24	1.2	1	0 24
76	22	1.2	1	1 21
77	41	1.2	1	0 41
78	36	1.2	1	0 36
79	25	1.2	1	0 25
80	31	1.2	1	0 31
81	48	1.2	1	0 48
82	39	1.2	1	0 39
83	21	1.2	1	2 19
84	24	1.2	1	3 21
85	24	1.2	1	1 23
86	49	1.2	1	0 49
87	24	1.2	1	0 24
88	43	1.2	1	1 42
89	37	1.2	1	0 37
90	34	1.2	1	1 33
91	31	1.2	1	0 31
92	30	1.2	1	0 30
93	28	1.2	1	0 28
94	31	1.2	1	0 31
95	37	1.2	1	2 35
96	48	1.2	1	0 48
97	55	1.2	1	2 53
98	34	1.2	1	0 34
99	24	1.2	1	0 24
100	35	1.2	1	0 35
101	32	1.2	1	2 30
102	37	1.2	1	1 36
103	34	1.2	1	1 33
104	28	1.2	1	2 26
105	32	1.2	1	0 32
106	39	1.2	1	1 38
107	46	1.2	1	0 46
108	21	1.2	1	0 21
109	25	1.2	1	0 25
110	42	1.2	1	0 42
111	20	1.2	1	0 20
112	34	1.2	1	0 34
113	26	1.2	1	0 26
114	32	1.2	1	0 32
115	36	1.2	1	0 36
116	29	1.2	1	0 29
117	31	1.2	1	0 31
118	41	1.2	1	0 41
119	16	1.2	1	0 16
120	26	1.2	1	0 26
121	32	1.2	1	0 32
122	27	1.2	1	2 25
123	45	1.2	1	0 45
124	19	1.2	1	0 19
125	32	1.2	1	0 32
126	47	1.2	1	0 47
127	27	1.2	1	2 25
128	21	1.2	1	2 19
129	29	1.2	1	2 27
130	23	1.2	1	4 19
131	19	1.2	1	2 17
132	31	1.2	1	1 30
133	47	1.2	1	4 43
134	51	1.2	1	3 48
135	25	1.2	1	7 18
136	14	1.2	1	2 12
137	36	1.2	1	0 36
138	43	1.2	1	0 43
139	31	1.2	1	3 28
140	26	1.2	1	1 25
141	16	1.2	1	1 15
142	33	1.2	1	0 33
143	36	1.2	1	0 36
144	30	1.2	1	0 30
145	31	1.2	1	2 29
146	8	1.2	1	0 8
147	45	1.2	1	2 43
148	21	1.2	1	0 21
149	30	1.2	1	0 30
150	34	1.2	1	0 34

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0011_good_2.fq.gz
=============================================
19654399 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0011_good_1_trimmed.fq.gz and Unknown_CK259-004T0011_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0011_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0011_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0011_good_1_trimmed.fq.gz and Unknown_CK259-004T0011_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0011_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0011_good_2_val_2.fq.gz

Total number of sequences analysed: 19654399

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 2394 (0.01%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 4 (0.00%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0011_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Approx 100% complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0011_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0011_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Approx 100% complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0011_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0011_good_1_trimmed.fq.gz and Unknown_CK259-004T0011_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0012_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	3	TGGAATTCTCGG	1000000	0.00
Nextera	0	CTGTCTCTTATA	1000000	0.00
Illumina	0	AGATCGGAAGAGC	1000000	0.00
Using smallRNA adapter for trimming (count: 3). Second best hit was Nextera (count: 0)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0012_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0012_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,354,161
Reads with adapters:                    56,515 (0.3%)
Reads written (passing filters):    20,354,161 (100.0%)

Total basepairs processed: 3,037,532,327 bp
Quality-trimmed:                  50,592 bp (0.0%)
Total written (filtered):  3,036,875,528 bp (100.0%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 56515 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.5%
  C: 30.2%
  G: 22.7%
  T: 23.5%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	37422	19877.1	0	37422
6	8440	4969.3	0	8440
7	2962	1242.3	0	2962
8	416	310.6	0	416
9	1322	77.6	0	261 1061
10	1905	19.4	1	29 1876
11	209	4.9	1	1 208
12	28	1.2	1	0 28
13	25	1.2	1	0 25
14	27	1.2	1	0 27
15	17	1.2	1	0 17
16	16	1.2	1	0 16
17	21	1.2	1	1 20
18	30	1.2	1	0 30
19	24	1.2	1	0 24
20	29	1.2	1	0 29
21	23	1.2	1	0 23
22	25	1.2	1	0 25
23	40	1.2	1	0 40
24	41	1.2	1	0 41
25	24	1.2	1	0 24
26	17	1.2	1	0 17
27	34	1.2	1	0 34
28	20	1.2	1	0 20
29	24	1.2	1	0 24
30	20	1.2	1	0 20
31	27	1.2	1	0 27
32	32	1.2	1	0 32
33	41	1.2	1	0 41
34	25	1.2	1	0 25
35	36	1.2	1	0 36
36	24	1.2	1	1 23
37	44	1.2	1	2 42
38	17	1.2	1	1 16
39	39	1.2	1	0 39
40	29	1.2	1	0 29
41	19	1.2	1	0 19
42	40	1.2	1	0 40
43	21	1.2	1	0 21
44	24	1.2	1	0 24
45	24	1.2	1	0 24
46	20	1.2	1	0 20
47	23	1.2	1	0 23
48	18	1.2	1	0 18
49	47	1.2	1	0 47
50	39	1.2	1	0 39
51	16	1.2	1	0 16
52	17	1.2	1	0 17
53	38	1.2	1	0 38
54	30	1.2	1	0 30
55	45	1.2	1	0 45
56	35	1.2	1	0 35
57	17	1.2	1	0 17
58	41	1.2	1	0 41
59	27	1.2	1	0 27
60	37	1.2	1	3 34
61	24	1.2	1	0 24
62	37	1.2	1	0 37
63	44	1.2	1	1 43
64	26	1.2	1	0 26
65	32	1.2	1	0 32
66	19	1.2	1	0 19
67	22	1.2	1	0 22
68	14	1.2	1	0 14
69	36	1.2	1	0 36
70	23	1.2	1	0 23
71	19	1.2	1	0 19
72	24	1.2	1	1 23
73	37	1.2	1	0 37
74	25	1.2	1	0 25
75	52	1.2	1	0 52
76	32	1.2	1	0 32
77	19	1.2	1	0 19
78	29	1.2	1	1 28
79	32	1.2	1	0 32
80	18	1.2	1	0 18
81	31	1.2	1	0 31
82	20	1.2	1	0 20
83	38	1.2	1	0 38
84	19	1.2	1	0 19
85	23	1.2	1	0 23
86	26	1.2	1	0 26
87	31	1.2	1	0 31
88	22	1.2	1	0 22
89	30	1.2	1	0 30
90	29	1.2	1	0 29
91	24	1.2	1	0 24
92	32	1.2	1	0 32
93	34	1.2	1	0 34
94	20	1.2	1	0 20
95	26	1.2	1	0 26
96	23	1.2	1	0 23
97	33	1.2	1	0 33
98	34	1.2	1	0 34
99	27	1.2	1	0 27
100	33	1.2	1	0 33
101	21	1.2	1	0 21
102	30	1.2	1	0 30
103	24	1.2	1	0 24
104	28	1.2	1	0 28
105	17	1.2	1	0 17
106	29	1.2	1	0 29
107	26	1.2	1	0 26
108	25	1.2	1	0 25
109	27	1.2	1	0 27
110	25	1.2	1	0 25
111	30	1.2	1	0 30
112	26	1.2	1	0 26
113	20	1.2	1	0 20
114	32	1.2	1	0 32
115	11	1.2	1	0 11
116	31	1.2	1	0 31
117	30	1.2	1	0 30
118	31	1.2	1	0 31
119	20	1.2	1	0 20
120	27	1.2	1	0 27
121	40	1.2	1	2 38
122	27	1.2	1	0 27
123	33	1.2	1	0 33
124	30	1.2	1	2 28
125	28	1.2	1	0 28
126	15	1.2	1	0 15
127	18	1.2	1	0 18
128	17	1.2	1	0 17
129	38	1.2	1	0 38
130	24	1.2	1	0 24
131	22	1.2	1	0 22
132	22	1.2	1	3 19
133	31	1.2	1	0 31
134	18	1.2	1	1 17
135	14	1.2	1	0 14
136	25	1.2	1	0 25
137	18	1.2	1	0 18
138	27	1.2	1	0 27
139	16	1.2	1	0 16
140	9	1.2	1	0 9
141	35	1.2	1	2 33
142	30	1.2	1	2 28
143	25	1.2	1	0 25
144	65	1.2	1	0 65
145	9	1.2	1	0 9
146	13	1.2	1	0 13
147	33	1.2	1	0 33
148	28	1.2	1	0 28
149	86	1.2	1	1 85
150	37	1.2	1	1 36

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_1.fq.gz
=============================================
20354161 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0012_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0012_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,354,161
Reads with adapters:                    14,153 (0.1%)
Reads written (passing filters):    20,354,161 (100.0%)

Total basepairs processed: 3,037,494,842 bp
Quality-trimmed:                 152,946 bp (0.0%)
Total written (filtered):  3,037,105,733 bp (100.0%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 14153 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 17.8%
  C: 12.0%
  G: 25.8%
  T: 44.4%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
5	6695	19877.1	0	6695
6	3754	4969.3	0	3754
7	762	1242.3	0	762
8	344	310.6	0	344
9	153	77.6	0	9 144
10	229	19.4	1	0 229
11	50	4.9	1	0 50
12	21	1.2	1	6 15
13	15	1.2	1	0 15
14	12	1.2	1	0 12
15	12	1.2	1	0 12
16	26	1.2	1	0 26
17	22	1.2	1	3 19
18	12	1.2	1	3 9
19	21	1.2	1	5 16
20	17	1.2	1	1 16
21	8	1.2	1	1 7
22	16	1.2	1	2 14
23	31	1.2	1	1 30
24	20	1.2	1	0 20
25	16	1.2	1	0 16
26	15	1.2	1	0 15
27	18	1.2	1	2 16
28	10	1.2	1	0 10
29	16	1.2	1	1 15
30	16	1.2	1	0 16
31	8	1.2	1	0 8
32	18	1.2	1	0 18
33	18	1.2	1	6 12
34	11	1.2	1	2 9
35	21	1.2	1	0 21
36	17	1.2	1	2 15
37	14	1.2	1	1 13
38	17	1.2	1	0 17
39	36	1.2	1	3 33
40	10	1.2	1	0 10
41	18	1.2	1	0 18
42	7	1.2	1	0 7
43	19	1.2	1	0 19
44	24	1.2	1	0 24
45	15	1.2	1	0 15
46	10	1.2	1	0 10
47	17	1.2	1	2 15
48	13	1.2	1	0 13
49	13	1.2	1	0 13
50	4	1.2	1	0 4
51	17	1.2	1	1 16
52	20	1.2	1	0 20
53	16	1.2	1	0 16
54	15	1.2	1	0 15
55	21	1.2	1	0 21
56	15	1.2	1	0 15
57	15	1.2	1	0 15
58	23	1.2	1	0 23
59	6	1.2	1	0 6
60	10	1.2	1	0 10
61	31	1.2	1	4 27
62	16	1.2	1	0 16
63	13	1.2	1	1 12
64	19	1.2	1	9 10
65	20	1.2	1	0 20
66	17	1.2	1	1 16
67	12	1.2	1	0 12
68	23	1.2	1	7 16
69	9	1.2	1	1 8
70	20	1.2	1	0 20
71	14	1.2	1	3 11
72	8	1.2	1	0 8
73	14	1.2	1	0 14
74	22	1.2	1	0 22
75	12	1.2	1	0 12
76	19	1.2	1	0 19
77	12	1.2	1	0 12
78	21	1.2	1	0 21
79	19	1.2	1	0 19
80	18	1.2	1	3 15
81	24	1.2	1	3 21
82	27	1.2	1	0 27
83	15	1.2	1	0 15
84	14	1.2	1	2 12
85	21	1.2	1	0 21
86	21	1.2	1	0 21
87	16	1.2	1	1 15
88	24	1.2	1	0 24
89	21	1.2	1	0 21
90	23	1.2	1	1 22
91	14	1.2	1	0 14
92	12	1.2	1	0 12
93	18	1.2	1	0 18
94	10	1.2	1	1 9
95	13	1.2	1	2 11
96	9	1.2	1	0 9
97	13	1.2	1	0 13
98	5	1.2	1	0 5
99	11	1.2	1	0 11
100	17	1.2	1	0 17
101	23	1.2	1	2 21
102	10	1.2	1	4 6
103	11	1.2	1	0 11
104	12	1.2	1	4 8
105	15	1.2	1	0 15
106	24	1.2	1	1 23
107	12	1.2	1	3 9
108	23	1.2	1	1 22
109	12	1.2	1	1 11
110	19	1.2	1	1 18
111	14	1.2	1	1 13
112	34	1.2	1	0 34
113	14	1.2	1	3 11
114	15	1.2	1	0 15
115	13	1.2	1	0 13
116	17	1.2	1	0 17
117	12	1.2	1	2 10
118	4	1.2	1	0 4
119	24	1.2	1	0 24
120	26	1.2	1	1 25
121	13	1.2	1	0 13
122	26	1.2	1	0 26
123	15	1.2	1	4 11
124	7	1.2	1	0 7
125	12	1.2	1	0 12
126	17	1.2	1	0 17
127	10	1.2	1	0 10
128	19	1.2	1	0 19
129	11	1.2	1	0 11
130	17	1.2	1	2 15
131	11	1.2	1	0 11
132	21	1.2	1	8 13
133	11	1.2	1	1 10
134	10	1.2	1	0 10
135	14	1.2	1	0 14
136	22	1.2	1	0 22
137	10	1.2	1	0 10
138	12	1.2	1	2 10
139	8	1.2	1	0 8
140	8	1.2	1	0 8
141	7	1.2	1	0 7
142	8	1.2	1	0 8
143	14	1.2	1	2 12
144	7	1.2	1	0 7
145	18	1.2	1	0 18
146	17	1.2	1	0 17
147	5	1.2	1	0 5
148	4	1.2	1	0 4
149	13	1.2	1	2 11
150	6	1.2	1	0 6

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0012_good_2.fq.gz
=============================================
20354161 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0012_good_1_trimmed.fq.gz and Unknown_CK259-004T0012_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0012_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0012_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0012_good_1_trimmed.fq.gz and Unknown_CK259-004T0012_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0012_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0012_good_2_val_2.fq.gz

Total number of sequences analysed: 20354161

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 3647 (0.02%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 2172 (0.01%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0012_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0012_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0012_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0012_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0012_good_1_trimmed.fq.gz and Unknown_CK259-004T0012_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0013_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Nextera	2	CTGTCTCTTATA	1000000	0.00
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Illumina	0	AGATCGGAAGAGC	1000000	0.00
Using Nextera adapter for trimming (count: 2). Second best hit was smallRNA (count: 1)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0013_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera Transposase sequence; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0013_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,792,337
Reads with adapters:                    33,696 (0.2%)
Reads written (passing filters):    19,792,337 (100.0%)

Total basepairs processed: 2,951,861,521 bp
Quality-trimmed:               1,972,586 bp (0.1%)
Total written (filtered):  2,949,279,390 bp (99.9%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 33696 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 19.8%
  C: 21.1%
  G: 24.7%
  T: 34.3%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	19084	19328.5	0	19084
6	5515	4832.1	0	5515
7	1706	1208.0	0	1706
8	765	302.0	0	765
9	331	75.5	0	74 257
10	415	18.9	1	5 410
11	245	4.7	1	1 244
12	62	1.2	1	0 62
13	48	1.2	1	0 48
14	55	1.2	1	0 55
15	54	1.2	1	1 53
16	47	1.2	1	0 47
17	38	1.2	1	0 38
18	30	1.2	1	0 30
19	30	1.2	1	0 30
20	33	1.2	1	0 33
21	27	1.2	1	0 27
22	27	1.2	1	0 27
23	44	1.2	1	0 44
24	36	1.2	1	0 36
25	46	1.2	1	0 46
26	38	1.2	1	0 38
27	31	1.2	1	1 30
28	46	1.2	1	1 45
29	34	1.2	1	0 34
30	48	1.2	1	1 47
31	57	1.2	1	0 57
32	52	1.2	1	0 52
33	46	1.2	1	0 46
34	64	1.2	1	1 63
35	48	1.2	1	0 48
36	46	1.2	1	0 46
37	31	1.2	1	1 30
38	22	1.2	1	0 22
39	44	1.2	1	2 42
40	41	1.2	1	0 41
41	39	1.2	1	2 37
42	30	1.2	1	0 30
43	45	1.2	1	1 44
44	37	1.2	1	0 37
45	36	1.2	1	1 35
46	48	1.2	1	0 48
47	38	1.2	1	0 38
48	30	1.2	1	0 30
49	38	1.2	1	1 37
50	39	1.2	1	0 39
51	47	1.2	1	0 47
52	53	1.2	1	1 52
53	45	1.2	1	2 43
54	30	1.2	1	2 28
55	79	1.2	1	0 79
56	31	1.2	1	0 31
57	42	1.2	1	0 42
58	33	1.2	1	0 33
59	41	1.2	1	0 41
60	38	1.2	1	1 37
61	42	1.2	1	0 42
62	45	1.2	1	1 44
63	41	1.2	1	0 41
64	20	1.2	1	0 20
65	28	1.2	1	0 28
66	48	1.2	1	1 47
67	39	1.2	1	0 39
68	26	1.2	1	0 26
69	40	1.2	1	0 40
70	48	1.2	1	0 48
71	38	1.2	1	0 38
72	44	1.2	1	0 44
73	44	1.2	1	0 44
74	30	1.2	1	0 30
75	55	1.2	1	0 55
76	35	1.2	1	0 35
77	40	1.2	1	0 40
78	23	1.2	1	1 22
79	45	1.2	1	0 45
80	46	1.2	1	0 46
81	58	1.2	1	0 58
82	29	1.2	1	0 29
83	27	1.2	1	0 27
84	35	1.2	1	0 35
85	43	1.2	1	0 43
86	56	1.2	1	0 56
87	46	1.2	1	0 46
88	44	1.2	1	0 44
89	46	1.2	1	0 46
90	34	1.2	1	0 34
91	45	1.2	1	0 45
92	35	1.2	1	0 35
93	23	1.2	1	0 23
94	26	1.2	1	0 26
95	24	1.2	1	0 24
96	47	1.2	1	0 47
97	53	1.2	1	0 53
98	45	1.2	1	0 45
99	27	1.2	1	0 27
100	38	1.2	1	0 38
101	48	1.2	1	0 48
102	38	1.2	1	2 36
103	36	1.2	1	1 35
104	38	1.2	1	0 38
105	52	1.2	1	0 52
106	39	1.2	1	0 39
107	58	1.2	1	0 58
108	38	1.2	1	0 38
109	30	1.2	1	0 30
110	36	1.2	1	0 36
111	28	1.2	1	0 28
112	53	1.2	1	0 53
113	48	1.2	1	0 48
114	51	1.2	1	0 51
115	41	1.2	1	0 41
116	37	1.2	1	0 37
117	21	1.2	1	0 21
118	33	1.2	1	0 33
119	44	1.2	1	0 44
120	48	1.2	1	0 48
121	34	1.2	1	0 34
122	26	1.2	1	0 26
123	44	1.2	1	0 44
124	44	1.2	1	0 44
125	47	1.2	1	0 47
126	68	1.2	1	0 68
127	51	1.2	1	1 50
128	38	1.2	1	0 38
129	39	1.2	1	2 37
130	35	1.2	1	3 32
131	34	1.2	1	0 34
132	35	1.2	1	0 35
133	46	1.2	1	0 46
134	43	1.2	1	0 43
135	33	1.2	1	1 32
136	46	1.2	1	3 43
137	39	1.2	1	1 38
138	54	1.2	1	0 54
139	34	1.2	1	0 34
140	42	1.2	1	0 42
141	34	1.2	1	0 34
142	46	1.2	1	3 43
143	55	1.2	1	0 55
144	34	1.2	1	0 34
145	40	1.2	1	0 40
146	9	1.2	1	0 9
147	36	1.2	1	0 36
148	27	1.2	1	0 27
149	71	1.2	1	0 71
150	32	1.2	1	0 32

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_1.fq.gz
=============================================
19792337 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0013_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera Transposase sequence; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0013_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,792,337
Reads with adapters:                    32,468 (0.2%)
Reads written (passing filters):    19,792,337 (100.0%)

Total basepairs processed: 2,951,541,755 bp
Quality-trimmed:               4,690,686 bp (0.2%)
Total written (filtered):  2,946,289,642 bp (99.8%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 32468 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 20.6%
  C: 20.9%
  G: 25.2%
  T: 33.2%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	18923	19328.5	0	18923
6	5200	4832.1	0	5200
7	1612	1208.0	0	1612
8	693	302.0	0	693
9	337	75.5	0	71 266
10	379	18.9	1	7 372
11	216	4.7	1	3 213
12	73	1.2	1	0 73
13	31	1.2	1	0 31
14	62	1.2	1	2 60
15	40	1.2	1	1 39
16	29	1.2	1	0 29
17	52	1.2	1	0 52
18	28	1.2	1	0 28
19	46	1.2	1	0 46
20	32	1.2	1	0 32
21	39	1.2	1	0 39
22	38	1.2	1	0 38
23	25	1.2	1	0 25
24	36	1.2	1	0 36
25	32	1.2	1	0 32
26	30	1.2	1	0 30
27	38	1.2	1	1 37
28	37	1.2	1	0 37
29	27	1.2	1	0 27
30	32	1.2	1	0 32
31	51	1.2	1	2 49
32	37	1.2	1	0 37
33	40	1.2	1	0 40
34	59	1.2	1	0 59
35	49	1.2	1	0 49
36	38	1.2	1	0 38
37	38	1.2	1	0 38
38	26	1.2	1	0 26
39	33	1.2	1	0 33
40	35	1.2	1	0 35
41	28	1.2	1	0 28
42	29	1.2	1	0 29
43	40	1.2	1	0 40
44	20	1.2	1	0 20
45	33	1.2	1	0 33
46	40	1.2	1	0 40
47	29	1.2	1	0 29
48	40	1.2	1	0 40
49	46	1.2	1	1 45
50	43	1.2	1	0 43
51	31	1.2	1	1 30
52	38	1.2	1	0 38
53	48	1.2	1	0 48
54	15	1.2	1	0 15
55	63	1.2	1	0 63
56	42	1.2	1	0 42
57	46	1.2	1	0 46
58	26	1.2	1	0 26
59	37	1.2	1	0 37
60	28	1.2	1	0 28
61	36	1.2	1	0 36
62	32	1.2	1	0 32
63	31	1.2	1	0 31
64	30	1.2	1	0 30
65	29	1.2	1	2 27
66	32	1.2	1	0 32
67	32	1.2	1	0 32
68	24	1.2	1	0 24
69	55	1.2	1	0 55
70	37	1.2	1	0 37
71	32	1.2	1	0 32
72	37	1.2	1	0 37
73	44	1.2	1	1 43
74	29	1.2	1	0 29
75	38	1.2	1	0 38
76	37	1.2	1	2 35
77	51	1.2	1	0 51
78	22	1.2	1	1 21
79	38	1.2	1	0 38
80	28	1.2	1	0 28
81	48	1.2	1	0 48
82	30	1.2	1	0 30
83	22	1.2	1	0 22
84	34	1.2	1	0 34
85	45	1.2	1	0 45
86	57	1.2	1	0 57
87	38	1.2	1	0 38
88	34	1.2	1	0 34
89	40	1.2	1	0 40
90	31	1.2	1	0 31
91	59	1.2	1	0 59
92	22	1.2	1	0 22
93	38	1.2	1	0 38
94	24	1.2	1	0 24
95	35	1.2	1	0 35
96	43	1.2	1	0 43
97	37	1.2	1	0 37
98	30	1.2	1	0 30
99	38	1.2	1	0 38
100	30	1.2	1	0 30
101	31	1.2	1	0 31
102	42	1.2	1	0 42
103	33	1.2	1	0 33
104	36	1.2	1	0 36
105	49	1.2	1	0 49
106	37	1.2	1	0 37
107	51	1.2	1	0 51
108	42	1.2	1	0 42
109	47	1.2	1	0 47
110	28	1.2	1	0 28
111	24	1.2	1	0 24
112	49	1.2	1	1 48
113	38	1.2	1	0 38
114	37	1.2	1	0 37
115	29	1.2	1	1 28
116	28	1.2	1	0 28
117	19	1.2	1	0 19
118	36	1.2	1	0 36
119	33	1.2	1	0 33
120	24	1.2	1	0 24
121	35	1.2	1	0 35
122	29	1.2	1	0 29
123	47	1.2	1	0 47
124	33	1.2	1	0 33
125	40	1.2	1	0 40
126	35	1.2	1	1 34
127	63	1.2	1	1 62
128	45	1.2	1	3 42
129	22	1.2	1	2 20
130	41	1.2	1	0 41
131	28	1.2	1	0 28
132	27	1.2	1	0 27
133	43	1.2	1	0 43
134	42	1.2	1	1 41
135	24	1.2	1	0 24
136	45	1.2	1	0 45
137	41	1.2	1	0 41
138	37	1.2	1	0 37
139	38	1.2	1	0 38
140	47	1.2	1	0 47
141	22	1.2	1	0 22
142	43	1.2	1	1 42
143	42	1.2	1	0 42
144	46	1.2	1	0 46
145	33	1.2	1	0 33
146	14	1.2	1	0 14
147	33	1.2	1	0 33
148	28	1.2	1	0 28
149	62	1.2	1	0 62
150	26	1.2	1	0 26

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0013_good_2.fq.gz
=============================================
19792337 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0013_good_1_trimmed.fq.gz and Unknown_CK259-004T0013_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0013_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0013_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0013_good_1_trimmed.fq.gz and Unknown_CK259-004T0013_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0013_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0013_good_2_val_2.fq.gz

Total number of sequences analysed: 19792337

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 30994 (0.16%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 7275 (0.04%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0013_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0013_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0013_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0013_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0013_good_1_trimmed.fq.gz and Unknown_CK259-004T0013_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0014_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	3	TGGAATTCTCGG	1000000	0.00
Nextera	1	CTGTCTCTTATA	1000000	0.00
Illumina	0	AGATCGGAAGAGC	1000000	0.00
Using smallRNA adapter for trimming (count: 3). Second best hit was Nextera (count: 1)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0014_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0014_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,988,588
Reads with adapters:                    57,458 (0.3%)
Reads written (passing filters):    19,988,588 (100.0%)

Total basepairs processed: 2,977,930,364 bp
Quality-trimmed:               1,337,447 bp (0.0%)
Total written (filtered):  2,975,813,779 bp (99.9%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 57458 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 25.5%
  C: 24.0%
  G: 25.1%
  T: 25.3%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	35928	19520.1	0	35928
6	10040	4880.0	0	10040
7	2516	1220.0	0	2516
8	490	305.0	0	490
9	908	76.3	0	118 790
10	1317	19.1	1	22 1295
11	229	4.8	1	3 226
12	39	1.2	1	0 39
13	44	1.2	1	0 44
14	33	1.2	1	0 33
15	26	1.2	1	0 26
16	58	1.2	1	0 58
17	21	1.2	1	0 21
18	17	1.2	1	0 17
19	26	1.2	1	0 26
20	109	1.2	1	0 109
21	24	1.2	1	0 24
22	37	1.2	1	0 37
23	35	1.2	1	0 35
24	69	1.2	1	0 69
25	33	1.2	1	0 33
26	19	1.2	1	0 19
27	31	1.2	1	0 31
28	18	1.2	1	0 18
29	32	1.2	1	0 32
30	33	1.2	1	0 33
31	47	1.2	1	0 47
32	58	1.2	1	0 58
33	49	1.2	1	0 49
34	29	1.2	1	1 28
35	30	1.2	1	0 30
36	35	1.2	1	0 35
37	21	1.2	1	0 21
38	29	1.2	1	0 29
39	23	1.2	1	0 23
40	58	1.2	1	0 58
41	29	1.2	1	0 29
42	58	1.2	1	0 58
43	43	1.2	1	0 43
44	50	1.2	1	0 50
45	51	1.2	1	1 50
46	77	1.2	1	0 77
47	65	1.2	1	0 65
48	39	1.2	1	0 39
49	54	1.2	1	0 54
50	24	1.2	1	0 24
51	67	1.2	1	0 67
52	37	1.2	1	0 37
53	171	1.2	1	0 171
54	44	1.2	1	0 44
55	42	1.2	1	0 42
56	52	1.2	1	0 52
57	32	1.2	1	0 32
58	71	1.2	1	0 71
59	36	1.2	1	0 36
60	18	1.2	1	0 18
61	21	1.2	1	0 21
62	45	1.2	1	0 45
63	33	1.2	1	0 33
64	53	1.2	1	0 53
65	28	1.2	1	0 28
66	28	1.2	1	0 28
67	26	1.2	1	2 24
68	26	1.2	1	1 25
69	34	1.2	1	0 34
70	27	1.2	1	0 27
71	29	1.2	1	0 29
72	96	1.2	1	0 96
73	43	1.2	1	1 42
74	44	1.2	1	0 44
75	45	1.2	1	0 45
76	41	1.2	1	1 40
77	60	1.2	1	0 60
78	72	1.2	1	0 72
79	44	1.2	1	0 44
80	44	1.2	1	0 44
81	45	1.2	1	0 45
82	29	1.2	1	0 29
83	41	1.2	1	0 41
84	49	1.2	1	0 49
85	27	1.2	1	0 27
86	39	1.2	1	0 39
87	39	1.2	1	0 39
88	137	1.2	1	1 136
89	35	1.2	1	0 35
90	31	1.2	1	0 31
91	34	1.2	1	0 34
92	31	1.2	1	1 30
93	59	1.2	1	0 59
94	39	1.2	1	0 39
95	15	1.2	1	0 15
96	24	1.2	1	1 23
97	33	1.2	1	0 33
98	34	1.2	1	0 34
99	58	1.2	1	0 58
100	52	1.2	1	0 52
101	60	1.2	1	0 60
102	41	1.2	1	0 41
103	39	1.2	1	0 39
104	29	1.2	1	0 29
105	31	1.2	1	0 31
106	55	1.2	1	0 55
107	49	1.2	1	0 49
108	82	1.2	1	0 82
109	39	1.2	1	0 39
110	36	1.2	1	0 36
111	50	1.2	1	0 50
112	44	1.2	1	0 44
113	56	1.2	1	2 54
114	35	1.2	1	1 34
115	18	1.2	1	0 18
116	63	1.2	1	0 63
117	49	1.2	1	0 49
118	33	1.2	1	0 33
119	23	1.2	1	0 23
120	32	1.2	1	0 32
121	75	1.2	1	1 74
122	38	1.2	1	0 38
123	69	1.2	1	0 69
124	55	1.2	1	0 55
125	43	1.2	1	1 42
126	23	1.2	1	0 23
127	48	1.2	1	0 48
128	60	1.2	1	0 60
129	33	1.2	1	0 33
130	31	1.2	1	0 31
131	31	1.2	1	0 31
132	32	1.2	1	0 32
133	35	1.2	1	0 35
134	44	1.2	1	1 43
135	43	1.2	1	1 42
136	48	1.2	1	0 48
137	76	1.2	1	0 76
138	38	1.2	1	0 38
139	23	1.2	1	0 23
140	21	1.2	1	1 20
141	72	1.2	1	0 72
142	27	1.2	1	0 27
143	28	1.2	1	0 28
144	42	1.2	1	0 42
145	10	1.2	1	0 10
146	10	1.2	1	0 10
147	15	1.2	1	0 15
148	69	1.2	1	0 69
149	125	1.2	1	1 124
150	69	1.2	1	0 69

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_1.fq.gz
=============================================
19988588 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0014_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0014_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,988,588
Reads with adapters:                    13,809 (0.1%)
Reads written (passing filters):    19,988,588 (100.0%)

Total basepairs processed: 2,977,721,140 bp
Quality-trimmed:               2,974,640 bp (0.1%)
Total written (filtered):  2,974,548,327 bp (99.9%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 13809 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 12.5%
  C: 19.0%
  G: 26.3%
  T: 42.0%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	7512	19520.1	0	7512
6	3209	4880.0	0	3209
7	917	1220.0	0	917
8	93	305.0	0	93
9	194	76.3	0	29 165
10	175	19.1	1	11 164
11	41	4.8	1	1 40
12	12	1.2	1	3 9
13	11	1.2	1	1 10
14	18	1.2	1	1 17
15	18	1.2	1	3 15
16	11	1.2	1	1 10
17	30	1.2	1	2 28
18	11	1.2	1	3 8
19	17	1.2	1	5 12
20	14	1.2	1	0 14
21	7	1.2	1	3 4
22	13	1.2	1	2 11
23	13	1.2	1	1 12
24	15	1.2	1	1 14
25	12	1.2	1	3 9
26	10	1.2	1	0 10
27	11	1.2	1	0 11
28	12	1.2	1	0 12
29	13	1.2	1	1 12
30	10	1.2	1	0 10
31	6	1.2	1	0 6
32	8	1.2	1	0 8
33	9	1.2	1	3 6
34	7	1.2	1	0 7
35	11	1.2	1	0 11
36	23	1.2	1	0 23
37	15	1.2	1	1 14
38	24	1.2	1	0 24
39	29	1.2	1	1 28
40	21	1.2	1	2 19
41	7	1.2	1	0 7
42	11	1.2	1	0 11
43	17	1.2	1	0 17
44	15	1.2	1	0 15
45	9	1.2	1	2 7
46	23	1.2	1	1 22
47	11	1.2	1	1 10
48	15	1.2	1	1 14
49	9	1.2	1	0 9
50	10	1.2	1	0 10
51	25	1.2	1	0 25
52	10	1.2	1	0 10
53	14	1.2	1	2 12
54	11	1.2	1	0 11
55	19	1.2	1	0 19
56	12	1.2	1	0 12
57	7	1.2	1	0 7
58	20	1.2	1	1 19
59	6	1.2	1	2 4
60	3	1.2	1	0 3
61	6	1.2	1	1 5
62	4	1.2	1	0 4
63	21	1.2	1	1 20
64	19	1.2	1	7 12
65	15	1.2	1	0 15
66	17	1.2	1	5 12
67	9	1.2	1	0 9
68	10	1.2	1	1 9
69	11	1.2	1	5 6
70	17	1.2	1	0 17
71	12	1.2	1	1 11
72	6	1.2	1	1 5
73	6	1.2	1	0 6
74	8	1.2	1	0 8
75	5	1.2	1	0 5
76	9	1.2	1	0 9
77	7	1.2	1	0 7
78	11	1.2	1	1 10
79	10	1.2	1	1 9
80	8	1.2	1	0 8
81	11	1.2	1	2 9
82	8	1.2	1	1 7
83	8	1.2	1	1 7
84	7	1.2	1	0 7
85	17	1.2	1	0 17
86	11	1.2	1	0 11
87	13	1.2	1	0 13
88	19	1.2	1	1 18
89	11	1.2	1	1 10
90	18	1.2	1	2 16
91	11	1.2	1	0 11
92	3	1.2	1	0 3
93	13	1.2	1	0 13
94	9	1.2	1	0 9
95	6	1.2	1	0 6
96	10	1.2	1	0 10
97	7	1.2	1	0 7
98	9	1.2	1	0 9
99	6	1.2	1	0 6
100	9	1.2	1	1 8
101	15	1.2	1	2 13
102	3	1.2	1	1 2
103	9	1.2	1	0 9
104	9	1.2	1	1 8
105	4	1.2	1	0 4
106	8	1.2	1	0 8
107	16	1.2	1	4 12
108	12	1.2	1	0 12
109	18	1.2	1	2 16
110	10	1.2	1	1 9
111	14	1.2	1	1 13
112	11	1.2	1	0 11
113	5	1.2	1	0 5
114	7	1.2	1	1 6
115	7	1.2	1	0 7
116	16	1.2	1	1 15
117	5	1.2	1	0 5
118	8	1.2	1	1 7
119	9	1.2	1	1 8
120	17	1.2	1	0 17
121	12	1.2	1	0 12
122	15	1.2	1	1 14
123	11	1.2	1	1 10
124	7	1.2	1	2 5
125	10	1.2	1	1 9
126	33	1.2	1	5 28
127	14	1.2	1	1 13
128	24	1.2	1	4 20
129	23	1.2	1	6 17
130	15	1.2	1	3 12
131	17	1.2	1	7 10
132	26	1.2	1	8 18
133	12	1.2	1	2 10
134	29	1.2	1	5 24
135	12	1.2	1	1 11
136	10	1.2	1	2 8
137	9	1.2	1	3 6
138	9	1.2	1	2 7
139	10	1.2	1	0 10
140	10	1.2	1	1 9
141	8	1.2	1	0 8
142	11	1.2	1	0 11
143	5	1.2	1	1 4
144	7	1.2	1	0 7
146	16	1.2	1	1 15
147	7	1.2	1	0 7
148	3	1.2	1	0 3
149	8	1.2	1	0 8
150	10	1.2	1	1 9

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0014_good_2.fq.gz
=============================================
19988588 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0014_good_1_trimmed.fq.gz and Unknown_CK259-004T0014_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0014_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0014_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0014_good_1_trimmed.fq.gz and Unknown_CK259-004T0014_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0014_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0014_good_2_val_2.fq.gz

Total number of sequences analysed: 19988588

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 23350 (0.12%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 7644 (0.04%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0014_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0014_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0014_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0014_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0014_good_1_trimmed.fq.gz and Unknown_CK259-004T0014_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0015_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	5	TGGAATTCTCGG	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Illumina	2	AGATCGGAAGAGC	1000000	0.00
Using smallRNA adapter for trimming (count: 5). Second best hit was Nextera (count: 2)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0015_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0015_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,917,243
Reads with adapters:                    60,606 (0.3%)
Reads written (passing filters):    19,917,243 (100.0%)

Total basepairs processed: 2,932,683,807 bp
Quality-trimmed:               4,577,674 bp (0.2%)
Total written (filtered):  2,927,072,056 bp (99.8%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 60606 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.6%
  C: 22.4%
  G: 25.1%
  T: 27.6%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	36746	19450.4	0	36746
6	9158	4862.6	0	9158
7	2501	1215.7	0	2501
8	426	303.9	0	426
9	1098	76.0	0	157 941
10	1082	19.0	1	49 1033
11	400	4.7	1	5 395
12	125	1.2	1	1 124
13	61	1.2	1	0 61
14	37	1.2	1	0 37
15	43	1.2	1	0 43
16	60	1.2	1	0 60
17	65	1.2	1	0 65
18	36	1.2	1	1 35
19	118	1.2	1	0 118
20	41	1.2	1	1 40
21	38	1.2	1	0 38
22	30	1.2	1	0 30
23	73	1.2	1	0 73
24	70	1.2	1	2 68
25	66	1.2	1	0 66
26	37	1.2	1	0 37
27	97	1.2	1	0 97
28	61	1.2	1	0 61
29	52	1.2	1	0 52
30	70	1.2	1	0 70
31	72	1.2	1	0 72
32	98	1.2	1	0 98
33	131	1.2	1	9 122
34	45	1.2	1	4 41
35	94	1.2	1	0 94
36	64	1.2	1	0 64
37	54	1.2	1	1 53
38	58	1.2	1	1 57
39	64	1.2	1	0 64
40	51	1.2	1	0 51
41	50	1.2	1	1 49
42	82	1.2	1	2 80
43	55	1.2	1	11 44
44	67	1.2	1	0 67
45	47	1.2	1	0 47
46	61	1.2	1	0 61
47	86	1.2	1	0 86
48	78	1.2	1	0 78
49	124	1.2	1	0 124
50	42	1.2	1	0 42
51	70	1.2	1	0 70
52	83	1.2	1	0 83
53	56	1.2	1	1 55
54	51	1.2	1	6 45
55	87	1.2	1	0 87
56	52	1.2	1	1 51
57	44	1.2	1	0 44
58	98	1.2	1	0 98
59	82	1.2	1	0 82
60	68	1.2	1	0 68
61	55	1.2	1	0 55
62	62	1.2	1	0 62
63	57	1.2	1	0 57
64	42	1.2	1	1 41
65	55	1.2	1	0 55
66	31	1.2	1	0 31
67	71	1.2	1	0 71
68	42	1.2	1	0 42
69	114	1.2	1	0 114
70	60	1.2	1	0 60
71	45	1.2	1	0 45
72	77	1.2	1	0 77
73	41	1.2	1	0 41
74	59	1.2	1	0 59
75	45	1.2	1	0 45
76	93	1.2	1	0 93
77	33	1.2	1	0 33
78	55	1.2	1	0 55
79	58	1.2	1	0 58
80	88	1.2	1	0 88
81	55	1.2	1	0 55
82	52	1.2	1	0 52
83	51	1.2	1	0 51
84	64	1.2	1	0 64
85	58	1.2	1	0 58
86	64	1.2	1	0 64
87	112	1.2	1	12 100
88	54	1.2	1	0 54
89	78	1.2	1	0 78
90	49	1.2	1	0 49
91	42	1.2	1	0 42
92	108	1.2	1	21 87
93	87	1.2	1	0 87
94	50	1.2	1	0 50
95	82	1.2	1	0 82
96	78	1.2	1	0 78
97	71	1.2	1	0 71
98	79	1.2	1	0 79
99	65	1.2	1	0 65
100	100	1.2	1	0 100
101	43	1.2	1	0 43
102	90	1.2	1	0 90
103	68	1.2	1	0 68
104	74	1.2	1	0 74
105	49	1.2	1	0 49
106	57	1.2	1	0 57
107	38	1.2	1	0 38
108	74	1.2	1	0 74
109	93	1.2	1	0 93
110	59	1.2	1	0 59
111	29	1.2	1	0 29
112	91	1.2	1	0 91
113	79	1.2	1	0 79
114	38	1.2	1	0 38
115	49	1.2	1	0 49
116	101	1.2	1	0 101
117	62	1.2	1	1 61
118	68	1.2	1	0 68
119	71	1.2	1	0 71
120	64	1.2	1	0 64
121	52	1.2	1	0 52
122	61	1.2	1	0 61
123	81	1.2	1	0 81
124	59	1.2	1	0 59
125	81	1.2	1	3 78
126	33	1.2	1	0 33
127	52	1.2	1	0 52
128	79	1.2	1	0 79
129	99	1.2	1	0 99
130	102	1.2	1	0 102
131	65	1.2	1	0 65
132	68	1.2	1	7 61
133	74	1.2	1	0 74
134	86	1.2	1	0 86
135	70	1.2	1	0 70
136	55	1.2	1	0 55
137	60	1.2	1	0 60
138	43	1.2	1	0 43
139	75	1.2	1	0 75
140	66	1.2	1	0 66
141	99	1.2	1	0 99
142	63	1.2	1	0 63
143	38	1.2	1	0 38
144	50	1.2	1	0 50
145	7	1.2	1	0 7
146	17	1.2	1	0 17
147	25	1.2	1	1 24
148	77	1.2	1	0 77
149	173	1.2	1	3 170
150	112	1.2	1	0 112

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_1.fq.gz
=============================================
19917243 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0015_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0015_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,917,243
Reads with adapters:                    17,648 (0.1%)
Reads written (passing filters):    19,917,243 (100.0%)

Total basepairs processed: 2,933,065,045 bp
Quality-trimmed:              10,684,731 bp (0.4%)
Total written (filtered):  2,921,932,560 bp (99.6%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 17648 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 15.9%
  C: 23.0%
  G: 24.2%
  T: 36.7%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	8196	19450.4	0	8196
6	2960	4862.6	0	2960
7	732	1215.7	0	732
8	164	303.9	0	164
9	246	76.0	0	29 217
10	306	19.0	1	8 298
11	145	4.7	1	1 144
12	48	1.2	1	4 44
13	32	1.2	1	0 32
14	37	1.2	1	2 35
15	25	1.2	1	0 25
16	52	1.2	1	0 52
17	71	1.2	1	0 71
18	56	1.2	1	0 56
19	41	1.2	1	1 40
20	64	1.2	1	1 63
21	37	1.2	1	0 37
22	49	1.2	1	0 49
23	52	1.2	1	0 52
24	30	1.2	1	1 29
25	32	1.2	1	0 32
26	47	1.2	1	1 46
27	41	1.2	1	0 41
28	31	1.2	1	0 31
29	46	1.2	1	0 46
30	31	1.2	1	2 29
31	13	1.2	1	0 13
32	39	1.2	1	1 38
33	43	1.2	1	1 42
34	32	1.2	1	0 32
35	34	1.2	1	0 34
36	34	1.2	1	1 33
37	14	1.2	1	0 14
38	34	1.2	1	1 33
39	37	1.2	1	0 37
40	47	1.2	1	0 47
41	24	1.2	1	0 24
42	31	1.2	1	3 28
43	38	1.2	1	0 38
44	69	1.2	1	0 69
45	75	1.2	1	0 75
46	43	1.2	1	0 43
47	24	1.2	1	0 24
48	80	1.2	1	0 80
49	48	1.2	1	0 48
50	15	1.2	1	1 14
51	66	1.2	1	0 66
52	24	1.2	1	0 24
53	45	1.2	1	0 45
54	38	1.2	1	3 35
55	65	1.2	1	4 61
56	46	1.2	1	2 44
57	35	1.2	1	0 35
58	26	1.2	1	1 25
59	25	1.2	1	0 25
60	30	1.2	1	0 30
61	29	1.2	1	1 28
62	21	1.2	1	2 19
63	24	1.2	1	3 21
64	29	1.2	1	7 22
65	67	1.2	1	1 66
66	32	1.2	1	8 24
67	39	1.2	1	0 39
68	66	1.2	1	0 66
69	22	1.2	1	2 20
70	38	1.2	1	0 38
71	34	1.2	1	0 34
72	23	1.2	1	0 23
73	12	1.2	1	0 12
74	33	1.2	1	1 32
75	18	1.2	1	0 18
76	27	1.2	1	0 27
77	22	1.2	1	0 22
78	18	1.2	1	0 18
79	25	1.2	1	0 25
80	18	1.2	1	0 18
81	4	1.2	1	0 4
82	22	1.2	1	3 19
83	42	1.2	1	5 37
84	27	1.2	1	0 27
85	22	1.2	1	0 22
86	38	1.2	1	0 38
87	24	1.2	1	3 21
88	48	1.2	1	0 48
89	40	1.2	1	1 39
90	49	1.2	1	0 49
91	33	1.2	1	0 33
92	39	1.2	1	0 39
93	38	1.2	1	1 37
94	39	1.2	1	0 39
95	44	1.2	1	0 44
96	33	1.2	1	0 33
97	50	1.2	1	0 50
98	28	1.2	1	3 25
99	45	1.2	1	1 44
100	51	1.2	1	0 51
101	29	1.2	1	1 28
102	29	1.2	1	5 24
103	61	1.2	1	3 58
104	19	1.2	1	1 18
105	19	1.2	1	0 19
106	14	1.2	1	0 14
107	33	1.2	1	1 32
108	30	1.2	1	3 27
109	19	1.2	1	2 17
110	18	1.2	1	0 18
111	38	1.2	1	0 38
112	27	1.2	1	1 26
113	73	1.2	1	0 73
114	25	1.2	1	1 24
115	49	1.2	1	1 48
116	35	1.2	1	1 34
117	25	1.2	1	1 24
118	22	1.2	1	3 19
119	64	1.2	1	3 61
120	33	1.2	1	1 32
121	45	1.2	1	1 44
122	44	1.2	1	0 44
123	28	1.2	1	2 26
124	31	1.2	1	1 30
125	35	1.2	1	4 31
126	52	1.2	1	9 43
127	46	1.2	1	0 46
128	44	1.2	1	5 39
129	64	1.2	1	9 55
130	32	1.2	1	6 26
131	19	1.2	1	3 16
132	36	1.2	1	8 28
133	27	1.2	1	4 23
134	38	1.2	1	5 33
135	24	1.2	1	3 21
136	27	1.2	1	3 24
137	12	1.2	1	0 12
138	38	1.2	1	0 38
139	15	1.2	1	0 15
140	37	1.2	1	1 36
141	22	1.2	1	0 22
142	25	1.2	1	0 25
143	5	1.2	1	0 5
144	22	1.2	1	0 22
145	21	1.2	1	0 21
146	56	1.2	1	0 56
147	29	1.2	1	0 29
148	25	1.2	1	0 25
149	12	1.2	1	1 11
150	17	1.2	1	0 17

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0015_good_2.fq.gz
=============================================
19917243 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0015_good_1_trimmed.fq.gz and Unknown_CK259-004T0015_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0015_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0015_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0015_good_1_trimmed.fq.gz and Unknown_CK259-004T0015_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0015_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0015_good_2_val_2.fq.gz

Total number of sequences analysed: 19917243

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 75154 (0.38%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 16638 (0.08%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0015_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0015_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0015_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0015_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0015_good_1_trimmed.fq.gz and Unknown_CK259-004T0015_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0016_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	3	AGATCGGAAGAGC	1000000	0.00
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Nextera	0	CTGTCTCTTATA	1000000	0.00
Using Illumina adapter for trimming (count: 3). Second best hit was smallRNA (count: 2)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0016_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0016_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              21,355,094
Reads with adapters:                     4,147 (0.0%)
Reads written (passing filters):    21,355,094 (100.0%)

Total basepairs processed: 3,149,472,645 bp
Quality-trimmed:               6,119,021 bp (0.2%)
Total written (filtered):  3,143,174,951 bp (99.8%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 4147 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 26.2%
  C: 26.3%
  G: 38.4%
  T: 8.8%
  none/other: 0.3%

Overview of removed sequences
length	count	expect	max.err	error counts
5	629	20854.6	0	629
6	100	5213.6	0	100
7	3	1303.4	0	3
8	3	325.9	0	3
9	587	81.5	0	3 584
10	201	20.4	1	1 200
11	105	5.1	1	1 104
12	38	1.3	1	4 34
13	16	0.3	1	1 15
14	37	0.3	1	0 37
15	42	0.3	1	1 41
16	19	0.3	1	1 18
17	43	0.3	1	1 42
18	6	0.3	1	0 6
19	21	0.3	1	3 18
20	14	0.3	1	0 14
21	20	0.3	1	4 16
22	24	0.3	1	0 24
23	13	0.3	1	3 10
24	15	0.3	1	2 13
25	24	0.3	1	2 22
26	58	0.3	1	0 58
27	18	0.3	1	4 14
28	32	0.3	1	0 32
29	16	0.3	1	4 12
30	62	0.3	1	7 55
31	16	0.3	1	3 13
32	17	0.3	1	5 12
33	9	0.3	1	0 9
34	6	0.3	1	0 6
35	50	0.3	1	3 47
36	12	0.3	1	2 10
37	24	0.3	1	6 18
38	39	0.3	1	1 38
39	16	0.3	1	1 15
40	38	0.3	1	4 34
41	24	0.3	1	3 21
42	39	0.3	1	13 26
43	34	0.3	1	2 32
44	14	0.3	1	5 9
45	31	0.3	1	10 21
46	32	0.3	1	7 25
47	31	0.3	1	7 24
48	41	0.3	1	6 35
49	51	0.3	1	10 41
50	33	0.3	1	7 26
51	47	0.3	1	4 43
52	34	0.3	1	4 30
53	53	0.3	1	8 45
54	28	0.3	1	2 26
55	23	0.3	1	5 18
56	20	0.3	1	2 18
57	12	0.3	1	0 12
58	36	0.3	1	2 34
59	9	0.3	1	2 7
60	9	0.3	1	3 6
61	10	0.3	1	2 8
62	13	0.3	1	2 11
63	12	0.3	1	3 9
64	8	0.3	1	0 8
65	2	0.3	1	0 2
66	34	0.3	1	0 34
67	18	0.3	1	3 15
68	14	0.3	1	0 14
69	39	0.3	1	3 36
70	25	0.3	1	2 23
71	16	0.3	1	5 11
72	76	0.3	1	3 73
73	11	0.3	1	2 9
74	11	0.3	1	1 10
75	8	0.3	1	2 6
76	5	0.3	1	0 5
77	34	0.3	1	0 34
78	12	0.3	1	3 9
79	8	0.3	1	2 6
80	12	0.3	1	0 12
81	10	0.3	1	1 9
82	29	0.3	1	1 28
83	7	0.3	1	1 6
84	3	0.3	1	0 3
85	12	0.3	1	0 12
86	5	0.3	1	0 5
87	55	0.3	1	1 54
88	1	0.3	1	0 1
89	2	0.3	1	1 1
90	8	0.3	1	0 8
91	7	0.3	1	0 7
92	20	0.3	1	1 19
93	5	0.3	1	0 5
94	8	0.3	1	1 7
95	3	0.3	1	0 3
96	3	0.3	1	0 3
97	12	0.3	1	0 12
98	30	0.3	1	0 30
99	6	0.3	1	0 6
100	15	0.3	1	0 15
101	19	0.3	1	0 19
102	7	0.3	1	0 7
103	14	0.3	1	0 14
104	9	0.3	1	0 9
105	16	0.3	1	0 16
106	16	0.3	1	0 16
107	11	0.3	1	0 11
108	1	0.3	1	1
109	12	0.3	1	1 11
110	4	0.3	1	0 4
111	43	0.3	1	0 43
113	1	0.3	1	0 1
114	7	0.3	1	0 7
115	27	0.3	1	0 27
116	13	0.3	1	0 13
117	8	0.3	1	0 8
118	32	0.3	1	0 32
119	6	0.3	1	1 5
120	11	0.3	1	0 11
121	7	0.3	1	0 7
122	19	0.3	1	0 19
123	8	0.3	1	0 8
124	6	0.3	1	0 6
125	28	0.3	1	0 28
126	2	0.3	1	0 2
127	38	0.3	1	0 38
128	10	0.3	1	0 10
129	10	0.3	1	0 10
130	8	0.3	1	0 8
131	1	0.3	1	1
132	1	0.3	1	0 1
133	1	0.3	1	0 1
134	6	0.3	1	1 5
135	1	0.3	1	0 1
136	40	0.3	1	0 40
137	27	0.3	1	0 27
138	4	0.3	1	0 4
139	4	0.3	1	0 4
140	5	0.3	1	0 5
141	4	0.3	1	0 4
142	10	0.3	1	0 10
143	21	0.3	1	0 21
144	1	0.3	1	0 1
145	8	0.3	1	0 8
146	1	0.3	1	0 1
147	26	0.3	1	0 26
148	6	0.3	1	0 6
150	4	0.3	1	0 4

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_1.fq.gz
=============================================
21355094 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0016_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0016_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              21,355,094
Reads with adapters:                     4,458 (0.0%)
Reads written (passing filters):    21,355,094 (100.0%)

Total basepairs processed: 3,150,011,955 bp
Quality-trimmed:              14,259,081 bp (0.5%)
Total written (filtered):  3,135,582,653 bp (99.5%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 4458 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 27.6%
  C: 28.3%
  G: 34.2%
  T: 9.6%
  none/other: 0.3%

Overview of removed sequences
length	count	expect	max.err	error counts
5	633	20854.6	0	633
6	113	5213.6	0	113
7	17	1303.4	0	17
8	9	325.9	0	9
9	667	81.5	0	18 649
10	201	20.4	1	6 195
11	158	5.1	1	10 148
12	41	1.3	1	8 33
13	21	0.3	1	1 20
14	29	0.3	1	9 20
15	39	0.3	1	9 30
16	19	0.3	1	3 16
17	57	0.3	1	8 49
18	24	0.3	1	15 9
19	79	0.3	1	36 43
20	28	0.3	1	10 18
21	14	0.3	1	2 12
22	37	0.3	1	17 20
23	56	0.3	1	18 38
24	67	0.3	1	24 43
25	41	0.3	1	10 31
26	44	0.3	1	13 31
27	53	0.3	1	21 32
28	39	0.3	1	13 26
29	27	0.3	1	8 19
30	37	0.3	1	6 31
31	26	0.3	1	9 17
32	40	0.3	1	18 22
33	21	0.3	1	6 15
34	44	0.3	1	12 32
35	57	0.3	1	22 35
36	30	0.3	1	16 14
37	33	0.3	1	10 23
38	63	0.3	1	10 53
39	37	0.3	1	7 30
40	23	0.3	1	2 21
41	25	0.3	1	9 16
42	42	0.3	1	10 32
43	36	0.3	1	15 21
44	30	0.3	1	7 23
45	36	0.3	1	8 28
46	40	0.3	1	21 19
47	46	0.3	1	14 32
48	56	0.3	1	7 49
49	17	0.3	1	4 13
50	31	0.3	1	12 19
51	13	0.3	1	4 9
52	16	0.3	1	2 14
53	10	0.3	1	3 7
54	20	0.3	1	3 17
55	21	0.3	1	2 19
56	9	0.3	1	5 4
57	7	0.3	1	0 7
58	24	0.3	1	4 20
59	32	0.3	1	2 30
60	10	0.3	1	3 7
61	13	0.3	1	1 12
62	5	0.3	1	0 5
63	18	0.3	1	2 16
64	6	0.3	1	2 4
65	4	0.3	1	2 2
66	14	0.3	1	1 13
67	19	0.3	1	2 17
68	11	0.3	1	2 9
69	26	0.3	1	1 25
70	5	0.3	1	1 4
71	13	0.3	1	4 9
72	62	0.3	1	4 58
73	21	0.3	1	5 16
74	21	0.3	1	1 20
75	4	0.3	1	1 3
76	13	0.3	1	4 9
77	18	0.3	1	5 13
78	16	0.3	1	6 10
79	6	0.3	1	4 2
80	11	0.3	1	2 9
81	12	0.3	1	1 11
82	28	0.3	1	1 27
83	21	0.3	1	2 19
84	5	0.3	1	1 4
85	6	0.3	1	0 6
86	11	0.3	1	4 7
87	27	0.3	1	2 25
88	9	0.3	1	1 8
89	5	0.3	1	2 3
90	1	0.3	1	1
91	9	0.3	1	1 8
92	32	0.3	1	1 31
93	4	0.3	1	1 3
94	2	0.3	1	1 1
95	4	0.3	1	0 4
96	3	0.3	1	1 2
97	9	0.3	1	1 8
98	19	0.3	1	1 18
99	5	0.3	1	0 5
100	6	0.3	1	1 5
101	26	0.3	1	0 26
102	12	0.3	1	0 12
103	1	0.3	1	0 1
104	9	0.3	1	1 8
105	18	0.3	1	0 18
106	13	0.3	1	0 13
107	3	0.3	1	0 3
108	1	0.3	1	1
109	10	0.3	1	0 10
110	11	0.3	1	0 11
111	10	0.3	1	0 10
112	1	0.3	1	0 1
113	5	0.3	1	3 2
114	3	0.3	1	0 3
115	2	0.3	1	0 2
116	21	0.3	1	0 21
117	6	0.3	1	0 6
118	13	0.3	1	0 13
119	12	0.3	1	1 11
120	22	0.3	1	0 22
121	6	0.3	1	0 6
122	17	0.3	1	0 17
123	14	0.3	1	0 14
124	6	0.3	1	0 6
125	24	0.3	1	0 24
126	18	0.3	1	1 17
127	24	0.3	1	0 24
128	10	0.3	1	0 10
129	3	0.3	1	0 3
130	9	0.3	1	0 9
131	1	0.3	1	1
132	17	0.3	1	0 17
133	13	0.3	1	0 13
134	1	0.3	1	0 1
135	1	0.3	1	0 1
136	39	0.3	1	0 39
137	22	0.3	1	0 22
139	2	0.3	1	0 2
140	5	0.3	1	0 5
141	5	0.3	1	0 5
142	4	0.3	1	0 4
143	9	0.3	1	0 9
144	1	0.3	1	0 1
145	12	0.3	1	0 12
146	1	0.3	1	0 1
147	16	0.3	1	0 16
148	7	0.3	1	0 7
149	1	0.3	1	0 1
150	2	0.3	1	0 2

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0016_good_2.fq.gz
=============================================
21355094 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0016_good_1_trimmed.fq.gz and Unknown_CK259-004T0016_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0016_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0016_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0016_good_1_trimmed.fq.gz and Unknown_CK259-004T0016_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0016_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0016_good_2_val_2.fq.gz

Total number of sequences analysed: 21355094

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 90673 (0.42%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 21380 (0.10%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0016_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0016_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0016_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0016_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0016_good_1_trimmed.fq.gz and Unknown_CK259-004T0016_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0017_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	4	AGATCGGAAGAGC	1000000	0.00
Nextera	4	CTGTCTCTTATA	1000000	0.00
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count Illumina: 4, count Nextera: 4, count smallRNA: 1)
Defaulting to Illumina universal adapter ( AGATCGGAAGAGC ). Specify -a SEQUENCE to avoid this behavior).

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0017_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0017_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              18,634,969
Reads with adapters:                     3,264 (0.0%)
Reads written (passing filters):    18,634,969 (100.0%)

Total basepairs processed: 2,778,138,720 bp
Quality-trimmed:               5,146,676 bp (0.2%)
Total written (filtered):  2,772,850,334 bp (99.8%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3264 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 22.8%
  C: 20.7%
  G: 48.1%
  T: 7.8%
  none/other: 0.6%

Overview of removed sequences
length	count	expect	max.err	error counts
5	710	18198.2	0	710
6	75	4549.6	0	75
7	6	1137.4	0	6
8	3	284.3	0	3
9	357	71.1	0	0 357
10	212	17.8	1	2 210
11	85	4.4	1	1 84
12	53	1.1	1	1 52
13	18	0.3	1	0 18
14	11	0.3	1	0 11
15	16	0.3	1	1 15
16	9	0.3	1	0 9
17	8	0.3	1	0 8
18	16	0.3	1	0 16
19	11	0.3	1	1 10
20	21	0.3	1	1 20
21	12	0.3	1	2 10
22	20	0.3	1	1 19
23	14	0.3	1	0 14
24	14	0.3	1	2 12
25	6	0.3	1	0 6
26	9	0.3	1	0 9
27	22	0.3	1	3 19
28	19	0.3	1	1 18
29	18	0.3	1	0 18
30	33	0.3	1	0 33
31	10	0.3	1	0 10
32	23	0.3	1	0 23
33	14	0.3	1	5 9
34	7	0.3	1	2 5
35	13	0.3	1	2 11
36	12	0.3	1	2 10
37	18	0.3	1	3 15
38	6	0.3	1	2 4
39	17	0.3	1	3 14
40	13	0.3	1	3 10
41	29	0.3	1	4 25
42	17	0.3	1	0 17
43	17	0.3	1	4 13
44	17	0.3	1	4 13
45	17	0.3	1	5 12
46	18	0.3	1	4 14
47	40	0.3	1	3 37
48	10	0.3	1	2 8
49	19	0.3	1	0 19
50	12	0.3	1	2 10
51	16	0.3	1	2 14
52	22	0.3	1	3 19
53	35	0.3	1	3 32
54	12	0.3	1	1 11
55	20	0.3	1	3 17
56	25	0.3	1	3 22
57	27	0.3	1	2 25
58	14	0.3	1	1 13
59	12	0.3	1	0 12
60	6	0.3	1	0 6
61	19	0.3	1	0 19
62	14	0.3	1	0 14
63	15	0.3	1	0 15
64	12	0.3	1	0 12
65	7	0.3	1	1 6
66	10	0.3	1	0 10
67	8	0.3	1	1 7
68	4	0.3	1	0 4
69	5	0.3	1	0 5
70	19	0.3	1	1 18
71	9	0.3	1	0 9
72	17	0.3	1	0 17
73	23	0.3	1	0 23
74	9	0.3	1	1 8
75	8	0.3	1	0 8
76	15	0.3	1	0 15
77	7	0.3	1	0 7
78	11	0.3	1	0 11
79	19	0.3	1	0 19
80	12	0.3	1	0 12
81	18	0.3	1	0 18
82	10	0.3	1	0 10
83	16	0.3	1	0 16
84	19	0.3	1	0 19
85	22	0.3	1	0 22
86	9	0.3	1	0 9
87	8	0.3	1	0 8
88	12	0.3	1	1 11
89	5	0.3	1	1 4
90	5	0.3	1	0 5
91	15	0.3	1	0 15
92	9	0.3	1	0 9
93	9	0.3	1	0 9
94	3	0.3	1	0 3
95	8	0.3	1	0 8
96	10	0.3	1	0 10
97	32	0.3	1	1 31
98	5	0.3	1	0 5
99	6	0.3	1	1 5
100	2	0.3	1	0 2
101	7	0.3	1	0 7
102	13	0.3	1	0 13
103	16	0.3	1	0 16
104	8	0.3	1	1 7
105	9	0.3	1	0 9
106	4	0.3	1	0 4
107	4	0.3	1	0 4
108	3	0.3	1	0 3
109	9	0.3	1	0 9
110	11	0.3	1	0 11
111	12	0.3	1	0 12
112	12	0.3	1	0 12
113	6	0.3	1	0 6
114	14	0.3	1	0 14
115	9	0.3	1	0 9
116	11	0.3	1	0 11
117	14	0.3	1	0 14
118	13	0.3	1	0 13
119	19	0.3	1	0 19
120	1	0.3	1	0 1
121	11	0.3	1	0 11
122	16	0.3	1	0 16
123	7	0.3	1	0 7
124	8	0.3	1	0 8
125	11	0.3	1	0 11
126	7	0.3	1	2 5
127	7	0.3	1	0 7
128	10	0.3	1	0 10
129	4	0.3	1	0 4
130	14	0.3	1	0 14
131	6	0.3	1	0 6
132	6	0.3	1	0 6
133	3	0.3	1	0 3
134	12	0.3	1	0 12
135	7	0.3	1	0 7
136	13	0.3	1	0 13
137	14	0.3	1	0 14
138	26	0.3	1	0 26
139	4	0.3	1	0 4
140	6	0.3	1	0 6
141	16	0.3	1	0 16
142	3	0.3	1	0 3
143	9	0.3	1	0 9
144	4	0.3	1	0 4
145	8	0.3	1	0 8
146	15	0.3	1	0 15
147	25	0.3	1	0 25
148	11	0.3	1	0 11
149	7	0.3	1	1 6
150	17	0.3	1	0 17

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_1.fq.gz
=============================================
18634969 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0017_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0017_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              18,634,969
Reads with adapters:                     3,116 (0.0%)
Reads written (passing filters):    18,634,969 (100.0%)

Total basepairs processed: 2,778,250,523 bp
Quality-trimmed:              11,791,318 bp (0.4%)
Total written (filtered):  2,766,325,655 bp (99.6%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3116 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 24.4%
  C: 21.1%
  G: 45.0%
  T: 9.0%
  none/other: 0.5%

Overview of removed sequences
length	count	expect	max.err	error counts
5	693	18198.2	0	693
6	92	4549.6	0	92
7	6	1137.4	0	6
8	4	284.3	0	4
9	381	71.1	0	9 372
10	159	17.8	1	2 157
11	72	4.4	1	1 71
12	39	1.1	1	5 34
13	13	0.3	1	0 13
14	7	0.3	1	2 5
15	17	0.3	1	2 15
16	9	0.3	1	1 8
17	19	0.3	1	4 15
18	7	0.3	1	0 7
19	13	0.3	1	6 7
20	17	0.3	1	2 15
21	16	0.3	1	1 15
22	15	0.3	1	6 9
23	17	0.3	1	3 14
24	21	0.3	1	5 16
25	10	0.3	1	0 10
26	10	0.3	1	1 9
27	23	0.3	1	4 19
28	9	0.3	1	1 8
29	15	0.3	1	3 12
30	29	0.3	1	7 22
31	14	0.3	1	1 13
32	18	0.3	1	1 17
33	10	0.3	1	3 7
34	6	0.3	1	2 4
35	9	0.3	1	0 9
36	23	0.3	1	3 20
37	15	0.3	1	0 15
38	7	0.3	1	2 5
39	11	0.3	1	0 11
40	20	0.3	1	4 16
41	21	0.3	1	3 18
42	14	0.3	1	5 9
43	13	0.3	1	0 13
44	18	0.3	1	3 15
45	14	0.3	1	3 11
46	9	0.3	1	2 7
47	17	0.3	1	1 16
48	23	0.3	1	1 22
49	19	0.3	1	0 19
50	7	0.3	1	4 3
51	9	0.3	1	1 8
52	27	0.3	1	1 26
53	23	0.3	1	1 22
54	9	0.3	1	1 8
55	17	0.3	1	1 16
56	12	0.3	1	2 10
57	21	0.3	1	0 21
58	11	0.3	1	0 11
59	22	0.3	1	0 22
60	4	0.3	1	1 3
61	21	0.3	1	1 20
62	14	0.3	1	0 14
63	19	0.3	1	0 19
64	16	0.3	1	0 16
65	8	0.3	1	2 6
66	23	0.3	1	0 23
67	12	0.3	1	2 10
68	8	0.3	1	1 7
69	17	0.3	1	0 17
70	11	0.3	1	0 11
71	10	0.3	1	0 10
72	14	0.3	1	0 14
73	9	0.3	1	0 9
74	7	0.3	1	1 6
75	10	0.3	1	3 7
76	16	0.3	1	0 16
77	18	0.3	1	1 17
78	11	0.3	1	1 10
79	14	0.3	1	0 14
80	11	0.3	1	0 11
81	14	0.3	1	0 14
82	7	0.3	1	0 7
83	12	0.3	1	0 12
84	8	0.3	1	0 8
85	32	0.3	1	1 31
86	22	0.3	1	0 22
87	10	0.3	1	0 10
88	2	0.3	1	1 1
89	5	0.3	1	1 4
90	11	0.3	1	0 11
91	21	0.3	1	0 21
92	19	0.3	1	0 19
93	9	0.3	1	0 9
94	11	0.3	1	1 10
95	17	0.3	1	0 17
96	17	0.3	1	0 17
97	18	0.3	1	1 17
98	4	0.3	1	0 4
99	5	0.3	1	1 4
100	2	0.3	1	0 2
101	3	0.3	1	0 3
102	10	0.3	1	0 10
103	3	0.3	1	0 3
104	18	0.3	1	1 17
105	10	0.3	1	0 10
106	8	0.3	1	0 8
107	11	0.3	1	0 11
108	8	0.3	1	0 8
109	5	0.3	1	0 5
110	7	0.3	1	0 7
111	7	0.3	1	0 7
112	1	0.3	1	0 1
113	5	0.3	1	1 4
114	17	0.3	1	0 17
115	15	0.3	1	0 15
116	3	0.3	1	1 2
117	3	0.3	1	0 3
118	17	0.3	1	0 17
119	9	0.3	1	0 9
120	7	0.3	1	0 7
121	1	0.3	1	0 1
122	9	0.3	1	0 9
123	18	0.3	1	0 18
124	12	0.3	1	0 12
125	11	0.3	1	0 11
126	1	0.3	1	0 1
127	14	0.3	1	0 14
128	11	0.3	1	0 11
129	6	0.3	1	0 6
130	15	0.3	1	0 15
131	13	0.3	1	0 13
132	15	0.3	1	1 14
133	7	0.3	1	0 7
134	9	0.3	1	1 8
135	8	0.3	1	0 8
136	5	0.3	1	0 5
137	15	0.3	1	0 15
138	14	0.3	1	0 14
139	4	0.3	1	0 4
140	5	0.3	1	0 5
141	13	0.3	1	0 13
142	6	0.3	1	0 6
143	2	0.3	1	0 2
144	6	0.3	1	0 6
145	4	0.3	1	0 4
146	1	0.3	1	0 1
147	24	0.3	1	0 24
148	17	0.3	1	0 17
149	2	0.3	1	0 2
150	10	0.3	1	0 10

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0017_good_2.fq.gz
=============================================
18634969 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0017_good_1_trimmed.fq.gz and Unknown_CK259-004T0017_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0017_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0017_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0017_good_1_trimmed.fq.gz and Unknown_CK259-004T0017_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0017_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0017_good_2_val_2.fq.gz

Total number of sequences analysed: 18634969

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 83180 (0.45%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 22705 (0.12%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0017_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0017_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0017_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0017_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0017_good_1_trimmed.fq.gz and Unknown_CK259-004T0017_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0018_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Nextera	1	CTGTCTCTTATA	1000000	0.00
Illumina	1	AGATCGGAAGAGC	1000000	0.00
Using smallRNA adapter for trimming (count: 2). Second best hit was Nextera (count: 1)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0018_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0018_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a TGGAATTCTCGG /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              21,575,590
Reads with adapters:                    67,985 (0.3%)
Reads written (passing filters):    21,575,590 (100.0%)

Total basepairs processed: 3,210,381,880 bp
Quality-trimmed:               3,430,122 bp (0.1%)
Total written (filtered):  3,205,846,902 bp (99.9%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 67985 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 24.4%
  C: 21.5%
  G: 27.2%
  T: 26.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	40668	21069.9	0	40668
6	10511	5267.5	0	10511
7	3460	1316.9	0	3460
8	672	329.2	0	672
9	1493	82.3	0	169 1324
10	1345	20.6	1	56 1289
11	321	5.1	1	0 321
12	78	1.3	1	0 78
13	49	1.3	1	0 49
14	49	1.3	1	0 49
15	37	1.3	1	0 37
16	79	1.3	1	0 79
17	77	1.3	1	0 77
18	58	1.3	1	0 58
19	66	1.3	1	0 66
20	75	1.3	1	0 75
21	40	1.3	1	0 40
22	57	1.3	1	0 57
23	79	1.3	1	0 79
24	63	1.3	1	1 62
25	68	1.3	1	0 68
26	34	1.3	1	0 34
27	54	1.3	1	0 54
28	38	1.3	1	0 38
29	44	1.3	1	0 44
30	47	1.3	1	0 47
31	50	1.3	1	0 50
32	60	1.3	1	1 59
33	68	1.3	1	1 67
34	46	1.3	1	0 46
35	60	1.3	1	0 60
36	69	1.3	1	0 69
37	82	1.3	1	0 82
38	48	1.3	1	0 48
39	50	1.3	1	1 49
40	95	1.3	1	1 94
41	80	1.3	1	0 80
42	69	1.3	1	1 68
43	56	1.3	1	0 56
44	58	1.3	1	0 58
45	69	1.3	1	0 69
46	58	1.3	1	0 58
47	82	1.3	1	0 82
48	51	1.3	1	1 50
49	104	1.3	1	0 104
50	71	1.3	1	0 71
51	92	1.3	1	0 92
52	68	1.3	1	0 68
53	75	1.3	1	1 74
54	65	1.3	1	0 65
55	71	1.3	1	0 71
56	62	1.3	1	0 62
57	55	1.3	1	0 55
58	128	1.3	1	0 128
59	58	1.3	1	0 58
60	70	1.3	1	0 70
61	67	1.3	1	0 67
62	80	1.3	1	0 80
63	71	1.3	1	0 71
64	82	1.3	1	0 82
65	92	1.3	1	0 92
66	59	1.3	1	3 56
67	69	1.3	1	0 69
68	50	1.3	1	0 50
69	57	1.3	1	4 53
70	63	1.3	1	0 63
71	50	1.3	1	0 50
72	92	1.3	1	0 92
73	54	1.3	1	0 54
74	72	1.3	1	0 72
75	97	1.3	1	0 97
76	91	1.3	1	0 91
77	110	1.3	1	0 110
78	63	1.3	1	0 63
79	101	1.3	1	2 99
80	71	1.3	1	0 71
81	62	1.3	1	0 62
82	106	1.3	1	0 106
83	108	1.3	1	0 108
84	69	1.3	1	0 69
85	73	1.3	1	0 73
86	53	1.3	1	0 53
87	54	1.3	1	3 51
88	79	1.3	1	0 79
89	59	1.3	1	0 59
90	58	1.3	1	0 58
91	75	1.3	1	0 75
92	103	1.3	1	0 103
93	78	1.3	1	0 78
94	50	1.3	1	1 49
95	73	1.3	1	0 73
96	79	1.3	1	5 74
97	98	1.3	1	0 98
98	67	1.3	1	1 66
99	74	1.3	1	0 74
100	108	1.3	1	0 108
101	79	1.3	1	0 79
102	131	1.3	1	0 131
103	90	1.3	1	0 90
104	82	1.3	1	0 82
105	39	1.3	1	0 39
106	83	1.3	1	0 83
107	60	1.3	1	2 58
108	80	1.3	1	0 80
109	75	1.3	1	0 75
110	63	1.3	1	1 62
111	49	1.3	1	0 49
112	79	1.3	1	1 78
113	50	1.3	1	0 50
114	54	1.3	1	2 52
115	83	1.3	1	0 83
116	52	1.3	1	0 52
117	89	1.3	1	0 89
118	66	1.3	1	0 66
119	66	1.3	1	3 63
120	45	1.3	1	0 45
121	58	1.3	1	0 58
122	45	1.3	1	0 45
123	79	1.3	1	0 79
124	68	1.3	1	0 68
125	73	1.3	1	0 73
126	53	1.3	1	1 52
127	62	1.3	1	2 60
128	73	1.3	1	2 71
129	63	1.3	1	0 63
130	85	1.3	1	0 85
131	69	1.3	1	0 69
132	63	1.3	1	0 63
133	51	1.3	1	0 51
134	57	1.3	1	0 57
135	52	1.3	1	0 52
136	65	1.3	1	0 65
137	49	1.3	1	0 49
138	68	1.3	1	0 68
139	54	1.3	1	0 54
140	51	1.3	1	0 51
141	68	1.3	1	0 68
142	58	1.3	1	0 58
143	36	1.3	1	0 36
144	107	1.3	1	0 107
145	19	1.3	1	0 19
146	20	1.3	1	0 20
147	40	1.3	1	0 40
148	71	1.3	1	0 71
149	179	1.3	1	3 176
150	85	1.3	1	1 84

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_1.fq.gz
=============================================
21575590 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0018_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0018_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a GATCGTCGGACT /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              21,575,590
Reads with adapters:                    18,317 (0.1%)
Reads written (passing filters):    21,575,590 (100.0%)

Total basepairs processed: 3,210,197,234 bp
Quality-trimmed:               7,312,969 bp (0.2%)
Total written (filtered):  3,202,494,025 bp (99.8%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 18317 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 14.9%
  C: 21.4%
  G: 21.8%
  T: 41.8%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	9380	21069.9	0	9380
6	3634	5267.5	0	3634
7	602	1316.9	0	602
8	257	329.2	0	257
9	193	82.3	0	29 164
10	362	20.6	1	5 357
11	139	5.1	1	2 137
12	48	1.3	1	11 37
13	31	1.3	1	2 29
14	24	1.3	1	3 21
15	21	1.3	1	4 17
16	44	1.3	1	7 37
17	27	1.3	1	4 23
18	13	1.3	1	1 12
19	13	1.3	1	1 12
20	20	1.3	1	3 17
21	25	1.3	1	3 22
22	25	1.3	1	1 24
23	28	1.3	1	0 28
24	41	1.3	1	1 40
25	20	1.3	1	0 20
26	35	1.3	1	1 34
27	43	1.3	1	2 41
28	22	1.3	1	0 22
29	20	1.3	1	9 11
30	15	1.3	1	1 14
31	16	1.3	1	0 16
32	30	1.3	1	1 29
33	33	1.3	1	3 30
34	13	1.3	1	0 13
35	22	1.3	1	0 22
36	31	1.3	1	3 28
37	22	1.3	1	0 22
38	29	1.3	1	1 28
39	51	1.3	1	1 50
40	21	1.3	1	0 21
41	29	1.3	1	0 29
42	29	1.3	1	0 29
43	28	1.3	1	1 27
44	29	1.3	1	0 29
45	27	1.3	1	2 25
46	37	1.3	1	0 37
47	30	1.3	1	5 25
48	38	1.3	1	3 35
49	24	1.3	1	0 24
50	19	1.3	1	0 19
51	21	1.3	1	0 21
52	36	1.3	1	0 36
53	24	1.3	1	1 23
54	15	1.3	1	0 15
55	34	1.3	1	2 32
56	26	1.3	1	1 25
57	29	1.3	1	2 27
58	39	1.3	1	1 38
59	19	1.3	1	2 17
60	13	1.3	1	1 12
61	24	1.3	1	1 23
62	21	1.3	1	0 21
63	23	1.3	1	3 20
64	19	1.3	1	3 16
65	28	1.3	1	0 28
66	24	1.3	1	2 22
67	29	1.3	1	1 28
68	30	1.3	1	7 23
69	26	1.3	1	8 18
70	24	1.3	1	0 24
71	32	1.3	1	2 30
72	13	1.3	1	0 13
73	13	1.3	1	0 13
74	32	1.3	1	0 32
75	12	1.3	1	0 12
76	18	1.3	1	0 18
77	32	1.3	1	0 32
78	27	1.3	1	0 27
79	22	1.3	1	3 19
80	16	1.3	1	2 14
81	25	1.3	1	1 24
82	15	1.3	1	2 13
83	32	1.3	1	1 31
84	36	1.3	1	1 35
85	28	1.3	1	0 28
86	23	1.3	1	3 20
87	24	1.3	1	0 24
88	27	1.3	1	0 27
89	23	1.3	1	3 20
90	29	1.3	1	0 29
91	10	1.3	1	0 10
92	20	1.3	1	0 20
93	39	1.3	1	0 39
94	21	1.3	1	0 21
95	10	1.3	1	0 10
96	24	1.3	1	2 22
97	13	1.3	1	0 13
98	21	1.3	1	0 21
99	29	1.3	1	2 27
100	27	1.3	1	5 22
101	42	1.3	1	5 37
102	39	1.3	1	15 24
103	18	1.3	1	0 18
104	30	1.3	1	5 25
105	11	1.3	1	0 11
106	25	1.3	1	0 25
107	37	1.3	1	12 25
108	23	1.3	1	4 19
109	23	1.3	1	1 22
110	18	1.3	1	0 18
111	22	1.3	1	0 22
112	25	1.3	1	1 24
113	30	1.3	1	1 29
114	34	1.3	1	1 33
115	26	1.3	1	1 25
116	28	1.3	1	1 27
117	23	1.3	1	1 22
118	22	1.3	1	2 20
119	24	1.3	1	5 19
120	38	1.3	1	2 36
121	15	1.3	1	2 13
122	40	1.3	1	6 34
123	26	1.3	1	5 21
124	26	1.3	1	9 17
125	28	1.3	1	7 21
126	69	1.3	1	15 54
127	38	1.3	1	6 32
128	54	1.3	1	7 47
129	56	1.3	1	9 47
130	41	1.3	1	7 34
131	34	1.3	1	7 27
132	41	1.3	1	19 22
133	43	1.3	1	6 37
134	48	1.3	1	10 38
135	44	1.3	1	7 37
136	33	1.3	1	6 27
137	22	1.3	1	3 19
138	36	1.3	1	8 28
139	30	1.3	1	2 28
140	8	1.3	1	0 8
141	15	1.3	1	1 14
142	27	1.3	1	0 27
143	14	1.3	1	0 14
144	21	1.3	1	0 21
145	10	1.3	1	0 10
146	56	1.3	1	0 56
147	18	1.3	1	0 18
148	11	1.3	1	0 11
149	18	1.3	1	0 18
150	20	1.3	1	3 17

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0018_good_2.fq.gz
=============================================
21575590 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0018_good_1_trimmed.fq.gz and Unknown_CK259-004T0018_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0018_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0018_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0018_good_1_trimmed.fq.gz and Unknown_CK259-004T0018_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0018_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0018_good_2_val_2.fq.gz

Total number of sequences analysed: 21575590

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 54987 (0.25%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 17930 (0.08%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0018_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0018_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0018_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0018_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0018_good_1_trimmed.fq.gz and Unknown_CK259-004T0018_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0019_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Nextera	6	CTGTCTCTTATA	1000000	0.00
smallRNA	5	TGGAATTCTCGG	1000000	0.00
Illumina	5	AGATCGGAAGAGC	1000000	0.00
Using Nextera adapter for trimming (count: 6). Second best hit was smallRNA (count: 5)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0019_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera Transposase sequence; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0019_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,931,761
Reads with adapters:                    38,307 (0.2%)
Reads written (passing filters):    19,931,761 (100.0%)

Total basepairs processed: 2,918,889,025 bp
Quality-trimmed:               4,383,868 bp (0.2%)
Total written (filtered):  2,913,745,961 bp (99.8%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 38307 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 20.9%
  C: 19.0%
  G: 24.7%
  T: 35.3%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	20592	19464.6	0	20592
6	6238	4866.2	0	6238
7	2124	1216.5	0	2124
8	724	304.1	0	724
9	472	76.0	0	95 377
10	519	19.0	1	1 518
11	296	4.8	1	3 293
12	94	1.2	1	0 94
13	46	1.2	1	0 46
14	81	1.2	1	1 80
15	55	1.2	1	0 55
16	32	1.2	1	0 32
17	84	1.2	1	0 84
18	30	1.2	1	0 30
19	47	1.2	1	0 47
20	29	1.2	1	0 29
21	53	1.2	1	0 53
22	93	1.2	1	0 93
23	36	1.2	1	0 36
24	71	1.2	1	0 71
25	62	1.2	1	0 62
26	62	1.2	1	0 62
27	42	1.2	1	0 42
28	84	1.2	1	0 84
29	54	1.2	1	0 54
30	57	1.2	1	0 57
31	97	1.2	1	0 97
32	57	1.2	1	0 57
33	98	1.2	1	0 98
34	36	1.2	1	0 36
35	104	1.2	1	0 104
36	54	1.2	1	1 53
37	26	1.2	1	0 26
38	26	1.2	1	0 26
39	24	1.2	1	0 24
40	78	1.2	1	0 78
41	43	1.2	1	0 43
42	50	1.2	1	1 49
43	71	1.2	1	4 67
44	39	1.2	1	0 39
45	41	1.2	1	0 41
46	90	1.2	1	0 90
47	60	1.2	1	0 60
48	69	1.2	1	0 69
49	61	1.2	1	0 61
50	59	1.2	1	0 59
51	45	1.2	1	0 45
52	57	1.2	1	0 57
53	48	1.2	1	0 48
54	45	1.2	1	0 45
55	43	1.2	1	0 43
56	43	1.2	1	0 43
57	25	1.2	1	0 25
58	26	1.2	1	0 26
59	29	1.2	1	0 29
60	36	1.2	1	0 36
61	24	1.2	1	0 24
62	40	1.2	1	0 40
63	90	1.2	1	0 90
64	106	1.2	1	0 106
65	66	1.2	1	0 66
66	35	1.2	1	0 35
67	48	1.2	1	0 48
68	50	1.2	1	0 50
69	24	1.2	1	0 24
70	42	1.2	1	0 42
71	41	1.2	1	0 41
72	54	1.2	1	0 54
73	35	1.2	1	0 35
74	53	1.2	1	0 53
75	42	1.2	1	0 42
76	25	1.2	1	0 25
77	41	1.2	1	0 41
78	50	1.2	1	0 50
79	64	1.2	1	0 64
80	60	1.2	1	0 60
81	59	1.2	1	0 59
82	36	1.2	1	0 36
83	32	1.2	1	1 31
84	31	1.2	1	0 31
85	29	1.2	1	0 29
86	102	1.2	1	0 102
87	120	1.2	1	4 116
88	41	1.2	1	0 41
89	56	1.2	1	0 56
90	53	1.2	1	0 53
91	59	1.2	1	0 59
92	31	1.2	1	0 31
93	18	1.2	1	0 18
94	67	1.2	1	0 67
95	28	1.2	1	0 28
96	37	1.2	1	0 37
97	40	1.2	1	0 40
98	66	1.2	1	0 66
99	72	1.2	1	0 72
100	47	1.2	1	1 46
101	81	1.2	1	3 78
102	46	1.2	1	0 46
103	44	1.2	1	0 44
104	65	1.2	1	0 65
105	59	1.2	1	0 59
106	46	1.2	1	0 46
107	65	1.2	1	0 65
108	48	1.2	1	0 48
109	35	1.2	1	0 35
110	29	1.2	1	0 29
111	30	1.2	1	0 30
112	61	1.2	1	0 61
113	34	1.2	1	0 34
114	49	1.2	1	0 49
115	41	1.2	1	0 41
116	71	1.2	1	0 71
117	45	1.2	1	0 45
118	65	1.2	1	0 65
119	28	1.2	1	0 28
120	33	1.2	1	2 31
121	58	1.2	1	0 58
122	55	1.2	1	0 55
123	81	1.2	1	0 81
124	67	1.2	1	0 67
125	57	1.2	1	0 57
126	95	1.2	1	0 95
127	69	1.2	1	21 48
128	34	1.2	1	14 20
129	49	1.2	1	12 37
130	24	1.2	1	2 22
131	62	1.2	1	0 62
132	58	1.2	1	4 54
133	54	1.2	1	0 54
134	19	1.2	1	0 19
135	57	1.2	1	0 57
136	55	1.2	1	0 55
137	111	1.2	1	0 111
138	43	1.2	1	0 43
139	82	1.2	1	4 78
140	65	1.2	1	0 65
141	52	1.2	1	0 52
142	53	1.2	1	0 53
143	35	1.2	1	0 35
144	65	1.2	1	0 65
145	80	1.2	1	4 76
146	25	1.2	1	17 8
147	38	1.2	1	1 37
148	32	1.2	1	1 31
149	49	1.2	1	7 42
150	37	1.2	1	0 37

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_1.fq.gz
=============================================
19931761 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0019_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera Transposase sequence; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0019_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,931,761
Reads with adapters:                    37,423 (0.2%)
Reads written (passing filters):    19,931,761 (100.0%)

Total basepairs processed: 2,919,387,949 bp
Quality-trimmed:               9,700,136 bp (0.3%)
Total written (filtered):  2,908,972,045 bp (99.6%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 37423 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 20.9%
  C: 18.9%
  G: 24.4%
  T: 35.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	20178	19464.6	0	20178
6	5966	4866.2	0	5966
7	2233	1216.5	0	2233
8	848	304.1	0	848
9	442	76.0	0	82 360
10	537	19.0	1	6 531
11	306	4.8	1	1 305
12	72	1.2	1	0 72
13	46	1.2	1	0 46
14	62	1.2	1	0 62
15	57	1.2	1	0 57
16	65	1.2	1	0 65
17	56	1.2	1	0 56
18	47	1.2	1	0 47
19	49	1.2	1	0 49
20	29	1.2	1	0 29
21	40	1.2	1	0 40
22	113	1.2	1	0 113
23	50	1.2	1	0 50
24	88	1.2	1	0 88
25	56	1.2	1	0 56
26	26	1.2	1	0 26
27	38	1.2	1	0 38
28	105	1.2	1	0 105
29	43	1.2	1	0 43
30	65	1.2	1	0 65
31	64	1.2	1	0 64
32	72	1.2	1	0 72
33	75	1.2	1	0 75
34	46	1.2	1	0 46
35	56	1.2	1	0 56
36	63	1.2	1	0 63
37	61	1.2	1	0 61
38	31	1.2	1	0 31
39	12	1.2	1	0 12
40	54	1.2	1	0 54
41	39	1.2	1	0 39
42	64	1.2	1	0 64
43	108	1.2	1	1 107
44	52	1.2	1	0 52
45	55	1.2	1	0 55
46	74	1.2	1	0 74
47	39	1.2	1	0 39
48	49	1.2	1	0 49
49	48	1.2	1	0 48
50	86	1.2	1	0 86
51	45	1.2	1	0 45
52	62	1.2	1	0 62
53	55	1.2	1	0 55
54	25	1.2	1	0 25
55	50	1.2	1	0 50
56	31	1.2	1	0 31
57	35	1.2	1	0 35
58	33	1.2	1	0 33
59	30	1.2	1	0 30
60	45	1.2	1	0 45
61	23	1.2	1	0 23
62	41	1.2	1	0 41
63	48	1.2	1	0 48
64	96	1.2	1	0 96
65	55	1.2	1	0 55
66	49	1.2	1	0 49
67	78	1.2	1	0 78
68	57	1.2	1	0 57
69	46	1.2	1	0 46
70	49	1.2	1	0 49
71	25	1.2	1	0 25
72	55	1.2	1	0 55
73	27	1.2	1	1 26
74	58	1.2	1	0 58
75	35	1.2	1	0 35
76	33	1.2	1	0 33
77	36	1.2	1	0 36
78	39	1.2	1	1 38
79	60	1.2	1	0 60
80	62	1.2	1	1 61
81	44	1.2	1	1 43
82	31	1.2	1	0 31
83	30	1.2	1	1 29
84	20	1.2	1	0 20
85	47	1.2	1	0 47
86	80	1.2	1	1 79
87	66	1.2	1	0 66
88	38	1.2	1	0 38
89	54	1.2	1	0 54
90	26	1.2	1	2 24
91	30	1.2	1	0 30
92	41	1.2	1	0 41
93	34	1.2	1	0 34
94	41	1.2	1	0 41
95	35	1.2	1	0 35
96	55	1.2	1	0 55
97	35	1.2	1	0 35
98	39	1.2	1	0 39
99	43	1.2	1	0 43
100	53	1.2	1	2 51
101	59	1.2	1	0 59
102	21	1.2	1	0 21
103	42	1.2	1	0 42
104	53	1.2	1	0 53
105	71	1.2	1	0 71
106	34	1.2	1	0 34
107	60	1.2	1	0 60
108	38	1.2	1	0 38
109	36	1.2	1	0 36
110	23	1.2	1	0 23
111	20	1.2	1	0 20
112	59	1.2	1	0 59
113	47	1.2	1	0 47
114	52	1.2	1	0 52
115	33	1.2	1	0 33
116	62	1.2	1	0 62
117	100	1.2	1	2 98
118	65	1.2	1	0 65
119	23	1.2	1	0 23
120	24	1.2	1	0 24
121	22	1.2	1	0 22
122	54	1.2	1	3 51
123	78	1.2	1	0 78
124	56	1.2	1	0 56
125	49	1.2	1	1 48
126	64	1.2	1	1 63
127	48	1.2	1	10 38
128	47	1.2	1	13 34
129	49	1.2	1	13 36
130	50	1.2	1	8 42
131	30	1.2	1	0 30
132	41	1.2	1	3 38
133	50	1.2	1	0 50
134	35	1.2	1	0 35
135	30	1.2	1	0 30
136	43	1.2	1	0 43
137	81	1.2	1	0 81
138	31	1.2	1	0 31
139	61	1.2	1	0 61
140	86	1.2	1	5 81
141	34	1.2	1	0 34
142	58	1.2	1	0 58
143	67	1.2	1	0 67
144	59	1.2	1	1 58
145	94	1.2	1	0 94
146	19	1.2	1	0 19
147	71	1.2	1	5 66
148	26	1.2	1	0 26
149	60	1.2	1	0 60
150	18	1.2	1	0 18

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0019_good_2.fq.gz
=============================================
19931761 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0019_good_1_trimmed.fq.gz and Unknown_CK259-004T0019_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0019_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0019_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0019_good_1_trimmed.fq.gz and Unknown_CK259-004T0019_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0019_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0019_good_2_val_2.fq.gz

Total number of sequences analysed: 19931761

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 62157 (0.31%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 15148 (0.08%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0019_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0019_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0019_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0019_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0019_good_1_trimmed.fq.gz and Unknown_CK259-004T0019_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0020_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	3	AGATCGGAAGAGC	1000000	0.00
Nextera	3	CTGTCTCTTATA	1000000	0.00
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count Illumina: 3, count Nextera: 3, count smallRNA: 1)
Defaulting to Illumina universal adapter ( AGATCGGAAGAGC ). Specify -a SEQUENCE to avoid this behavior).

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0020_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0020_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,746,854
Reads with adapters:                     5,137 (0.0%)
Reads written (passing filters):    22,746,854 (100.0%)

Total basepairs processed: 3,378,468,610 bp
Quality-trimmed:               8,917,145 bp (0.3%)
Total written (filtered):  3,369,261,623 bp (99.7%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 5137 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 38.1%
  C: 29.5%
  G: 22.9%
  T: 9.3%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	397	22213.7	0	397
6	93	5553.4	0	93
7	9	1388.4	0	9
8	6	347.1	0	6
9	469	86.8	0	5 464
10	160	21.7	1	1 159
11	128	5.4	1	1 127
12	50	1.4	1	2 48
13	29	0.3	1	0 29
14	25	0.3	1	0 25
15	45	0.3	1	2 43
16	30	0.3	1	1 29
17	39	0.3	1	1 38
18	38	0.3	1	0 38
19	54	0.3	1	3 51
20	37	0.3	1	3 34
21	41	0.3	1	5 36
22	30	0.3	1	2 28
23	49	0.3	1	1 48
24	52	0.3	1	1 51
25	50	0.3	1	0 50
26	16	0.3	1	1 15
27	36	0.3	1	8 28
28	22	0.3	1	4 18
29	45	0.3	1	3 42
30	43	0.3	1	1 42
31	48	0.3	1	7 41
32	54	0.3	1	8 46
33	57	0.3	1	3 54
34	41	0.3	1	3 38
35	21	0.3	1	2 19
36	43	0.3	1	6 37
37	39	0.3	1	2 37
38	60	0.3	1	1 59
39	32	0.3	1	4 28
40	37	0.3	1	2 35
41	38	0.3	1	3 35
42	35	0.3	1	3 32
43	43	0.3	1	7 36
44	25	0.3	1	3 22
45	49	0.3	1	12 37
46	38	0.3	1	6 32
47	43	0.3	1	10 33
48	22	0.3	1	6 16
49	51	0.3	1	3 48
50	18	0.3	1	4 14
51	44	0.3	1	1 43
52	44	0.3	1	0 44
53	57	0.3	1	4 53
54	39	0.3	1	3 36
55	39	0.3	1	2 37
56	27	0.3	1	1 26
57	28	0.3	1	0 28
58	16	0.3	1	1 15
59	22	0.3	1	2 20
60	17	0.3	1	0 17
61	20	0.3	1	3 17
62	16	0.3	1	1 15
63	17	0.3	1	1 16
64	26	0.3	1	0 26
65	13	0.3	1	2 11
66	34	0.3	1	2 32
67	30	0.3	1	2 28
68	16	0.3	1	1 15
69	23	0.3	1	2 21
70	40	0.3	1	10 30
71	26	0.3	1	6 20
72	16	0.3	1	3 13
73	29	0.3	1	2 27
74	17	0.3	1	1 16
75	22	0.3	1	2 20
76	20	0.3	1	5 15
77	32	0.3	1	2 30
78	15	0.3	1	0 15
79	22	0.3	1	0 22
80	20	0.3	1	0 20
81	15	0.3	1	0 15
82	14	0.3	1	0 14
83	13	0.3	1	0 13
84	27	0.3	1	7 20
85	39	0.3	1	2 37
86	26	0.3	1	0 26
87	16	0.3	1	0 16
88	17	0.3	1	2 15
89	9	0.3	1	1 8
90	29	0.3	1	1 28
91	9	0.3	1	1 8
92	13	0.3	1	0 13
93	11	0.3	1	0 11
94	19	0.3	1	0 19
95	23	0.3	1	1 22
96	26	0.3	1	0 26
97	11	0.3	1	0 11
98	25	0.3	1	0 25
99	9	0.3	1	0 9
100	11	0.3	1	0 11
101	27	0.3	1	0 27
102	37	0.3	1	1 36
103	25	0.3	1	5 20
104	23	0.3	1	4 19
105	22	0.3	1	1 21
106	13	0.3	1	0 13
107	11	0.3	1	0 11
108	11	0.3	1	1 10
109	32	0.3	1	0 32
110	16	0.3	1	0 16
111	25	0.3	1	1 24
112	25	0.3	1	0 25
113	18	0.3	1	3 15
114	35	0.3	1	1 34
115	42	0.3	1	1 41
116	39	0.3	1	1 38
117	27	0.3	1	1 26
118	27	0.3	1	6 21
119	14	0.3	1	1 13
120	10	0.3	1	0 10
121	16	0.3	1	0 16
122	35	0.3	1	0 35
123	14	0.3	1	1 13
124	25	0.3	1	1 24
125	27	0.3	1	0 27
126	21	0.3	1	0 21
127	22	0.3	1	0 22
128	43	0.3	1	2 41
129	32	0.3	1	1 31
130	24	0.3	1	3 21
131	30	0.3	1	1 29
132	15	0.3	1	0 15
133	28	0.3	1	0 28
134	53	0.3	1	0 53
135	13	0.3	1	0 13
136	23	0.3	1	0 23
137	13	0.3	1	1 12
138	13	0.3	1	1 12
139	20	0.3	1	1 19
140	34	0.3	1	1 33
141	51	0.3	1	3 48
142	7	0.3	1	2 5
143	35	0.3	1	0 35
144	12	0.3	1	0 12
145	27	0.3	1	0 27
146	7	0.3	1	0 7
147	59	0.3	1	1 58
148	7	0.3	1	0 7
149	5	0.3	1	0 5
150	11	0.3	1	0 11

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_1.fq.gz
=============================================
22746854 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0020_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0020_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,746,854
Reads with adapters:                     5,619 (0.0%)
Reads written (passing filters):    22,746,854 (100.0%)

Total basepairs processed: 3,378,703,245 bp
Quality-trimmed:              17,651,703 bp (0.5%)
Total written (filtered):  3,360,748,047 bp (99.5%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 5619 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 37.6%
  C: 28.0%
  G: 24.3%
  T: 9.9%
  none/other: 0.2%

Overview of removed sequences
length	count	expect	max.err	error counts
5	522	22213.7	0	522
6	125	5553.4	0	125
7	21	1388.4	0	21
8	15	347.1	0	15
9	487	86.8	0	18 469
10	157	21.7	1	6 151
11	138	5.4	1	14 124
12	80	1.4	1	13 67
13	27	0.3	1	6 21
14	31	0.3	1	4 27
15	36	0.3	1	6 30
16	48	0.3	1	9 39
17	45	0.3	1	7 38
18	51	0.3	1	9 42
19	84	0.3	1	19 65
20	43	0.3	1	8 35
21	41	0.3	1	4 37
22	33	0.3	1	5 28
23	59	0.3	1	11 48
24	63	0.3	1	10 53
25	41	0.3	1	2 39
26	39	0.3	1	2 37
27	54	0.3	1	19 35
28	36	0.3	1	9 27
29	38	0.3	1	7 31
30	50	0.3	1	10 40
31	50	0.3	1	9 41
32	54	0.3	1	5 49
33	69	0.3	1	5 64
34	54	0.3	1	9 45
35	40	0.3	1	5 35
36	56	0.3	1	4 52
37	52	0.3	1	13 39
38	68	0.3	1	8 60
39	47	0.3	1	9 38
40	48	0.3	1	6 42
41	56	0.3	1	10 46
42	39	0.3	1	3 36
43	36	0.3	1	6 30
44	29	0.3	1	6 23
45	33	0.3	1	5 28
46	34	0.3	1	5 29
47	33	0.3	1	11 22
48	22	0.3	1	5 17
49	28	0.3	1	5 23
50	32	0.3	1	5 27
51	15	0.3	1	2 13
52	27	0.3	1	0 27
53	34	0.3	1	8 26
54	27	0.3	1	8 19
55	30	0.3	1	2 28
56	17	0.3	1	2 15
57	25	0.3	1	3 22
58	20	0.3	1	4 16
59	19	0.3	1	4 15
60	15	0.3	1	0 15
61	15	0.3	1	1 14
62	19	0.3	1	1 18
63	29	0.3	1	4 25
64	29	0.3	1	2 27
65	18	0.3	1	0 18
66	19	0.3	1	1 18
67	33	0.3	1	4 29
68	20	0.3	1	3 17
69	25	0.3	1	6 19
70	30	0.3	1	8 22
71	17	0.3	1	2 15
72	31	0.3	1	8 23
73	39	0.3	1	9 30
74	18	0.3	1	4 14
75	18	0.3	1	1 17
76	35	0.3	1	5 30
77	29	0.3	1	3 26
78	27	0.3	1	2 25
79	22	0.3	1	2 20
80	24	0.3	1	1 23
81	24	0.3	1	2 22
82	24	0.3	1	3 21
83	26	0.3	1	3 23
84	22	0.3	1	2 20
85	29	0.3	1	4 25
86	14	0.3	1	1 13
87	29	0.3	1	4 25
88	30	0.3	1	3 27
89	13	0.3	1	4 9
90	26	0.3	1	3 23
91	24	0.3	1	0 24
92	17	0.3	1	0 17
93	6	0.3	1	0 6
94	29	0.3	1	0 29
95	25	0.3	1	1 24
96	14	0.3	1	1 13
97	11	0.3	1	0 11
98	25	0.3	1	2 23
99	9	0.3	1	1 8
100	15	0.3	1	1 14
101	17	0.3	1	0 17
102	22	0.3	1	0 22
103	22	0.3	1	0 22
104	24	0.3	1	3 21
105	26	0.3	1	2 24
106	22	0.3	1	1 21
107	20	0.3	1	1 19
108	7	0.3	1	2 5
109	16	0.3	1	2 14
110	20	0.3	1	1 19
111	29	0.3	1	1 28
112	34	0.3	1	2 32
113	29	0.3	1	5 24
114	27	0.3	1	0 27
115	37	0.3	1	1 36
116	44	0.3	1	4 40
117	42	0.3	1	0 42
118	34	0.3	1	1 33
119	24	0.3	1	2 22
120	16	0.3	1	1 15
121	19	0.3	1	3 16
122	34	0.3	1	1 33
123	19	0.3	1	1 18
124	43	0.3	1	3 40
125	29	0.3	1	3 26
126	30	0.3	1	2 28
127	16	0.3	1	0 16
128	38	0.3	1	1 37
129	32	0.3	1	0 32
130	26	0.3	1	0 26
131	22	0.3	1	4 18
132	15	0.3	1	0 15
133	22	0.3	1	0 22
134	31	0.3	1	0 31
135	20	0.3	1	0 20
136	31	0.3	1	2 29
137	17	0.3	1	0 17
138	18	0.3	1	1 17
139	21	0.3	1	3 18
140	31	0.3	1	0 31
141	54	0.3	1	10 44
142	11	0.3	1	5 6
143	26	0.3	1	1 25
144	14	0.3	1	0 14
145	19	0.3	1	0 19
146	3	0.3	1	0 3
147	58	0.3	1	3 55
148	9	0.3	1	1 8
149	10	0.3	1	0 10
150	7	0.3	1	0 7

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0020_good_2.fq.gz
=============================================
22746854 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0020_good_1_trimmed.fq.gz and Unknown_CK259-004T0020_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0020_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0020_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0020_good_1_trimmed.fq.gz and Unknown_CK259-004T0020_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0020_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0020_good_2_val_2.fq.gz

Total number of sequences analysed: 22746854

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 99713 (0.44%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 22061 (0.10%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0020_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0020_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0020_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0020_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0020_good_1_trimmed.fq.gz and Unknown_CK259-004T0020_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0021_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	6	AGATCGGAAGAGC	1000000	0.00
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using Illumina adapter for trimming (count: 6). Second best hit was smallRNA (count: 2)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0021_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0021_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,084,682
Reads with adapters:                     3,694 (0.0%)
Reads written (passing filters):    20,084,682 (100.0%)

Total basepairs processed: 2,989,070,786 bp
Quality-trimmed:               2,780,151 bp (0.1%)
Total written (filtered):  2,986,048,871 bp (99.9%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3694 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 40.1%
  C: 30.5%
  G: 20.8%
  T: 8.2%
  none/other: 0.4%

Overview of removed sequences
length	count	expect	max.err	error counts
5	152	19613.9	0	152
6	46	4903.5	0	46
7	2	1225.9	0	2
8	2	306.5	0	2
9	324	76.6	0	2 322
10	87	19.2	1	0 87
11	82	4.8	1	1 81
12	34	1.2	1	0 34
13	18	0.3	1	1 17
14	22	0.3	1	0 22
15	19	0.3	1	0 19
16	26	0.3	1	1 25
17	35	0.3	1	0 35
18	31	0.3	1	0 31
19	38	0.3	1	2 36
20	14	0.3	1	0 14
21	23	0.3	1	1 22
22	19	0.3	1	0 19
23	12	0.3	1	2 10
24	17	0.3	1	0 17
25	14	0.3	1	0 14
26	19	0.3	1	0 19
27	15	0.3	1	1 14
28	24	0.3	1	1 23
29	34	0.3	1	4 30
30	28	0.3	1	2 26
31	32	0.3	1	4 28
32	35	0.3	1	4 31
33	35	0.3	1	0 35
34	31	0.3	1	3 28
35	19	0.3	1	0 19
36	27	0.3	1	2 25
37	45	0.3	1	2 43
38	35	0.3	1	0 35
39	21	0.3	1	0 21
40	33	0.3	1	1 32
41	26	0.3	1	1 25
42	14	0.3	1	2 12
43	38	0.3	1	1 37
44	18	0.3	1	0 18
45	19	0.3	1	2 17
46	17	0.3	1	1 16
47	19	0.3	1	4 15
48	19	0.3	1	2 17
49	20	0.3	1	1 19
50	26	0.3	1	3 23
51	21	0.3	1	3 18
52	23	0.3	1	1 22
53	25	0.3	1	2 23
54	21	0.3	1	0 21
55	21	0.3	1	2 19
56	23	0.3	1	3 20
57	28	0.3	1	1 27
58	17	0.3	1	0 17
59	24	0.3	1	1 23
60	14	0.3	1	1 13
61	16	0.3	1	0 16
62	17	0.3	1	0 17
63	14	0.3	1	1 13
64	32	0.3	1	2 30
65	13	0.3	1	1 12
66	16	0.3	1	3 13
67	18	0.3	1	1 17
68	18	0.3	1	0 18
69	20	0.3	1	4 16
70	14	0.3	1	4 10
71	23	0.3	1	2 21
72	17	0.3	1	0 17
73	21	0.3	1	3 18
74	19	0.3	1	1 18
75	13	0.3	1	0 13
76	33	0.3	1	0 33
77	22	0.3	1	1 21
78	14	0.3	1	2 12
79	16	0.3	1	1 15
80	17	0.3	1	0 17
81	20	0.3	1	1 19
82	25	0.3	1	0 25
83	29	0.3	1	0 29
84	16	0.3	1	1 15
85	10	0.3	1	1 9
86	16	0.3	1	0 16
87	16	0.3	1	1 15
88	24	0.3	1	2 22
89	15	0.3	1	0 15
90	28	0.3	1	2 26
91	18	0.3	1	1 17
92	14	0.3	1	1 13
93	15	0.3	1	2 13
94	9	0.3	1	1 8
95	17	0.3	1	1 16
96	25	0.3	1	2 23
97	23	0.3	1	0 23
98	14	0.3	1	0 14
99	17	0.3	1	1 16
100	19	0.3	1	0 19
101	24	0.3	1	0 24
102	15	0.3	1	0 15
103	24	0.3	1	0 24
104	25	0.3	1	0 25
105	26	0.3	1	0 26
106	26	0.3	1	0 26
107	10	0.3	1	0 10
108	15	0.3	1	1 14
109	20	0.3	1	1 19
110	15	0.3	1	1 14
111	15	0.3	1	2 13
112	27	0.3	1	1 26
113	19	0.3	1	1 18
114	26	0.3	1	2 24
115	30	0.3	1	0 30
116	23	0.3	1	0 23
117	25	0.3	1	0 25
118	26	0.3	1	1 25
119	20	0.3	1	0 20
120	14	0.3	1	1 13
121	13	0.3	1	0 13
122	30	0.3	1	0 30
123	10	0.3	1	0 10
124	25	0.3	1	1 24
125	14	0.3	1	0 14
126	18	0.3	1	0 18
127	15	0.3	1	0 15
128	39	0.3	1	0 39
129	33	0.3	1	0 33
130	18	0.3	1	0 18
131	18	0.3	1	1 17
132	12	0.3	1	0 12
133	23	0.3	1	0 23
134	33	0.3	1	0 33
135	12	0.3	1	1 11
136	29	0.3	1	5 24
137	16	0.3	1	0 16
138	14	0.3	1	0 14
139	15	0.3	1	0 15
140	29	0.3	1	1 28
141	36	0.3	1	1 35
142	13	0.3	1	1 12
143	39	0.3	1	0 39
144	9	0.3	1	2 7
145	21	0.3	1	0 21
146	8	0.3	1	1 7
147	72	0.3	1	0 72
148	5	0.3	1	0 5
149	9	0.3	1	1 8
150	15	0.3	1	0 15

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_1.fq.gz
=============================================
20084682 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0021_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0021_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,084,682
Reads with adapters:                     3,979 (0.0%)
Reads written (passing filters):    20,084,682 (100.0%)

Total basepairs processed: 2,988,974,103 bp
Quality-trimmed:               5,313,867 bp (0.2%)
Total written (filtered):  2,983,409,072 bp (99.8%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3979 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 40.6%
  C: 28.9%
  G: 20.8%
  T: 9.2%
  none/other: 0.4%

Overview of removed sequences
length	count	expect	max.err	error counts
5	205	19613.9	0	205
6	99	4903.5	0	99
7	4	1225.9	0	4
8	5	306.5	0	5
9	348	76.6	0	8 340
10	130	19.2	1	3 127
11	117	4.8	1	1 116
12	43	1.2	1	2 41
13	16	0.3	1	1 15
14	16	0.3	1	1 15
15	15	0.3	1	2 13
16	23	0.3	1	3 20
17	29	0.3	1	1 28
18	26	0.3	1	4 22
19	37	0.3	1	8 29
20	24	0.3	1	2 22
21	14	0.3	1	0 14
22	27	0.3	1	2 25
23	24	0.3	1	2 22
24	28	0.3	1	3 25
25	25	0.3	1	0 25
26	31	0.3	1	4 27
27	23	0.3	1	3 20
28	20	0.3	1	2 18
29	18	0.3	1	1 17
30	18	0.3	1	1 17
31	22	0.3	1	3 19
32	51	0.3	1	5 46
33	51	0.3	1	2 49
34	34	0.3	1	2 32
35	23	0.3	1	3 20
36	31	0.3	1	2 29
37	24	0.3	1	1 23
38	42	0.3	1	5 37
39	26	0.3	1	3 23
40	35	0.3	1	0 35
41	22	0.3	1	1 21
42	15	0.3	1	4 11
43	30	0.3	1	0 30
44	14	0.3	1	0 14
45	30	0.3	1	2 28
46	19	0.3	1	1 18
47	15	0.3	1	7 8
48	23	0.3	1	1 22
49	15	0.3	1	0 15
50	16	0.3	1	0 16
51	23	0.3	1	0 23
52	24	0.3	1	0 24
53	25	0.3	1	1 24
54	24	0.3	1	0 24
55	12	0.3	1	2 10
56	20	0.3	1	0 20
57	21	0.3	1	0 21
58	15	0.3	1	2 13
59	9	0.3	1	0 9
60	18	0.3	1	2 16
61	7	0.3	1	0 7
62	15	0.3	1	0 15
63	18	0.3	1	0 18
64	16	0.3	1	0 16
65	21	0.3	1	1 20
66	21	0.3	1	2 19
67	28	0.3	1	0 28
68	15	0.3	1	1 14
69	12	0.3	1	1 11
70	27	0.3	1	10 17
71	20	0.3	1	3 17
72	19	0.3	1	3 16
73	17	0.3	1	1 16
74	12	0.3	1	3 9
75	21	0.3	1	0 21
76	27	0.3	1	2 25
77	22	0.3	1	4 18
78	21	0.3	1	3 18
79	9	0.3	1	0 9
80	28	0.3	1	0 28
81	18	0.3	1	0 18
82	20	0.3	1	1 19
83	31	0.3	1	2 29
84	13	0.3	1	4 9
85	16	0.3	1	1 15
86	14	0.3	1	1 13
87	29	0.3	1	3 26
88	22	0.3	1	2 20
89	19	0.3	1	1 18
90	35	0.3	1	1 34
91	22	0.3	1	3 19
92	13	0.3	1	3 10
93	19	0.3	1	1 18
94	11	0.3	1	0 11
95	9	0.3	1	1 8
96	29	0.3	1	0 29
97	18	0.3	1	0 18
98	18	0.3	1	0 18
99	14	0.3	1	2 12
100	15	0.3	1	0 15
101	32	0.3	1	1 31
102	32	0.3	1	0 32
103	23	0.3	1	1 22
104	21	0.3	1	0 21
105	15	0.3	1	0 15
106	23	0.3	1	2 21
107	16	0.3	1	0 16
108	13	0.3	1	3 10
109	16	0.3	1	1 15
110	17	0.3	1	0 17
111	18	0.3	1	0 18
112	18	0.3	1	0 18
113	12	0.3	1	2 10
114	23	0.3	1	1 22
115	27	0.3	1	0 27
116	22	0.3	1	0 22
117	24	0.3	1	0 24
118	21	0.3	1	0 21
119	32	0.3	1	1 31
120	11	0.3	1	3 8
121	14	0.3	1	1 13
122	18	0.3	1	1 17
123	21	0.3	1	2 19
124	39	0.3	1	1 38
125	28	0.3	1	2 26
126	19	0.3	1	0 19
127	26	0.3	1	0 26
128	33	0.3	1	0 33
129	38	0.3	1	1 37
130	38	0.3	1	1 37
131	14	0.3	1	1 13
132	6	0.3	1	0 6
133	23	0.3	1	0 23
134	34	0.3	1	0 34
135	12	0.3	1	1 11
136	22	0.3	1	2 20
137	24	0.3	1	0 24
138	17	0.3	1	0 17
139	20	0.3	1	1 19
140	41	0.3	1	0 41
141	39	0.3	1	6 33
142	25	0.3	1	3 22
143	28	0.3	1	1 27
144	9	0.3	1	0 9
145	18	0.3	1	2 16
146	8	0.3	1	0 8
147	66	0.3	1	1 65
148	6	0.3	1	0 6
149	9	0.3	1	0 9
150	13	0.3	1	0 13

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0021_good_2.fq.gz
=============================================
20084682 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0021_good_1_trimmed.fq.gz and Unknown_CK259-004T0021_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0021_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0021_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0021_good_1_trimmed.fq.gz and Unknown_CK259-004T0021_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0021_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0021_good_2_val_2.fq.gz

Total number of sequences analysed: 20084682

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 30884 (0.15%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 8430 (0.04%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0021_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0021_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0021_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0021_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0021_good_1_trimmed.fq.gz and Unknown_CK259-004T0021_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0022_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Nextera	2	CTGTCTCTTATA	1000000	0.00
smallRNA	2	TGGAATTCTCGG	1000000	0.00
Illumina	1	AGATCGGAAGAGC	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count Nextera: 2, count smallRNA: 2, count Illumina: 1)
Defaulting to Nextera adapter as next best option ( CTGTCTCTTATA ). Specify -a SEQUENCE to avoid this behavior).
Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0022_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0022_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_1.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,040,680
Reads with adapters:                    43,010 (0.2%)
Reads written (passing filters):    20,040,680 (100.0%)

Total basepairs processed: 2,988,775,437 bp
Quality-trimmed:               6,773,219 bp (0.2%)
Total written (filtered):  2,980,632,699 bp (99.7%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 43010 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 45.3%
  C: 17.6%
  G: 17.1%
  T: 19.8%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	17714	19571.0	0	17714
6	5616	4892.7	0	5616
7	1951	1223.2	0	1951
8	597	305.8	0	597
9	388	76.4	0	92 296
10	510	19.1	1	8 502
11	201	4.8	1	0 201
12	153	1.2	1	0 153
13	83	1.2	1	0 83
14	87	1.2	1	0 87
15	114	1.2	1	0 114
16	94	1.2	1	0 94
17	394	1.2	1	0 394
18	60	1.2	1	0 60
19	136	1.2	1	0 136
20	87	1.2	1	0 87
21	75	1.2	1	0 75
22	148	1.2	1	0 148
23	162	1.2	1	0 162
24	561	1.2	1	0 561
25	236	1.2	1	0 236
26	149	1.2	1	1 148
27	107	1.2	1	0 107
28	100	1.2	1	0 100
29	319	1.2	1	0 319
30	90	1.2	1	0 90
31	47	1.2	1	0 47
32	35	1.2	1	0 35
33	44	1.2	1	0 44
34	168	1.2	1	0 168
35	179	1.2	1	0 179
36	82	1.2	1	0 82
37	36	1.2	1	0 36
38	106	1.2	1	0 106
39	96	1.2	1	0 96
40	43	1.2	1	0 43
41	47	1.2	1	0 47
42	55	1.2	1	0 55
43	48	1.2	1	0 48
44	263	1.2	1	0 263
45	274	1.2	1	0 274
46	131	1.2	1	0 131
47	37	1.2	1	0 37
48	10	1.2	1	0 10
49	17	1.2	1	0 17
50	82	1.2	1	0 82
51	136	1.2	1	0 136
52	71	1.2	1	0 71
53	245	1.2	1	0 245
54	66	1.2	1	0 66
55	82	1.2	1	0 82
56	34	1.2	1	0 34
57	37	1.2	1	0 37
58	25	1.2	1	0 25
59	75	1.2	1	0 75
60	221	1.2	1	0 221
61	100	1.2	1	0 100
62	183	1.2	1	1 182
63	95	1.2	1	0 95
64	86	1.2	1	0 86
65	102	1.2	1	0 102
66	84	1.2	1	0 84
67	103	1.2	1	0 103
68	89	1.2	1	1 88
69	109	1.2	1	0 109
70	103	1.2	1	0 103
71	201	1.2	1	0 201
72	96	1.2	1	1 95
73	161	1.2	1	0 161
74	88	1.2	1	0 88
75	105	1.2	1	0 105
76	286	1.2	1	0 286
77	145	1.2	1	0 145
78	103	1.2	1	0 103
79	62	1.2	1	0 62
80	618	1.2	1	0 618
81	117	1.2	1	0 117
82	234	1.2	1	0 234
83	179	1.2	1	0 179
84	87	1.2	1	0 87
85	150	1.2	1	0 150
86	171	1.2	1	0 171
87	410	1.2	1	0 410
88	135	1.2	1	1 134
89	97	1.2	1	0 97
90	109	1.2	1	0 109
91	68	1.2	1	0 68
92	132	1.2	1	0 132
93	197	1.2	1	0 197
94	108	1.2	1	0 108
95	52	1.2	1	0 52
96	51	1.2	1	0 51
97	150	1.2	1	0 150
98	30	1.2	1	0 30
99	95	1.2	1	0 95
100	52	1.2	1	0 52
101	130	1.2	1	1 129
102	179	1.2	1	0 179
103	62	1.2	1	0 62
104	86	1.2	1	1 85
105	81	1.2	1	0 81
106	64	1.2	1	0 64
107	113	1.2	1	0 113
108	93	1.2	1	0 93
109	30	1.2	1	0 30
110	71	1.2	1	0 71
111	20	1.2	1	0 20
112	99	1.2	1	0 99
113	17	1.2	1	1 16
114	30	1.2	1	0 30
115	43	1.2	1	0 43
116	51	1.2	1	0 51
117	119	1.2	1	0 119
118	128	1.2	1	0 128
119	33	1.2	1	0 33
120	18	1.2	1	0 18
121	34	1.2	1	0 34
122	25	1.2	1	0 25
123	66	1.2	1	0 66
124	69	1.2	1	0 69
125	91	1.2	1	0 91
126	108	1.2	1	0 108
127	375	1.2	1	2 373
128	104	1.2	1	0 104
129	53	1.2	1	0 53
130	92	1.2	1	0 92
131	61	1.2	1	0 61
132	43	1.2	1	5 38
133	93	1.2	1	0 93
134	255	1.2	1	1 254
135	236	1.2	1	0 236
136	74	1.2	1	0 74
137	27	1.2	1	0 27
138	79	1.2	1	0 79
139	162	1.2	1	0 162
140	52	1.2	1	0 52
141	57	1.2	1	0 57
142	46	1.2	1	0 46
143	327	1.2	1	0 327
144	149	1.2	1	0 149
145	33	1.2	1	0 33
146	22	1.2	1	0 22
147	50	1.2	1	0 50
148	46	1.2	1	0 46
149	190	1.2	1	0 190
150	57	1.2	1	0 57

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_1.fq.gz
=============================================
20040680 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0022_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'CTGTCTCTTATA' (Nextera; (assigned because of inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0022_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'CTGTCTCTTATA' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a CTGTCTCTTATA /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,040,680
Reads with adapters:                    41,633 (0.2%)
Reads written (passing filters):    20,040,680 (100.0%)

Total basepairs processed: 2,988,780,931 bp
Quality-trimmed:              13,518,529 bp (0.5%)
Total written (filtered):  2,973,955,034 bp (99.5%)

=== Adapter 1 ===

Sequence: CTGTCTCTTATA; Type: regular 3'; Length: 12; Trimmed: 41633 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 45.0%
  C: 17.3%
  G: 17.9%
  T: 19.7%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	17760	19571.0	0	17760
6	5308	4892.7	0	5308
7	1936	1223.2	0	1936
8	712	305.8	0	712
9	399	76.4	0	90 309
10	431	19.1	1	3 428
11	125	4.8	1	0 125
12	105	1.2	1	0 105
13	77	1.2	1	0 77
14	91	1.2	1	0 91
15	76	1.2	1	0 76
16	78	1.2	1	0 78
17	357	1.2	1	0 357
18	82	1.2	1	0 82
19	104	1.2	1	0 104
20	63	1.2	1	0 63
21	64	1.2	1	0 64
22	154	1.2	1	0 154
23	135	1.2	1	0 135
24	535	1.2	1	1 534
25	227	1.2	1	0 227
26	155	1.2	1	0 155
27	74	1.2	1	0 74
28	81	1.2	1	0 81
29	298	1.2	1	2 296
30	94	1.2	1	0 94
31	40	1.2	1	0 40
32	41	1.2	1	0 41
33	44	1.2	1	0 44
34	115	1.2	1	0 115
35	165	1.2	1	0 165
36	80	1.2	1	0 80
37	53	1.2	1	0 53
38	137	1.2	1	0 137
39	73	1.2	1	0 73
40	57	1.2	1	0 57
41	50	1.2	1	0 50
42	41	1.2	1	0 41
43	51	1.2	1	1 50
44	188	1.2	1	0 188
45	228	1.2	1	0 228
46	130	1.2	1	0 130
47	41	1.2	1	0 41
48	15	1.2	1	0 15
49	43	1.2	1	0 43
50	33	1.2	1	0 33
51	132	1.2	1	0 132
52	87	1.2	1	0 87
53	177	1.2	1	0 177
54	44	1.2	1	0 44
55	78	1.2	1	0 78
56	29	1.2	1	0 29
57	22	1.2	1	0 22
58	12	1.2	1	0 12
59	39	1.2	1	0 39
60	193	1.2	1	0 193
61	92	1.2	1	0 92
62	163	1.2	1	0 163
63	93	1.2	1	0 93
64	140	1.2	1	1 139
65	71	1.2	1	0 71
66	62	1.2	1	0 62
67	80	1.2	1	0 80
68	75	1.2	1	0 75
69	93	1.2	1	0 93
70	104	1.2	1	0 104
71	195	1.2	1	0 195
72	93	1.2	1	0 93
73	176	1.2	1	0 176
74	89	1.2	1	0 89
75	90	1.2	1	0 90
76	276	1.2	1	0 276
77	147	1.2	1	0 147
78	103	1.2	1	0 103
79	109	1.2	1	0 109
80	695	1.2	1	0 695
81	144	1.2	1	4 140
82	174	1.2	1	0 174
83	189	1.2	1	0 189
84	31	1.2	1	0 31
85	94	1.2	1	0 94
86	119	1.2	1	0 119
87	368	1.2	1	0 368
88	142	1.2	1	0 142
89	83	1.2	1	0 83
90	107	1.2	1	0 107
91	68	1.2	1	0 68
92	132	1.2	1	0 132
93	209	1.2	1	0 209
94	70	1.2	1	0 70
95	46	1.2	1	0 46
96	80	1.2	1	0 80
97	143	1.2	1	0 143
98	39	1.2	1	0 39
99	37	1.2	1	0 37
100	69	1.2	1	0 69
101	89	1.2	1	0 89
102	132	1.2	1	0 132
103	34	1.2	1	0 34
104	68	1.2	1	0 68
105	70	1.2	1	0 70
106	45	1.2	1	0 45
107	92	1.2	1	0 92
108	81	1.2	1	0 81
109	31	1.2	1	0 31
110	58	1.2	1	0 58
111	21	1.2	1	0 21
112	87	1.2	1	0 87
113	15	1.2	1	3 12
114	43	1.2	1	0 43
115	37	1.2	1	0 37
116	35	1.2	1	0 35
117	146	1.2	1	0 146
118	131	1.2	1	0 131
119	35	1.2	1	0 35
120	17	1.2	1	0 17
121	22	1.2	1	0 22
122	31	1.2	1	0 31
123	30	1.2	1	0 30
124	92	1.2	1	0 92
125	131	1.2	1	0 131
126	136	1.2	1	0 136
127	352	1.2	1	9 343
128	78	1.2	1	0 78
129	51	1.2	1	0 51
130	81	1.2	1	0 81
131	43	1.2	1	0 43
132	35	1.2	1	5 30
133	79	1.2	1	0 79
134	301	1.2	1	0 301
135	298	1.2	1	1 297
136	85	1.2	1	0 85
137	49	1.2	1	0 49
138	29	1.2	1	0 29
139	106	1.2	1	0 106
140	48	1.2	1	0 48
141	77	1.2	1	0 77
142	77	1.2	1	0 77
143	402	1.2	1	1 401
144	122	1.2	1	0 122
145	29	1.2	1	0 29
146	38	1.2	1	0 38
147	59	1.2	1	0 59
148	24	1.2	1	0 24
149	173	1.2	1	0 173
150	39	1.2	1	0 39

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0022_good_2.fq.gz
=============================================
20040680 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0022_good_1_trimmed.fq.gz and Unknown_CK259-004T0022_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0022_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0022_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0022_good_1_trimmed.fq.gz and Unknown_CK259-004T0022_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0022_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0022_good_2_val_2.fq.gz

Total number of sequences analysed: 20040680

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 95251 (0.48%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 21458 (0.11%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0022_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0022_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0022_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0022_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0022_good_1_trimmed.fq.gz and Unknown_CK259-004T0022_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0023_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	1	TGGAATTCTCGG	1000000	0.00
Illumina	1	AGATCGGAAGAGC	1000000	0.00
Nextera	0	CTGTCTCTTATA	1000000	0.00
Unable to auto-detect most prominent adapter from the first specified file (count smallRNA: 1, count Illumina: 1, count Nextera: 0)
Defaulting to Illumina universal adapter ( AGATCGGAAGAGC ). Specify -a SEQUENCE to avoid this behavior).

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0023_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0023_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,545,203
Reads with adapters:                     2,861 (0.0%)
Reads written (passing filters):    19,545,203 (100.0%)

Total basepairs processed: 2,922,302,061 bp
Quality-trimmed:                 189,835 bp (0.0%)
Total written (filtered):  2,921,985,323 bp (100.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 2861 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 39.4%
  C: 20.9%
  G: 28.5%
  T: 10.9%
  none/other: 0.3%

Overview of removed sequences
length	count	expect	max.err	error counts
5	401	19087.1	0	401
6	36	4771.8	0	36
7	6	1192.9	0	6
9	423	74.6	0	0 423
10	166	18.6	1	0 166
11	243	4.7	1	0 243
12	145	1.2	1	0 145
13	13	0.3	1	0 13
14	9	0.3	1	0 9
15	12	0.3	1	0 12
16	7	0.3	1	0 7
17	18	0.3	1	0 18
18	1	0.3	1	0 1
19	11	0.3	1	0 11
20	8	0.3	1	0 8
21	10	0.3	1	0 10
22	6	0.3	1	0 6
23	7	0.3	1	0 7
24	8	0.3	1	0 8
25	5	0.3	1	0 5
26	17	0.3	1	0 17
27	8	0.3	1	0 8
28	9	0.3	1	0 9
29	13	0.3	1	0 13
30	8	0.3	1	0 8
31	9	0.3	1	0 9
32	9	0.3	1	0 9
33	20	0.3	1	0 20
34	23	0.3	1	0 23
35	12	0.3	1	0 12
36	4	0.3	1	0 4
37	6	0.3	1	0 6
38	21	0.3	1	0 21
39	8	0.3	1	0 8
40	23	0.3	1	1 22
41	10	0.3	1	0 10
42	12	0.3	1	0 12
43	6	0.3	1	0 6
44	6	0.3	1	0 6
45	8	0.3	1	0 8
46	9	0.3	1	0 9
47	5	0.3	1	0 5
48	10	0.3	1	0 10
49	5	0.3	1	0 5
50	10	0.3	1	0 10
51	13	0.3	1	0 13
52	22	0.3	1	0 22
53	12	0.3	1	0 12
54	9	0.3	1	0 9
56	7	0.3	1	0 7
57	12	0.3	1	0 12
58	7	0.3	1	0 7
59	13	0.3	1	0 13
60	13	0.3	1	0 13
61	12	0.3	1	0 12
62	11	0.3	1	0 11
63	11	0.3	1	0 11
64	19	0.3	1	1 18
65	11	0.3	1	0 11
66	8	0.3	1	0 8
67	11	0.3	1	0 11
68	16	0.3	1	0 16
69	16	0.3	1	0 16
70	14	0.3	1	0 14
71	14	0.3	1	0 14
72	5	0.3	1	0 5
73	12	0.3	1	0 12
74	25	0.3	1	0 25
75	6	0.3	1	0 6
76	24	0.3	1	0 24
77	17	0.3	1	0 17
78	21	0.3	1	0 21
79	19	0.3	1	0 19
80	10	0.3	1	0 10
81	4	0.3	1	0 4
82	4	0.3	1	0 4
83	9	0.3	1	0 9
84	18	0.3	1	1 17
85	9	0.3	1	1 8
86	17	0.3	1	1 16
87	11	0.3	1	1 10
88	12	0.3	1	2 10
89	10	0.3	1	0 10
90	15	0.3	1	0 15
91	7	0.3	1	1 6
92	8	0.3	1	0 8
93	8	0.3	1	0 8
94	10	0.3	1	0 10
95	10	0.3	1	0 10
96	2	0.3	1	0 2
97	10	0.3	1	1 9
98	13	0.3	1	0 13
99	13	0.3	1	0 13
100	9	0.3	1	0 9
101	8	0.3	1	0 8
102	8	0.3	1	0 8
103	7	0.3	1	0 7
104	9	0.3	1	0 9
105	8	0.3	1	0 8
106	14	0.3	1	0 14
107	8	0.3	1	0 8
108	5	0.3	1	1 4
109	9	0.3	1	0 9
110	5	0.3	1	0 5
111	14	0.3	1	0 14
112	16	0.3	1	0 16
113	15	0.3	1	0 15
114	8	0.3	1	1 7
115	12	0.3	1	1 11
116	6	0.3	1	0 6
117	11	0.3	1	0 11
118	6	0.3	1	0 6
119	6	0.3	1	0 6
120	10	0.3	1	0 10
121	5	0.3	1	0 5
122	9	0.3	1	0 9
123	7	0.3	1	0 7
124	9	0.3	1	0 9
125	11	0.3	1	0 11
126	6	0.3	1	0 6
127	5	0.3	1	0 5
128	14	0.3	1	0 14
129	14	0.3	1	0 14
130	10	0.3	1	0 10
131	13	0.3	1	0 13
132	7	0.3	1	0 7
133	18	0.3	1	0 18
134	7	0.3	1	0 7
135	7	0.3	1	0 7
136	11	0.3	1	0 11
137	5	0.3	1	0 5
138	9	0.3	1	0 9
139	6	0.3	1	1 5
140	4	0.3	1	0 4
141	30	0.3	1	0 30
142	6	0.3	1	0 6
143	4	0.3	1	0 4
144	4	0.3	1	0 4
145	18	0.3	1	0 18
146	6	0.3	1	0 6
147	16	0.3	1	0 16
148	8	0.3	1	0 8
149	5	0.3	1	0 5
150	7	0.3	1	0 7

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_1.fq.gz
=============================================
19545203 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0023_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; default (inconclusive auto-detection))
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0023_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,545,203
Reads with adapters:                     3,128 (0.0%)
Reads written (passing filters):    19,545,203 (100.0%)

Total basepairs processed: 2,922,010,264 bp
Quality-trimmed:                 299,253 bp (0.0%)
Total written (filtered):  2,921,579,710 bp (100.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 3128 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 45.0%
  C: 17.5%
  G: 26.1%
  T: 11.1%
  none/other: 0.3%

Overview of removed sequences
length	count	expect	max.err	error counts
5	390	19087.1	0	390
6	75	4771.8	0	75
7	2	1192.9	0	2
8	1	298.2	0	1
9	545	74.6	0	0 545
10	204	18.6	1	0 204
11	236	4.7	1	0 236
12	211	1.2	1	0 211
13	22	0.3	1	0 22
14	14	0.3	1	0 14
15	14	0.3	1	0 14
16	11	0.3	1	0 11
17	10	0.3	1	0 10
18	9	0.3	1	0 9
19	9	0.3	1	0 9
20	14	0.3	1	0 14
21	9	0.3	1	0 9
22	7	0.3	1	0 7
23	13	0.3	1	0 13
24	11	0.3	1	0 11
25	12	0.3	1	0 12
26	19	0.3	1	0 19
27	10	0.3	1	0 10
28	7	0.3	1	0 7
29	9	0.3	1	0 9
30	6	0.3	1	0 6
31	10	0.3	1	0 10
32	7	0.3	1	0 7
33	9	0.3	1	0 9
34	8	0.3	1	0 8
35	22	0.3	1	1 21
36	13	0.3	1	0 13
37	5	0.3	1	0 5
38	14	0.3	1	0 14
39	14	0.3	1	0 14
40	14	0.3	1	0 14
41	14	0.3	1	0 14
42	7	0.3	1	0 7
43	12	0.3	1	0 12
44	5	0.3	1	0 5
45	4	0.3	1	0 4
46	11	0.3	1	0 11
47	4	0.3	1	0 4
48	10	0.3	1	0 10
49	4	0.3	1	0 4
50	20	0.3	1	0 20
51	6	0.3	1	0 6
52	13	0.3	1	1 12
53	3	0.3	1	2 1
54	15	0.3	1	0 15
55	12	0.3	1	0 12
56	11	0.3	1	0 11
57	16	0.3	1	0 16
58	13	0.3	1	0 13
59	11	0.3	1	0 11
60	9	0.3	1	0 9
61	5	0.3	1	0 5
62	10	0.3	1	0 10
63	12	0.3	1	0 12
64	16	0.3	1	0 16
65	14	0.3	1	0 14
66	7	0.3	1	0 7
67	12	0.3	1	0 12
68	20	0.3	1	0 20
69	14	0.3	1	0 14
70	9	0.3	1	0 9
71	16	0.3	1	1 15
72	11	0.3	1	0 11
73	8	0.3	1	0 8
74	17	0.3	1	0 17
75	5	0.3	1	0 5
76	12	0.3	1	0 12
77	4	0.3	1	0 4
78	9	0.3	1	1 8
79	14	0.3	1	1 13
80	16	0.3	1	0 16
81	9	0.3	1	0 9
82	7	0.3	1	0 7
83	10	0.3	1	1 9
84	9	0.3	1	1 8
85	15	0.3	1	1 14
86	12	0.3	1	0 12
87	9	0.3	1	2 7
88	5	0.3	1	1 4
89	10	0.3	1	0 10
90	10	0.3	1	0 10
91	15	0.3	1	4 11
92	18	0.3	1	0 18
93	10	0.3	1	0 10
94	15	0.3	1	2 13
95	14	0.3	1	1 13
96	8	0.3	1	0 8
97	9	0.3	1	1 8
98	8	0.3	1	0 8
99	7	0.3	1	1 6
100	10	0.3	1	0 10
101	9	0.3	1	0 9
102	9	0.3	1	0 9
103	20	0.3	1	0 20
104	9	0.3	1	0 9
105	4	0.3	1	1 3
106	13	0.3	1	1 12
107	7	0.3	1	1 6
108	10	0.3	1	1 9
109	8	0.3	1	0 8
110	8	0.3	1	0 8
111	17	0.3	1	0 17
112	10	0.3	1	0 10
113	11	0.3	1	0 11
114	11	0.3	1	1 10
115	8	0.3	1	1 7
116	15	0.3	1	0 15
117	12	0.3	1	1 11
118	9	0.3	1	0 9
119	5	0.3	1	1 4
120	11	0.3	1	0 11
121	12	0.3	1	0 12
122	11	0.3	1	0 11
123	8	0.3	1	0 8
124	12	0.3	1	0 12
125	14	0.3	1	0 14
126	5	0.3	1	0 5
127	8	0.3	1	0 8
128	6	0.3	1	1 5
129	14	0.3	1	3 11
130	12	0.3	1	1 11
131	8	0.3	1	0 8
132	9	0.3	1	0 9
133	7	0.3	1	0 7
134	7	0.3	1	0 7
135	7	0.3	1	0 7
136	9	0.3	1	0 9
137	11	0.3	1	0 11
138	8	0.3	1	0 8
139	8	0.3	1	0 8
140	4	0.3	1	0 4
141	18	0.3	1	0 18
142	5	0.3	1	0 5
143	11	0.3	1	0 11
144	10	0.3	1	0 10
145	9	0.3	1	0 9
146	6	0.3	1	0 6
147	22	0.3	1	0 22
148	11	0.3	1	0 11
149	14	0.3	1	0 14
150	9	0.3	1	0 9

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0023_good_2.fq.gz
=============================================
19545203 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0023_good_1_trimmed.fq.gz and Unknown_CK259-004T0023_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0023_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0023_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0023_good_1_trimmed.fq.gz and Unknown_CK259-004T0023_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0023_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0023_good_2_val_2.fq.gz

Total number of sequences analysed: 19545203

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 699 (0.00%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 2 (0.00%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0023_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0023_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0023_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0023_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0023_good_1_trimmed.fq.gz and Unknown_CK259-004T0023_good_2_trimmed.fq.gz

====================================================================================================

正在处理样本 Unknown_CK259-004T0024_good ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/2025hagfish/NGS/Data/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_1.fq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	3	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
smallRNA	0	TGGAATTCTCGG	1000000	0.00
Using Illumina adapter for trimming (count: 3). Second best hit was Nextera (count: 2)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0024_good_1.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_1.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0024_good_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_1.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_1.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,391,848
Reads with adapters:                     4,212 (0.0%)
Reads written (passing filters):    19,391,848 (100.0%)

Total basepairs processed: 2,859,808,279 bp
Quality-trimmed:               7,848,494 bp (0.3%)
Total written (filtered):  2,851,724,417 bp (99.7%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 4212 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 35.9%
  C: 28.4%
  G: 26.8%
  T: 8.8%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
5	354	18937.4	0	354
6	95	4734.3	0	95
7	23	1183.6	0	23
8	5	295.9	0	5
9	300	74.0	0	10 290
10	92	18.5	1	3 89
11	57	4.6	1	0 57
12	27	1.2	1	0 27
13	27	0.3	1	1 26
14	8	0.3	1	0 8
15	36	0.3	1	1 35
16	25	0.3	1	0 25
17	39	0.3	1	1 38
18	19	0.3	1	0 19
19	31	0.3	1	1 30
20	20	0.3	1	1 19
21	41	0.3	1	4 37
22	20	0.3	1	3 17
23	34	0.3	1	6 28
24	40	0.3	1	4 36
25	39	0.3	1	3 36
26	22	0.3	1	1 21
27	30	0.3	1	2 28
28	30	0.3	1	5 25
29	41	0.3	1	2 39
30	27	0.3	1	6 21
31	31	0.3	1	5 26
32	55	0.3	1	5 50
33	74	0.3	1	8 66
34	54	0.3	1	7 47
35	18	0.3	1	4 14
36	88	0.3	1	8 80
37	54	0.3	1	6 48
38	35	0.3	1	3 32
39	41	0.3	1	14 27
40	53	0.3	1	4 49
41	35	0.3	1	2 33
42	36	0.3	1	2 34
43	24	0.3	1	6 18
44	37	0.3	1	12 25
45	42	0.3	1	11 31
46	20	0.3	1	5 15
47	42	0.3	1	10 32
48	28	0.3	1	10 18
49	28	0.3	1	0 28
50	32	0.3	1	6 26
51	23	0.3	1	4 19
52	31	0.3	1	5 26
53	41	0.3	1	10 31
54	58	0.3	1	8 50
55	24	0.3	1	4 20
56	42	0.3	1	6 36
57	27	0.3	1	3 24
58	9	0.3	1	1 8
59	6	0.3	1	1 5
60	3	0.3	1	1 2
61	6	0.3	1	1 5
62	11	0.3	1	1 10
63	15	0.3	1	0 15
64	19	0.3	1	2 17
65	27	0.3	1	2 25
66	6	0.3	1	0 6
67	29	0.3	1	1 28
68	23	0.3	1	2 21
69	9	0.3	1	4 5
70	18	0.3	1	2 16
71	34	0.3	1	0 34
72	44	0.3	1	0 44
73	48	0.3	1	1 47
74	29	0.3	1	3 26
75	32	0.3	1	0 32
76	13	0.3	1	2 11
77	6	0.3	1	0 6
78	17	0.3	1	1 16
79	10	0.3	1	1 9
80	14	0.3	1	1 13
81	38	0.3	1	3 35
82	17	0.3	1	0 17
83	18	0.3	1	6 12
84	11	0.3	1	6 5
85	2	0.3	1	0 2
86	9	0.3	1	2 7
87	25	0.3	1	0 25
88	19	0.3	1	0 19
89	11	0.3	1	2 9
90	47	0.3	1	0 47
91	17	0.3	1	2 15
92	9	0.3	1	2 7
93	7	0.3	1	0 7
94	6	0.3	1	0 6
95	8	0.3	1	0 8
96	28	0.3	1	2 26
97	10	0.3	1	0 10
98	28	0.3	1	0 28
99	8	0.3	1	0 8
100	28	0.3	1	0 28
101	33	0.3	1	0 33
102	13	0.3	1	1 12
103	11	0.3	1	2 9
104	6	0.3	1	1 5
105	8	0.3	1	0 8
106	37	0.3	1	0 37
107	4	0.3	1	0 4
108	6	0.3	1	0 6
109	3	0.3	1	0 3
110	19	0.3	1	10 9
111	6	0.3	1	1 5
112	24	0.3	1	0 24
113	17	0.3	1	0 17
114	19	0.3	1	0 19
115	29	0.3	1	0 29
116	40	0.3	1	2 38
117	5	0.3	1	0 5
118	40	0.3	1	0 40
119	22	0.3	1	0 22
120	6	0.3	1	1 5
121	41	0.3	1	0 41
122	14	0.3	1	0 14
123	8	0.3	1	0 8
124	56	0.3	1	0 56
125	19	0.3	1	0 19
126	26	0.3	1	0 26
127	16	0.3	1	0 16
128	37	0.3	1	4 33
129	45	0.3	1	0 45
130	40	0.3	1	0 40
131	3	0.3	1	0 3
132	8	0.3	1	0 8
133	29	0.3	1	0 29
134	15	0.3	1	0 15
135	5	0.3	1	0 5
136	12	0.3	1	1 11
137	6	0.3	1	0 6
138	17	0.3	1	0 17
139	4	0.3	1	0 4
140	9	0.3	1	0 9
141	22	0.3	1	2 20
142	14	0.3	1	4 10
143	20	0.3	1	0 20
144	12	0.3	1	0 12
145	6	0.3	1	0 6
146	2	0.3	1	0 2
147	41	0.3	1	0 41
148	6	0.3	1	0 6
149	1	0.3	1	0 1
150	1	0.3	1	0 1

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_1.fq.gz
=============================================
19391848 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/2025hagfish/NGS/Data/trim_galore/Unknown_CK259-004T0024_good_2.fq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_2.fq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 5 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to Unknown_CK259-004T0024_good_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_2.fq.gz <<< 
10000000 sequences processed
This is cutadapt 5.0 with Python 3.12.2
Command line parameters: -j 1 -e 0.1 -q 20 -O 5 -a AGATCGGAAGAGC /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_2.fq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              19,391,848
Reads with adapters:                     5,134 (0.0%)
Reads written (passing filters):    19,391,848 (100.0%)

Total basepairs processed: 2,860,277,726 bp
Quality-trimmed:              15,443,713 bp (0.5%)
Total written (filtered):  2,844,556,350 bp (99.5%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 5134 times

Minimum overlap: 5
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 33.5%
  C: 29.2%
  G: 26.7%
  T: 10.2%
  none/other: 0.4%

Overview of removed sequences
length	count	expect	max.err	error counts
5	375	18937.4	0	375
6	78	4734.3	0	78
7	19	1183.6	0	19
8	16	295.9	0	16
9	314	74.0	0	28 286
10	72	18.5	1	6 66
11	109	4.6	1	11 98
12	54	1.2	1	15 39
13	33	0.3	1	7 26
14	37	0.3	1	8 29
15	48	0.3	1	3 45
16	50	0.3	1	11 39
17	45	0.3	1	10 35
18	41	0.3	1	11 30
19	85	0.3	1	32 53
20	76	0.3	1	15 61
21	42	0.3	1	3 39
22	38	0.3	1	13 25
23	53	0.3	1	9 44
24	62	0.3	1	29 33
25	73	0.3	1	12 61
26	48	0.3	1	6 42
27	75	0.3	1	24 51
28	57	0.3	1	8 49
29	38	0.3	1	16 22
30	42	0.3	1	11 31
31	49	0.3	1	11 38
32	48	0.3	1	11 37
33	94	0.3	1	7 87
34	96	0.3	1	18 78
35	30	0.3	1	11 19
36	71	0.3	1	10 61
37	31	0.3	1	3 28
38	75	0.3	1	16 59
39	35	0.3	1	9 26
40	82	0.3	1	15 67
41	23	0.3	1	2 21
42	47	0.3	1	12 35
43	61	0.3	1	17 44
44	41	0.3	1	13 28
45	57	0.3	1	14 43
46	30	0.3	1	10 20
47	51	0.3	1	18 33
48	31	0.3	1	11 20
49	32	0.3	1	9 23
50	33	0.3	1	7 26
51	15	0.3	1	2 13
52	14	0.3	1	2 12
53	35	0.3	1	4 31
54	34	0.3	1	4 30
55	14	0.3	1	4 10
56	14	0.3	1	2 12
57	57	0.3	1	8 49
58	29	0.3	1	3 26
59	15	0.3	1	3 12
60	18	0.3	1	10 8
61	16	0.3	1	5 11
62	21	0.3	1	5 16
63	12	0.3	1	2 10
64	25	0.3	1	0 25
65	26	0.3	1	2 24
66	9	0.3	1	2 7
67	14	0.3	1	1 13
68	18	0.3	1	6 12
69	11	0.3	1	1 10
70	25	0.3	1	3 22
71	40	0.3	1	8 32
72	62	0.3	1	7 55
73	47	0.3	1	4 43
74	34	0.3	1	7 27
75	21	0.3	1	2 19
76	29	0.3	1	5 24
77	24	0.3	1	9 15
78	34	0.3	1	4 30
79	32	0.3	1	4 28
80	13	0.3	1	1 12
81	30	0.3	1	1 29
82	22	0.3	1	3 19
83	21	0.3	1	8 13
84	28	0.3	1	4 24
85	11	0.3	1	5 6
86	23	0.3	1	1 22
87	17	0.3	1	2 15
88	12	0.3	1	2 10
89	16	0.3	1	2 14
90	79	0.3	1	25 54
91	20	0.3	1	2 18
92	4	0.3	1	2 2
93	5	0.3	1	1 4
94	11	0.3	1	2 9
95	17	0.3	1	0 17
96	18	0.3	1	2 16
97	12	0.3	1	0 12
98	24	0.3	1	0 24
99	4	0.3	1	0 4
100	9	0.3	1	1 8
101	44	0.3	1	0 44
102	8	0.3	1	1 7
103	43	0.3	1	2 41
104	3	0.3	1	0 3
105	9	0.3	1	0 9
106	18	0.3	1	1 17
107	6	0.3	1	2 4
108	6	0.3	1	0 6
109	11	0.3	1	2 9
110	10	0.3	1	0 10
111	2	0.3	1	2
112	18	0.3	1	2 16
113	8	0.3	1	0 8
114	9	0.3	1	0 9
115	27	0.3	1	0 27
116	32	0.3	1	1 31
117	4	0.3	1	0 4
118	42	0.3	1	0 42
119	41	0.3	1	0 41
120	12	0.3	1	0 12
121	56	0.3	1	1 55
122	16	0.3	1	2 14
123	21	0.3	1	1 20
124	67	0.3	1	0 67
125	25	0.3	1	1 24
126	13	0.3	1	2 11
127	19	0.3	1	0 19
128	34	0.3	1	0 34
129	24	0.3	1	0 24
130	25	0.3	1	2 23
131	16	0.3	1	8 8
132	2	0.3	1	0 2
133	52	0.3	1	0 52
134	21	0.3	1	0 21
135	4	0.3	1	0 4
136	17	0.3	1	1 16
137	6	0.3	1	0 6
138	28	0.3	1	0 28
139	4	0.3	1	0 4
140	32	0.3	1	0 32
141	43	0.3	1	3 40
142	19	0.3	1	3 16
143	14	0.3	1	0 14
144	11	0.3	1	0 11
145	13	0.3	1	0 13
146	2	0.3	1	0 2
147	49	0.3	1	0 49
148	3	0.3	1	0 3
149	4	0.3	1	0 4
150	3	0.3	1	0 3

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/2025hagfish/NGS/Data/Unknown_CK259-004T0024_good_2.fq.gz
=============================================
19391848 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files Unknown_CK259-004T0024_good_1_trimmed.fq.gz and Unknown_CK259-004T0024_good_2_trimmed.fq.gz
file_1: Unknown_CK259-004T0024_good_1_trimmed.fq.gz, file_2: Unknown_CK259-004T0024_good_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: Unknown_CK259-004T0024_good_1_trimmed.fq.gz and Unknown_CK259-004T0024_good_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to Unknown_CK259-004T0024_good_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to Unknown_CK259-004T0024_good_2_val_2.fq.gz

Total number of sequences analysed: 19391848

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 93759 (0.48%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 24152 (0.12%)


  >>> Now running FastQC on the validated data Unknown_CK259-004T0024_good_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 5% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 10% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 15% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 20% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 25% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 30% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 35% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 40% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 45% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 50% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 55% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 60% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 65% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 70% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 75% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 80% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 85% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 90% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Approx 95% complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz
Analysis complete for Unknown_CK259-004T0024_good_1_val_1.fq.gz

  >>> Now running FastQC on the validated data Unknown_CK259-004T0024_good_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 5% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 10% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 15% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 20% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 25% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 30% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 35% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 40% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 45% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 50% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 55% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 60% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 65% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 70% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 75% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 80% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 85% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 90% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Approx 95% complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Analysis complete for Unknown_CK259-004T0024_good_2_val_2.fq.gz
Deleting both intermediate output files Unknown_CK259-004T0024_good_1_trimmed.fq.gz and Unknown_CK259-004T0024_good_2_trimmed.fq.gz

====================================================================================================

所有样本处理完成！
