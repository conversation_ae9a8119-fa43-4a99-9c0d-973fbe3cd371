# 火山图矢量图输出脚本
# 将所有组织的火山图整合到单个可编辑PDF中

library(ggplot2)
library(dplyr)
library(ggrepel)
library(gridExtra)
library(grid)  # 添加grid包支持

# 设置工作目录
setwd("d:/working/盲鳗2代")

# 获取所有差异表达结果文件
de_files <- list.files("results", pattern = "DE_.*_vs_all\\.csv", full.names = TRUE)

# 创建绘图列表
plot_list <- list()

# 对每个差异表达结果文件绘制火山图
for (de_file in de_files) {
  # 提取组织名称
  tissue <- gsub("results/DE_hg3(.*)_vs_all\\.csv", "\\1", de_file)
  
  # 读取差异表达结果并预处理
  de_results <- read.csv(de_file) %>%
    filter(!is.na(padj)) %>%
    filter(!is.na(pvalue)) %>%
    mutate(
      min_pvalue = min(pvalue[pvalue > 0], na.rm = TRUE),
      pvalue = ifelse(pvalue == 0, min_pvalue, pvalue),
      significant = ifelse(abs(log2FoldChange) > 1 & padj < 0.05, 
                          "Significant", "Not significant"),
      gene = rownames(.)
    ) %>%
    select(-min_pvalue)

  # 获取上下调top5基因
  top_up <- de_results %>%
    filter(log2FoldChange > 1 & padj < 0.05) %>%
    arrange(desc(log2FoldChange), pvalue) %>%
    head(5)
  
  top_down <- de_results %>%
    filter(log2FoldChange < -1 & padj < 0.05) %>%
    arrange(log2FoldChange, pvalue) %>%
    head(5)
  
  genes_to_label <- bind_rows(top_up, top_down)

  # 创建火山图
  p <- ggplot(de_results, aes(x = log2FoldChange, y = -log10(pvalue), 
                            color = -log10(pvalue), size = abs(log2FoldChange))) +
    geom_point(alpha = 0.7) +
    geom_text_repel(
      data = genes_to_label,
      aes(label = gene, color = NULL, size = NULL),
      color = "black",
      size = 3,
      box.padding = 0.5,
      point.padding = 0.5,
      max.overlaps = 100,
      segment.color = "grey50",
      min.segment.length = 0.2
    ) +
    scale_color_gradientn(
      colours = c("blue", "green", "yellow", "red"),
      name = "-log10(p-value)"
    ) +
    scale_size_continuous(
      range = c(1, 3),
      name = "|log2FC|"
    ) +
    labs(
      title = paste(tissue, "vs All"),
      x = "log2 Fold Change",
      y = "-log10(p-value)"
    ) +
    theme_minimal() +
    theme(
      legend.position = "right",
      plot.title = element_text(face = "bold", hjust = 0.5, size = 10),
      axis.title = element_text(size = 8)
    ) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey50") +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey50")

  plot_list[[tissue]] <- p
}

# 计算图形数量
n_plots <- length(plot_list)

# 自动决定列数（最多4列）
n_cols <- min(4, ceiling(sqrt(n_plots)))

# 优化图形布局参数
plot_margin <- unit(0.5, "cm")
title_space <- unit(1.5, "cm")

# 输出PDF矢量图（单页）
pdf("results/combined_volcano_plots.pdf", 
    width = 8 * n_cols,  # 根据列数动态调整宽度
    height = 6 * ceiling(n_plots/n_cols), # 根据行数动态调整高度
    onefile = TRUE,
    title = "Combined Volcano Plots")

# 创建整体布局
grid.arrange(
  grobs = plot_list,
  ncol = n_cols,
  top = textGrob("Differential Expression Analysis - All Tissues", 
                gp = gpar(fontsize = 16, fontface = "bold")),
  bottom = textGrob(paste("Total tissues:", n_plots), 
                   gp = gpar(fontsize = 12)),
  padding = plot_margin,
  widths = rep(1, n_cols),  # 等宽列
  heights = rep(1, ceiling(n_plots/n_cols))  # 等高行
)

# 关闭PDF设备
dev.off()

# 同时保存每个单独图的PDF版本
for(i in seq_along(plot_list)){
  pdf(paste0("results/volcano_plots/", names(plot_list)[i], "_combined.pdf"), 
      width = 10, height = 8)
  print(plot_list[[i]])
  dev.off()
}

message(paste("Combined volcano plots saved to results/combined_volcano_plots.pdf with", 
             n_plots, "plots in", n_rows, "rows"))
