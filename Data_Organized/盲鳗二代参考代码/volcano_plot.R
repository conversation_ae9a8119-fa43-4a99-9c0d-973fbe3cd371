# 火山图绘制脚本
# 基于DESeq2差异分析结果绘制火山图

library(ggplot2)
library(dplyr)
library(ggrepel)  # 用于避免标签重叠

# 设置工作目录(根据实际位置调整)
setwd("d:/working/盲鳗2代")

# 获取所有差异表达结果文件
de_files <- list.files("results", pattern = "DE_.*_vs_all\\.csv", full.names = TRUE)

# 创建火山图目录(如果不存在)
if (!dir.exists("results/volcano_plots")) {
  dir.create("results/volcano_plots")
}

# 对每个差异表达结果文件绘制火山图
for (de_file in de_files) {
  # 提取组织名称
  tissue <- gsub("results/DE_hg3(.*)_vs_all\\.csv", "\\1", de_file)
  
  # 读取差异表达结果并预处理
  de_results <- read.csv(de_file) %>%
    filter(!is.na(padj)) %>%  # 移除padj为NA的行
    filter(!is.na(pvalue)) %>% # 确保pvalue不为NA
    mutate(
      # 处理pvalue=0的情况：
      # 1. 首先找到数据集中最小的非零pvalue值
      # 2. 将所有pvalue=0的值替换为该最小值
      # 这种处理方式可以避免计算-log10(0)得到无限大值
      # 同时保持数据的相对关系
      min_pvalue = min(pvalue[pvalue > 0], na.rm = TRUE),
      pvalue = ifelse(pvalue == 0, min_pvalue, pvalue),
      # 添加显著性标记
      significant = ifelse(abs(log2FoldChange) > 1 & padj < 0.05, 
                          "Significant", "Not significant"),
      # 添加基因名列（直接从第一列获取基因ID）
      gene = as.character(.[[1]])
    ) %>%
    select(-min_pvalue)  # 移除临时变量
  
  # 获取上下调top5基因
  top_up <- de_results %>%
    filter(log2FoldChange > 1 & padj < 0.05) %>%
    arrange(desc(log2FoldChange), pvalue) %>%
    head(5)
  
  top_down <- de_results %>%
    filter(log2FoldChange < -1 & padj < 0.05) %>%
    arrange(log2FoldChange, pvalue) %>%
    head(5)
  
  # 合并要标记的基因
  genes_to_label <- bind_rows(top_up, top_down)
  
  # 绘制火山图
  p <- ggplot(de_results, aes(x = log2FoldChange, y = -log10(pvalue), 
                            color = -log10(pvalue), size = abs(log2FoldChange))) +
    geom_point(alpha = 0.7) +  # 调整透明度
    # 标记top基因
    geom_text_repel(
      data = genes_to_label,
      aes(label = gene, color = NULL, size = NULL),
      color = "black",
      size = 3,
      box.padding = 0.5,
      point.padding = 0.5,
      max.overlaps = 100,
      segment.color = "grey50",
      min.segment.length = 0.2
    ) +
    # 颜色渐变设置
    scale_color_gradientn(
      colours = c("blue", "green", "yellow", "red"),
      name = "-log10(p-value)",
      guide = guide_colorbar(barwidth = 1, barheight = 15)
    ) +
    # 点大小设置
    scale_size_continuous(
      range = c(1, 3),
      name = "|log2FC|",
      guide = guide_legend(override.aes = list(color = "grey50"))
    ) +
    labs(
      title = paste("Volcano Plot:", tissue, "vs All"),
      subtitle = "Top 5 up/down regulated genes labeled",
      x = "log2 Fold Change",
      y = "-log10(p-value)",
      caption = "Significance threshold: |log2FC| > 1, padj < 0.05"
    ) +
    theme_minimal(base_size = 12) +
    theme(
      legend.position = "none",
      plot.title = element_text(face = "bold", hjust = 0.5),
      plot.subtitle = element_text(hjust = 0.5),
      panel.grid.major = element_line(color = "grey90"),
      panel.grid.minor = element_blank(),
      axis.line = element_line(color = "black")
    ) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "grey50") +
    geom_vline(xintercept = c(-1, 1), linetype = "dashed", color = "grey50") +
    # 设置坐标轴范围
    coord_cartesian(xlim = c(-max(abs(de_results$log2FoldChange)), max(abs(de_results$log2FoldChange))))
  
  # 保存火山图(PDF矢量图格式)
  ggsave(
    paste0("results/volcano_plots/volcano_", tissue, ".pdf"), 
    plot = p, 
    width = 10,  # 增加图片宽度以适应标签
    height = 8,
    device = "pdf",  # 指定输出为PDF格式
    bg = "white"  # 设置白色背景
  )
}

message("Volcano plots generated for all tissues!")
