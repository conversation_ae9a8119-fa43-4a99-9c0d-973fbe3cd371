# DESeq2差异表达分析脚本
# 只进行差异分析，不包含绘图代码

# 加载必要的包
library(DESeq2)

# 设置工作目录(根据实际位置调整)
setwd("d:/working/盲鳗2代")

# 读取counts数据
count_data <- read.table("files/counts_processed.txt", header=TRUE, row.names=1, sep="\t")

# 组织样本列表
tissues <- c("L1", "L2", "L3", "L4", "L5", "L6", "sex", "intes", "noto", "axon", 
             "heart", "muscl", "gall", "Lliver", "brain", "solo", "skin1", "ki", 
             "leu", "skin2")

# 创建结果目录(如果不存在)
if (!dir.exists("results")) {
  dir.create("results")
}

# 对每个组织进行差异表达分析
for (tissue in tissues) {
  # 创建样本分组信息
  sample_names <- colnames(count_data)
  condition <- ifelse(grepl(tissue, sample_names), tissue, "other")
  col_data <- data.frame(row.names=sample_names, condition=factor(condition))
  
  # 创建DESeqDataSet对象
  dds <- DESeqDataSetFromMatrix(countData=count_data,
                               colData=col_data,
                               design=~condition)
  
  # 过滤低表达基因
  keep <- rowSums(counts(dds)) >= 10
  dds <- dds[keep,]
  
  # 运行DESeq分析
  dds <- DESeq(dds)
  
  # 获取结果
  res <- results(dds, contrast=c("condition", tissue, "other"))
  
  # 保存结果
  write.csv(as.data.frame(res), file=paste0("results/DE_hg3", tissue, "_vs_all.csv"))
}

# 完成提示
message("DESeq2 differential analysis completed for all tissues!")
