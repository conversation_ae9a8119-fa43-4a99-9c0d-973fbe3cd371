#!/usr/bin/env python
import sys
import pandas as pd

def calculate_fpkm(counts_file, lengths_file, output_file):
    # Read counts data - skip comment and header lines
    with open(counts_file) as f:
        for line in f:
            if not line.startswith('#'):
                break
        header = line.strip().split('\t')
    
    # Read counts and convert to numeric
    counts_df = pd.read_csv(counts_file, sep='\t', comment='#', header=None, 
                          names=header, index_col=0)
    counts_df = counts_df.apply(pd.to_numeric, errors='coerce').fillna(0)
    
    # Read gene lengths
    lengths_df = pd.read_csv(lengths_file, sep='\t', index_col=0)
    
    # Calculate total reads per sample
    total_reads = counts_df.sum(axis=0)
    
    # Calculate FPKM
    fpkm_df = counts_df.copy()
    for sample in fpkm_df.columns:
        fpkm_df[sample] = (counts_df[sample].astype(float) * 1e9) / (lengths_df['length'].astype(float) * total_reads[sample].astype(float))
    
    # Save results
    fpkm_df.to_csv(output_file, sep='\t', float_format='%.4f')

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python calculate_fpkm.py <counts_file> <lengths_file> <output_file>")
        sys.exit(1)
        
    counts_file = sys.argv[1]
    lengths_file = sys.argv[2]
    output_file = sys.argv[3]
    calculate_fpkm(counts_file, lengths_file, output_file)
