import pandas as pd

def rename_fpkm_groups():
    # 读取分组文件
    group_df = pd.read_csv('files/分组.csv', sep='\t', header=None, names=['short_name', 'long_name'])
    # 创建映射字典: long_name → short_name
    name_map = dict(zip(group_df['long_name'], group_df['short_name']))
    
    # 读取fpkm文件
    with open('files/fpkm_matrix.txt', 'r') as f:
        lines = f.readlines()
    
    # 处理表头行
    header = lines[0].strip().split('\t')
    new_header = [header[0]]  # 保留Geneid
    
    # 替换样本名称
    for path in header[1:]:
        long_name = path.split('/')[-1].replace('.sorted.bam', '')
        if long_name in name_map:
            new_header.append(name_map[long_name])
        else:
            new_header.append(path)  # 如果没有匹配则保留原样
    
    # 转换为DataFrame便于列操作
    data = [line.strip().split('\t') for line in lines[1:]]
    df = pd.DataFrame(data, columns=new_header)
    
    # 删除指定列
    columns_to_drop = []
    # 要删除的样本路径
    samples_to_remove = [
        'Unknown_CK259-004T0020_good.sorted.bam',
        'Unknown_CK259-004T0021_good.sorted.bam',
        'Unknown_CK259-004T0024_good.sorted.bam'
    ]
    # 删除匹配的列
    for col in df.columns:
        if any(sample in col for sample in samples_to_remove):
            columns_to_drop.append(col)
    # 删除hg3ovary列
    if 'hg3ovary' in df.columns:
        columns_to_drop.append('hg3ovary')
    
    df = df.drop(columns=columns_to_drop, errors='ignore')
    
    # 写入新文件
    with open('files/fpkm_matrix_final.txt', 'w') as f:
        # 写入表头
        f.write('\t'.join(df.columns) + '\n')
        # 写入数据
        for _, row in df.iterrows():
            # 处理None值，转换为空字符串
            cleaned_values = [str(val) if val is not None else '' for val in row.values]
            f.write('\t'.join(cleaned_values) + '\n')
    
    print(f"组名替换和列删除完成，结果保存在files/fpkm_matrix_final.txt。已删除列：{columns_to_drop}")

if __name__ == '__main__':
    rename_fpkm_groups()
