import pandas as pd

def process_counts():
    # 读取分组文件
    group_df = pd.read_csv('files/分组.csv', sep='\t', header=None, names=['sample', 'description'])
    
    # 获取有效样本列表(排除hg3ovary)
    valid_samples = group_df[group_df['sample'] != 'hg3ovary']['sample'].tolist()
    
    # 读取count数据(跳过注释行)
    count_df = pd.read_csv('files/counts.txt', sep='\t', comment='#')
    
    # 提取样本路径中的样本ID
    # 样本列名格式: /path/to/Unknown_CK259-004T0001_good.sorted.bam
    # 提取为: hg3L1 (需要与分组.csv中的样本名对应)
    # 这里假设前20列对应分组.csv中的样本顺序
    
    # 重命名列名为分组.csv中的样本名
    sample_columns = group_df['sample'].tolist()[:20]  # 取前20个样本名
    column_mapping = {'Geneid': 'Geneid'}
    for i, col in enumerate(count_df.columns[1:21], 1):  # 前20个数据列
        column_mapping[col] = sample_columns[i-1]
    count_df = count_df.rename(columns=column_mapping)
    
    # 筛选有效样本列(排除hg3ovary)
    columns_to_keep = ['Geneid'] + valid_samples
    processed_df = count_df[columns_to_keep]
    
    # 确保列顺序与分组文件一致
    processed_df = processed_df[['Geneid'] + valid_samples]
    
    # 写入处理后的count数据
    with open('files/counts_processed.txt', 'w') as f:
        # 写入表头
        f.write('\t'.join(processed_df.columns) + '\n')
        # 写入数据
        for _, row in processed_df.iterrows():
            f.write('\t'.join(map(str, row.values)) + '\n')
    
    # 保存处理后的样本信息
    group_df[group_df['sample'] != 'hg3ovary'].to_csv(
        'files/sample_info_processed.csv',
        index=False
    )
    
    print(f"Count数据处理完成，结果保存在files/counts_processed.txt。保留样本：{valid_samples}")

if __name__ == '__main__':
    process_counts()
