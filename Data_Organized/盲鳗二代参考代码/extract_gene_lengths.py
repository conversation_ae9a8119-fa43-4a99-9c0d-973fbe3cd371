#!/usr/bin/env python
import sys

def extract_gene_lengths(input_file, output_file):
    with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
        f_out.write("gene_id\tlength\n")
        for line in f_in:
            # Skip comment lines
            if line.startswith('#'):
                continue
            # Skip header line
            if line.startswith('Geneid'):
                continue
                
            parts = line.strip().split('\t')
            if len(parts) >= 6:  # Ensure we have enough columns
                gene_id = parts[0]
                length = parts[5]  # 6th column is Length
                f_out.write(f"{gene_id}\t{length}\n")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python extract_gene_lengths.py <input_file> <output_file>")
        sys.exit(1)
        
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    extract_gene_lengths(input_file, output_file)
