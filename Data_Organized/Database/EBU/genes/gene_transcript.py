import argparse
import csv
import re

def extract_transcript_oid(input_path, output_path):
    results = []

    with open(input_path, "r") as f:
        for line in f:
            if not line.strip() or line.startswith("#"):
                continue
            fields = line.strip().split("\t")
            if len(fields) < 9:
                continue
            if fields[2] != "transcript":
                continue
            attr_field = fields[8]
            gene_id_match = re.search(r'gene_id\s+"([^"]+)"', attr_field)
            oid_match = re.search(r'oId\s+"([^"]+)"', attr_field)
            if gene_id_match and oid_match:
                gene_id = gene_id_match.group(1)
                oid = oid_match.group(1)
                results.append((gene_id, oid))

    with open(output_path, "w", newline="") as out_csv:
        writer = csv.writer(out_csv)
        writer.writerow(["gene_id", "oId"])
        writer.writerows(results)

    print(f"✅ 提取完成：{len(results)} 条记录写入 {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="提取GTF中transcript行的gene_id与oId并输出为CSV")
    parser.add_argument("-i", "--input", required=True, help="输入 GTF 文件路径")
    parser.add_argument("-o", "--output", required=True, help="输出 CSV 文件路径")
    args = parser.parse_args()

    extract_transcript_oid(args.input, args.output)

