#!/bin/bash

# HMMER方法EggNOG-mapper分析
# 与Diamond方法对比，结果分开存储

echo "=== 盲鳗蛋白质功能注释 - HMMER方法 ==="
echo "开始时间: $(date)"

# 设置路径 - 注意与Diamond结果分开
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_hmmer"  # 专门的HMMER结果目录
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_hmmer"

# 创建专门的输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 检查HMMER数据库状态 ==="

# 检查HMMER数据库
HMMER_DB=$(find "$DATA_DIR" -name "*.hmm" 2>/dev/null | head -1)
if [ -n "$HMMER_DB" ]; then
    echo "✅ 找到HMMER数据库: $HMMER_DB"
    echo "数据库大小: $(du -h "$HMMER_DB" | cut -f1)"
else
    echo "❌ 未找到HMMER数据库"
    echo "请等待数据库下载完成或手动下载:"
    echo "download_eggnog_data.py -H -d 2759 --data_dir $DATA_DIR"
    exit 1
fi

echo ""
echo "=== 对比现有Diamond结果 ==="

DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

if [ -f "$DIAMOND_FILE" ]; then
    echo "✅ 找到Diamond结果文件"
    
    # Diamond统计
    DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
    DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "Diamond方法结果 (对比基准):"
    echo "  总注释序列: $DIAMOND_TOTAL"
    echo "  GO注释: $DIAMOND_GO ($(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $DIAMOND_KEGG ($(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $DIAMOND_GENE ($(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
else
    echo "❌ 未找到Diamond结果文件"
fi

echo ""
echo "=== 运行HMMER方法EggNOG-mapper ==="

# 运行HMMER方法
echo "开始HMMER搜索和注释..."
echo "注意: HMMER方法更精确但速度较慢，请耐心等待"

emapper.py \
    -m hmmer \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 4 \
    --override

HMMER_EXIT_CODE=$?
echo "HMMER方法退出码: $HMMER_EXIT_CODE"

echo ""
echo "=== 检查HMMER结果 ==="

# 检查主要输出文件
HMMER_ANNOTATION="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"
HMMER_HITS="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.hits"
HMMER_SEEDS="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.seed_orthologs"

if [ -f "$HMMER_ANNOTATION" ]; then
    echo "✅ HMMER注释成功完成！"
    
    # HMMER统计
    HMMER_TOTAL=$(tail -n +5 "$HMMER_ANNOTATION" | wc -l)
    HMMER_GO=$(tail -n +5 "$HMMER_ANNOTATION" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_KEGG=$(tail -n +5 "$HMMER_ANNOTATION" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_GENE=$(tail -n +5 "$HMMER_ANNOTATION" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_COG=$(tail -n +5 "$HMMER_ANNOTATION" | cut -f7 | grep -v "^-$" | grep -v "^$" | wc -l)
    HMMER_PFAM=$(tail -n +5 "$HMMER_ANNOTATION" | cut -f21 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo ""
    echo "HMMER方法结果:"
    echo "  总注释序列: $HMMER_TOTAL"
    echo "  GO注释: $HMMER_GO ($(echo "scale=1; $HMMER_GO * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $HMMER_KEGG ($(echo "scale=1; $HMMER_KEGG * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $HMMER_GENE ($(echo "scale=1; $HMMER_GENE * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  COG分类: $HMMER_COG ($(echo "scale=1; $HMMER_COG * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  PFAM结构域: $HMMER_PFAM ($(echo "scale=1; $HMMER_PFAM * 100 / $HMMER_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    
    # 与Diamond对比
    if [ -f "$DIAMOND_FILE" ]; then
        echo ""
        echo "=== Diamond vs HMMER 详细对比 ==="
        echo "方法        总序列    GO注释    KEGG注释  基因名    COG分类   PFAM结构域"
        echo "Diamond     $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE      -         -"
        echo "HMMER       $HMMER_TOTAL      $HMMER_GO      $HMMER_KEGG       $HMMER_GENE      $HMMER_COG      $HMMER_PFAM"
        
        # 计算差异
        GO_DIFF=$((HMMER_GO - DIAMOND_GO))
        KEGG_DIFF=$((HMMER_KEGG - DIAMOND_KEGG))
        GENE_DIFF=$((HMMER_GENE - DIAMOND_GENE))
        TOTAL_DIFF=$((HMMER_TOTAL - DIAMOND_TOTAL))
        
        echo ""
        echo "HMMER相对于Diamond的改进:"
        echo "  总注释序列差异: $TOTAL_DIFF"
        echo "  GO注释差异: $GO_DIFF"
        echo "  KEGG注释差异: $KEGG_DIFF"
        echo "  基因名差异: $GENE_DIFF"
        
        # 计算百分比改进
        if [ $DIAMOND_GO -gt 0 ]; then
            GO_IMPROVE=$(echo "scale=1; ($GO_DIFF * 100) / $DIAMOND_GO" | bc -l 2>/dev/null || echo "N/A")
            echo "  GO注释改进: $GO_IMPROVE%"
        fi
        
        if [ $DIAMOND_KEGG -gt 0 ]; then
            KEGG_IMPROVE=$(echo "scale=1; ($KEGG_DIFF * 100) / $DIAMOND_KEGG" | bc -l 2>/dev/null || echo "N/A")
            echo "  KEGG注释改进: $KEGG_IMPROVE%"
        fi
    fi
    
    echo ""
    echo "HMMER注释结果预览:"
    head -10 "$HMMER_ANNOTATION"
    
    # 保存对比结果
    COMPARISON_FILE="$OUTPUT_DIR/diamond_vs_hmmer_comparison.txt"
    cat > "$COMPARISON_FILE" << EOF
# Diamond vs HMMER 注释方法对比
# 生成时间: $(date)
# 数据: 盲鳗蛋白质序列 ($SEQ_COUNT 条)

## 注释统计对比
方法        总序列    GO注释    KEGG注释  基因名    COG分类   PFAM结构域
Diamond     $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE      -         -
HMMER       $HMMER_TOTAL      $HMMER_GO      $HMMER_KEGG       $HMMER_GENE      $HMMER_COG      $HMMER_PFAM

## 改进情况
总注释序列差异: $TOTAL_DIFF
GO注释差异: $GO_DIFF
KEGG注释差异: $KEGG_DIFF
基因名差异: $GENE_DIFF

## 方法特点
Diamond:
- 速度快，适合大规模数据
- 基于BLAST算法
- 内存需求较低

HMMER:
- 更精确的序列搜索
- 基于隐马尔可夫模型
- 更敏感，能发现远程同源序列
- 速度较慢但精度更高
EOF
    
    echo "对比结果已保存: $COMPARISON_FILE"
    
else
    echo "❌ HMMER注释失败"
    
    echo ""
    echo "检查可能的问题:"
    if [ -f "$HMMER_HITS" ]; then
        echo "✅ 找到搜索结果文件，可能是注释步骤失败"
        echo "搜索结果行数: $(wc -l < $HMMER_HITS)"
    else
        echo "❌ 未找到搜索结果文件，可能是搜索步骤失败"
    fi
    
    if [ -f "$HMMER_SEEDS" ]; then
        echo "✅ 找到种子直系同源文件"
        echo "种子直系同源数: $(wc -l < $HMMER_SEEDS)"
    fi
    
    echo ""
    echo "可能的原因:"
    echo "1. HMMER数据库不完整"
    echo "2. 内存不足"
    echo "3. 磁盘空间不足"
    echo "4. 网络连接问题"
fi

echo ""
echo "=== 文件管理总结 ==="

echo ""
echo "Diamond结果文件 (../10_Annotation/eggnog_results/):"
ls -la ../10_Annotation/eggnog_results/ | grep hagfish_proteins

echo ""
echo "HMMER结果文件 (../10_Annotation/eggnog_results_hmmer/):"
ls -la "$OUTPUT_DIR" | grep hagfish_proteins

echo ""
echo "数据库文件 (../10_Annotation/eggnog_data/):"
ls -la "$DATA_DIR"

echo ""
echo "完成时间: $(date)"
echo "=== HMMER方法分析完成 ==="

echo ""
echo "下一步: 运行火山图对比分析"
echo "命令: ./create_hmmer_volcano_plots.R"
