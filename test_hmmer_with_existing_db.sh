#!/bin/bash

# 测试HMMER方法是否能用现有数据库工作

echo "=== 测试HMMER方法 - 使用现有数据库 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/test_hmmer"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="test_hmmer"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo ""
echo "=== 检查现有数据库 ==="

echo "数据目录: $DATA_DIR"
echo "现有文件:"
ls -la "$DATA_DIR"

echo ""
echo "=== 创建小测试样本 ==="

# 创建小测试样本（前10个序列）
head -n 20 "$PROTEIN_FILE" > test_sample.fa
SEQ_COUNT=$(grep -c "^>" test_sample.fa)
echo "测试序列数: $SEQ_COUNT"

echo ""
echo "=== 测试HMMER方法 ==="

# 尝试HMMER方法
emapper.py \
    -m hmmer \
    -i test_sample.fa \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 2 \
    --override

HMMER_EXIT_CODE=$?
echo "HMMER退出码: $HMMER_EXIT_CODE"

echo ""
echo "=== 检查结果 ==="

HMMER_RESULT="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$HMMER_RESULT" ]; then
    echo "✅ HMMER测试成功！"
    echo "结果文件: $HMMER_RESULT"
    echo "结果行数: $(wc -l < $HMMER_RESULT)"
    
    echo ""
    echo "前5行结果:"
    head -5 "$HMMER_RESULT"
    
    echo ""
    echo "✅ HMMER方法可以工作，现在可以运行完整分析"
    
else
    echo "❌ HMMER测试失败"
    
    echo ""
    echo "检查错误日志:"
    if [ -f "$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.hits" ]; then
        echo "找到hits文件，可能是注释步骤失败"
    fi
    
    echo ""
    echo "尝试其他方法..."
    
    # 尝试MMseqs2
    echo "测试MMseqs2方法:"
    emapper.py \
        -m mmseqs \
        -i test_sample.fa \
        --output "${OUTPUT_PREFIX}_mmseqs" \
        --output_dir "$OUTPUT_DIR" \
        --data_dir "$DATA_DIR" \
        --cpu 2 \
        --override
    
    MMSEQS_RESULT="$OUTPUT_DIR/${OUTPUT_PREFIX}_mmseqs.emapper.annotations"
    if [ -f "$MMSEQS_RESULT" ]; then
        echo "✅ MMseqs2测试成功！"
    else
        echo "❌ MMseqs2也失败，可能需要下载MMseqs2数据库"
        echo "命令: download_eggnog_data.py -M --data_dir $DATA_DIR"
    fi
fi

# 清理测试文件
rm -f test_sample.fa

echo ""
echo "完成时间: $(date)"
echo "=== 测试完成 ==="
