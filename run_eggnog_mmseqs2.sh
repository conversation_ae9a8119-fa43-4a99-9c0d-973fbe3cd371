#!/bin/bash

# EggNOG-mapper MMseqs2方法分析
# 作为HMMER的替代方案，MMseqs2提供了速度和精度的平衡

echo "=== 盲鳗蛋白质功能注释 - EggNOG-mapper MMseqs2方法 ==="
echo "开始时间: $(date)"

# 设置路径
PROTEIN_FILE="../08_Results/predicted_proteins.fa"
OUTPUT_DIR="../10_Annotation/eggnog_results_mmseqs2"
DATA_DIR="../10_Annotation/eggnog_data"
OUTPUT_PREFIX="hagfish_proteins_mmseqs2"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 统计序列数量
SEQ_COUNT=$(grep -c "^>" "$PROTEIN_FILE")
echo "蛋白质序列数量: $SEQ_COUNT"

echo ""
echo "=== 当前Diamond方法结果统计 ==="

DIAMOND_FILE="../10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"

if [ -f "$DIAMOND_FILE" ]; then
    echo "分析现有Diamond结果..."
    
    # Diamond统计
    DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
    DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "Diamond方法结果:"
    echo "  总注释序列: $DIAMOND_TOTAL"
    echo "  GO注释: $DIAMOND_GO ($(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $DIAMOND_KEGG ($(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $DIAMOND_GENE ($(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
else
    echo "未找到Diamond结果文件"
fi

echo ""
echo "=== 检查MMseqs2数据库 ==="

# 检查MMseqs2数据库
MMSEQS_DB="$DATA_DIR/mmseqs_dbs/eggnog_proteins.mmseqs"
if [ -f "$MMSEQS_DB" ]; then
    echo "✅ 找到MMseqs2数据库: $MMSEQS_DB"
else
    echo "❌ 未找到MMseqs2数据库"
    echo "可能需要下载: download_eggnog_data.py -M"
    
    # 检查是否有其他MMseqs数据库
    echo "检查可用的数据库文件:"
    find "$DATA_DIR" -name "*mmseqs*" -o -name "*.mmseqs" 2>/dev/null | head -5
fi

echo ""
echo "=== 运行MMseqs2方法 ==="

# MMseqs2搜索
echo "开始MMseqs2搜索..."

emapper.py \
    -m mmseqs \
    -i "$PROTEIN_FILE" \
    --output "$OUTPUT_PREFIX" \
    --output_dir "$OUTPUT_DIR" \
    --data_dir "$DATA_DIR" \
    --cpu 4 \
    --override

MMSEQS_EXIT_CODE=$?
echo "MMseqs2方法退出码: $MMSEQS_EXIT_CODE"

# 检查结果
MMSEQS_FILE="$OUTPUT_DIR/${OUTPUT_PREFIX}.emapper.annotations"

if [ -f "$MMSEQS_FILE" ]; then
    echo "✅ MMseqs2方法成功！"
    
    # MMseqs2统计
    MMSEQS_TOTAL=$(tail -n +5 "$MMSEQS_FILE" | wc -l)
    MMSEQS_GO=$(tail -n +5 "$MMSEQS_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
    MMSEQS_KEGG=$(tail -n +5 "$MMSEQS_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
    MMSEQS_GENE=$(tail -n +5 "$MMSEQS_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)
    MMSEQS_COG=$(tail -n +5 "$MMSEQS_FILE" | cut -f7 | grep -v "^-$" | grep -v "^$" | wc -l)
    MMSEQS_PFAM=$(tail -n +5 "$MMSEQS_FILE" | cut -f21 | grep -v "^-$" | grep -v "^$" | wc -l)
    
    echo "MMseqs2方法结果:"
    echo "  总注释序列: $MMSEQS_TOTAL"
    echo "  GO注释: $MMSEQS_GO ($(echo "scale=1; $MMSEQS_GO * 100 / $MMSEQS_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  KEGG注释: $MMSEQS_KEGG ($(echo "scale=1; $MMSEQS_KEGG * 100 / $MMSEQS_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  基因名: $MMSEQS_GENE ($(echo "scale=1; $MMSEQS_GENE * 100 / $MMSEQS_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  COG分类: $MMSEQS_COG ($(echo "scale=1; $MMSEQS_COG * 100 / $MMSEQS_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    echo "  PFAM结构域: $MMSEQS_PFAM ($(echo "scale=1; $MMSEQS_PFAM * 100 / $MMSEQS_TOTAL" | bc -l 2>/dev/null || echo "N/A")%)"
    
    echo ""
    echo "=== Diamond vs MMseqs2 详细对比 ==="
    echo "方法        总序列    GO注释    KEGG注释  基因名    COG分类   PFAM结构域"
    echo "Diamond     $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE      -         -"
    echo "MMseqs2     $MMSEQS_TOTAL      $MMSEQS_GO      $MMSEQS_KEGG       $MMSEQS_GENE      $MMSEQS_COG      $MMSEQS_PFAM"
    
    # 计算差异和改进
    if [ $DIAMOND_TOTAL -gt 0 ] && [ $MMSEQS_TOTAL -gt 0 ]; then
        echo ""
        echo "MMseqs2相对于Diamond的改进:"
        GO_DIFF=$((MMSEQS_GO - DIAMOND_GO))
        KEGG_DIFF=$((MMSEQS_KEGG - DIAMOND_KEGG))
        GENE_DIFF=$((MMSEQS_GENE - DIAMOND_GENE))
        TOTAL_DIFF=$((MMSEQS_TOTAL - DIAMOND_TOTAL))
        
        echo "  总注释序列差异: $TOTAL_DIFF"
        echo "  GO注释差异: $GO_DIFF"
        echo "  KEGG注释差异: $KEGG_DIFF"
        echo "  基因名差异: $GENE_DIFF"
        
        # 计算百分比改进
        if [ $DIAMOND_GO -gt 0 ]; then
            GO_IMPROVE=$(echo "scale=1; ($GO_DIFF * 100) / $DIAMOND_GO" | bc -l 2>/dev/null || echo "N/A")
            echo "  GO注释改进: $GO_IMPROVE%"
        fi
        
        if [ $DIAMOND_KEGG -gt 0 ]; then
            KEGG_IMPROVE=$(echo "scale=1; ($KEGG_DIFF * 100) / $DIAMOND_KEGG" | bc -l 2>/dev/null || echo "N/A")
            echo "  KEGG注释改进: $KEGG_IMPROVE%"
        fi
        
        if [ $DIAMOND_GENE -gt 0 ]; then
            GENE_IMPROVE=$(echo "scale=1; ($GENE_DIFF * 100) / $DIAMOND_GENE" | bc -l 2>/dev/null || echo "N/A")
            echo "  基因名改进: $GENE_IMPROVE%"
        fi
    fi
    
    echo ""
    echo "MMseqs2方法注释结果预览:"
    head -10 "$MMSEQS_FILE"
    
    # 保存对比结果
    echo ""
    echo "=== 保存对比结果 ==="
    
    COMPARISON_FILE="$OUTPUT_DIR/diamond_vs_mmseqs2_comparison.txt"
    cat > "$COMPARISON_FILE" << EOF
# Diamond vs MMseqs2 注释方法对比
# 生成时间: $(date)
# 数据: 盲鳗蛋白质序列 ($SEQ_COUNT 条)

## 注释统计对比
方法        总序列    GO注释    KEGG注释  基因名    COG分类   PFAM结构域
Diamond     $DIAMOND_TOTAL      $DIAMOND_GO      $DIAMOND_KEGG       $DIAMOND_GENE      -         -
MMseqs2     $MMSEQS_TOTAL      $MMSEQS_GO      $MMSEQS_KEGG       $MMSEQS_GENE      $MMSEQS_COG      $MMSEQS_PFAM

## 改进情况
总注释序列差异: $TOTAL_DIFF
GO注释差异: $GO_DIFF
KEGG注释差异: $KEGG_DIFF
基因名差异: $GENE_DIFF

## 方法特点
Diamond:
- 速度快，适合大规模数据
- 基于BLAST算法
- 内存需求较低

MMseqs2:
- 速度和精度的平衡
- 更敏感的序列搜索
- 可能发现更多远程同源序列
EOF
    
    echo "对比结果已保存: $COMPARISON_FILE"
    
else
    echo "❌ MMseqs2方法失败"
    
    echo ""
    echo "可能的原因:"
    echo "1. 缺少MMseqs2数据库"
    echo "2. 需要下载MMseqs2数据库: download_eggnog_data.py -M"
    echo "3. 内存不足"
    echo "4. 参数配置问题"
    
    echo ""
    echo "建议解决方案:"
    echo "1. 下载MMseqs2数据库:"
    echo "   download_eggnog_data.py -M"
    echo "2. 检查可用内存"
    echo "3. 减少CPU数量: --cpu 2"
fi

echo ""
echo "完成时间: $(date)"
echo "=== EggNOG-mapper MMseqs2方法分析完成 ==="

echo ""
echo "生成的文件:"
ls -la "$OUTPUT_DIR"

echo ""
echo "=== 方法选择建议 ==="
echo "1. Diamond: 快速注释，适合初步分析"
echo "2. MMseqs2: 更敏感的搜索，可能发现更多同源序列"
echo "3. HMMER: 最精确，但需要特定数据库和更多时间"
echo ""
echo "对于盲鳗(真核生物)数据:"
echo "- 推荐使用MMseqs2获得更好的注释覆盖度"
echo "- Diamond结果已经很好，可作为快速分析的基准"
