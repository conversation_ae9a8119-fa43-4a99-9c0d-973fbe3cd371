#!/bin/bash

echo "🎉 === HMMER vs Diamond 注释方法对比分析 === 🎉"
echo

# 文件路径
DIAMOND_FILE="Data_Organized/10_Annotation/eggnog_results/hagfish_proteins.emapper.annotations"
HMMER_FILE="Data_Organized/10_Annotation/eggnog_results_hmmer/hagfish_proteins_hmmer.emapper.annotations"

echo "📁 检查文件存在性和大小:"
if [ -f "$DIAMOND_FILE" ]; then
    DIAMOND_SIZE=$(ls -lh "$DIAMOND_FILE" | awk '{print $5}')
    echo "✅ Diamond结果文件存在 (大小: $DIAMOND_SIZE)"
else
    echo "❌ Diamond结果文件不存在"
    exit 1
fi

if [ -f "$HMMER_FILE" ]; then
    HMMER_SIZE=$(ls -lh "$HMMER_FILE" | awk '{print $5}')
    echo "✅ HMMER结果文件存在 (大小: $HMMER_SIZE)"
else
    echo "❌ HMMER结果文件不存在"
    exit 1
fi

echo
echo "📊 === 基础统计对比 ==="

# Diamond统计
echo "Diamond方法统计:"
DIAMOND_TOTAL=$(tail -n +5 "$DIAMOND_FILE" | wc -l)
DIAMOND_GO=$(tail -n +5 "$DIAMOND_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
DIAMOND_KEGG=$(tail -n +5 "$DIAMOND_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
DIAMOND_GENE=$(tail -n +5 "$DIAMOND_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)

echo "  总注释序列: $DIAMOND_TOTAL"
echo "  GO注释: $DIAMOND_GO"
echo "  KEGG注释: $DIAMOND_KEGG"
echo "  基因名: $DIAMOND_GENE"

echo
echo "HMMER方法统计:"
HMMER_TOTAL=$(tail -n +5 "$HMMER_FILE" | wc -l)
HMMER_GO=$(tail -n +5 "$HMMER_FILE" | cut -f10 | grep -v "^-$" | grep -v "^$" | wc -l)
HMMER_KEGG=$(tail -n +5 "$HMMER_FILE" | cut -f12 | grep -v "^-$" | grep -v "^$" | wc -l)
HMMER_GENE=$(tail -n +5 "$HMMER_FILE" | cut -f9 | grep -v "^-$" | grep -v "^$" | wc -l)

echo "  总注释序列: $HMMER_TOTAL"
echo "  GO注释: $HMMER_GO"
echo "  KEGG注释: $HMMER_KEGG"
echo "  基因名: $HMMER_GENE"

echo
echo "📈 === 对比结果表格 ==="
printf "%-15s %-10s %-10s %-8s %-10s\n" "指标" "Diamond" "HMMER" "差异" "改进率"
echo "================================================================"
printf "%-15s %-10s %-10s %-8s " "总注释序列" "$DIAMOND_TOTAL" "$HMMER_TOTAL" "$((HMMER_TOTAL - DIAMOND_TOTAL))"
if [ $DIAMOND_TOTAL -gt 0 ]; then
    TOTAL_IMPROVE=$(echo "scale=1; ($HMMER_TOTAL - $DIAMOND_TOTAL) * 100 / $DIAMOND_TOTAL" | bc -l)
    printf "%-10s\n" "${TOTAL_IMPROVE}%"
else
    printf "%-10s\n" "N/A"
fi

printf "%-15s %-10s %-10s %-8s " "GO注释" "$DIAMOND_GO" "$HMMER_GO" "$((HMMER_GO - DIAMOND_GO))"
if [ $DIAMOND_GO -gt 0 ]; then
    GO_IMPROVE=$(echo "scale=1; ($HMMER_GO - $DIAMOND_GO) * 100 / $DIAMOND_GO" | bc -l)
    printf "%-10s\n" "${GO_IMPROVE}%"
else
    printf "%-10s\n" "N/A"
fi

printf "%-15s %-10s %-10s %-8s " "KEGG注释" "$DIAMOND_KEGG" "$HMMER_KEGG" "$((HMMER_KEGG - DIAMOND_KEGG))"
if [ $DIAMOND_KEGG -gt 0 ]; then
    KEGG_IMPROVE=$(echo "scale=1; ($HMMER_KEGG - $DIAMOND_KEGG) * 100 / $DIAMOND_KEGG" | bc -l)
    printf "%-10s\n" "${KEGG_IMPROVE}%"
else
    printf "%-10s\n" "N/A"
fi

printf "%-15s %-10s %-10s %-8s " "基因名" "$DIAMOND_GENE" "$HMMER_GENE" "$((HMMER_GENE - DIAMOND_GENE))"
if [ $DIAMOND_GENE -gt 0 ]; then
    GENE_IMPROVE=$(echo "scale=1; ($HMMER_GENE - $DIAMOND_GENE) * 100 / $DIAMOND_GENE" | bc -l)
    printf "%-10s\n" "${GENE_IMPROVE}%"
else
    printf "%-10s\n" "N/A"
fi

echo
echo "🎯 === 覆盖率对比 ==="
if [ $DIAMOND_TOTAL -gt 0 ]; then
    DIAMOND_GO_RATE=$(echo "scale=1; $DIAMOND_GO * 100 / $DIAMOND_TOTAL" | bc -l)
    DIAMOND_KEGG_RATE=$(echo "scale=1; $DIAMOND_KEGG * 100 / $DIAMOND_TOTAL" | bc -l)
    DIAMOND_GENE_RATE=$(echo "scale=1; $DIAMOND_GENE * 100 / $DIAMOND_TOTAL" | bc -l)
    echo "Diamond覆盖率: GO ${DIAMOND_GO_RATE}%, KEGG ${DIAMOND_KEGG_RATE}%, 基因名 ${DIAMOND_GENE_RATE}%"
fi

if [ $HMMER_TOTAL -gt 0 ]; then
    HMMER_GO_RATE=$(echo "scale=1; $HMMER_GO * 100 / $HMMER_TOTAL" | bc -l)
    HMMER_KEGG_RATE=$(echo "scale=1; $HMMER_KEGG * 100 / $HMMER_TOTAL" | bc -l)
    HMMER_GENE_RATE=$(echo "scale=1; $HMMER_GENE * 100 / $HMMER_TOTAL" | bc -l)
    echo "HMMER覆盖率:  GO ${HMMER_GO_RATE}%, KEGG ${HMMER_KEGG_RATE}%, 基因名 ${HMMER_GENE_RATE}%"
fi

echo
echo "🏆 === 结论总结 ==="
if [ $HMMER_TOTAL -gt $DIAMOND_TOTAL ]; then
    echo "🎯 HMMER方法注释了更多的序列 (+$((HMMER_TOTAL - DIAMOND_TOTAL)))"
elif [ $HMMER_TOTAL -lt $DIAMOND_TOTAL ]; then
    echo "🎯 Diamond方法注释了更多的序列 (+$((DIAMOND_TOTAL - HMMER_TOTAL)))"
else
    echo "🎯 两种方法注释的序列数量相同"
fi

if [ $HMMER_GO -gt $DIAMOND_GO ]; then
    echo "🧬 HMMER在GO注释方面表现更好 (+$((HMMER_GO - DIAMOND_GO)))"
elif [ $HMMER_GO -lt $DIAMOND_GO ]; then
    echo "🧬 Diamond在GO注释方面表现更好 (+$((DIAMOND_GO - HMMER_GO)))"
else
    echo "🧬 两种方法GO注释数量相同"
fi

if [ $HMMER_KEGG -gt $DIAMOND_KEGG ]; then
    echo "🔬 HMMER在KEGG注释方面表现更好 (+$((HMMER_KEGG - DIAMOND_KEGG)))"
elif [ $HMMER_KEGG -lt $DIAMOND_KEGG ]; then
    echo "🔬 Diamond在KEGG注释方面表现更好 (+$((DIAMOND_KEGG - HMMER_KEGG)))"
else
    echo "🔬 两种方法KEGG注释数量相同"
fi

echo
echo "📋 === 结果文件预览 ==="
echo "Diamond结果前3行 (跳过注释行):"
head -8 "$DIAMOND_FILE" | tail -3

echo
echo "HMMER结果前3行 (跳过注释行):"
head -8 "$HMMER_FILE" | tail -3

echo
echo "✅ 对比分析完成！"
echo "📁 结果文件位置:"
echo "   Diamond: $DIAMOND_FILE"
echo "   HMMER:   $HMMER_FILE"
