#!/bin/bash

# 下载真核生物HMMER数据库用于EggNOG-mapper
# 基于官方文档: download_eggnog_data.py -H -d taxID

echo "=== 下载真核生物HMMER数据库 ==="
echo "开始时间: $(date)"

DATA_DIR="../10_Annotation/eggnog_data"

echo "数据目录: $DATA_DIR"

echo ""
echo "=== 当前数据库状态 ==="

echo "现有数据库文件:"
ls -la "$DATA_DIR"

echo ""
echo "=== 下载真核生物HMMER数据库 ==="

# 真核生物的税号是2759
# 根据官方文档: download_eggnog_data.py -H -d 2759

echo "下载真核生物(taxID: 2759)的HMMER数据库..."
echo "注意: 这可能需要一些时间和网络带宽"

download_eggnog_data.py -H -d 2759 --data_dir "$DATA_DIR"

DOWNLOAD_EXIT_CODE=$?
echo "下载退出码: $DOWNLOAD_EXIT_CODE"

echo ""
echo "=== 检查下载结果 ==="

echo "下载后的数据库文件:"
ls -la "$DATA_DIR"

# 检查HMMER数据库文件
HMMER_FILES=$(find "$DATA_DIR" -name "*.hmm*" 2>/dev/null)

if [ -n "$HMMER_FILES" ]; then
    echo ""
    echo "✅ HMMER数据库下载成功！"
    echo "HMMER数据库文件:"
    echo "$HMMER_FILES"
else
    echo ""
    echo "❌ 未找到HMMER数据库文件"
    echo "可能的原因:"
    echo "1. 下载失败"
    echo "2. 网络问题"
    echo "3. 磁盘空间不足"
    
    echo ""
    echo "尝试其他方法..."
    
    # 尝试下载所有可用的HMMER数据库
    echo "尝试下载默认HMMER数据库..."
    download_eggnog_data.py -H --data_dir "$DATA_DIR"
fi

echo ""
echo "=== 数据库大小统计 ==="

echo "数据目录总大小:"
du -sh "$DATA_DIR"

echo ""
echo "各文件大小:"
du -h "$DATA_DIR"/*

echo ""
echo "完成时间: $(date)"
echo "=== HMMER数据库下载完成 ==="

echo ""
echo "下一步: 运行HMMER方法的EggNOG-mapper分析"
echo "命令: emapper.py -m hmmer -i input.fa --data_dir $DATA_DIR"
