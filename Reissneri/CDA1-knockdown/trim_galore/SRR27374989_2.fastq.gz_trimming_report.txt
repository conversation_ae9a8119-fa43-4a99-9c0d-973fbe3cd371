
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using Illumina adapter for trimming (count: 10). Second best hit was smallRNA (count: 6)
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a AGATCGGAAGAGC /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,879,266
Reads with adapters:                   464,675 (2.0%)
Reads written (passing filters):    22,879,266 (100.0%)

Total basepairs processed: 3,295,765,805 bp
Quality-trimmed:              30,468,890 bp (0.9%)
Total written (filtered):  3,263,494,129 bp (99.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 464675 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 30.6%
  C: 28.9%
  G: 29.3%
  T: 11.2%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	356507	357488.5	0	356507
4	71267	89372.1	0	71267
5	19910	22343.0	0	19910
6	7362	5585.8	0	7362
7	1870	1396.4	0	1870
8	583	349.1	0	583
9	1636	87.3	0	342 1294
10	715	21.8	1	178 537
11	392	5.5	1	110 282
12	230	1.4	1	122 108
13	104	0.3	1	64 40
14	122	0.3	1	78 44
15	114	0.3	1	84 30
16	123	0.3	1	88 35
17	130	0.3	1	96 34
18	137	0.3	1	101 36
19	195	0.3	1	146 49
20	85	0.3	1	54 31
21	57	0.3	1	30 27
22	49	0.3	1	20 29
23	44	0.3	1	24 20
24	84	0.3	1	60 24
25	46	0.3	1	20 26
26	90	0.3	1	39 51
27	104	0.3	1	66 38
28	47	0.3	1	23 24
29	42	0.3	1	20 22
30	45	0.3	1	32 13
31	43	0.3	1	19 24
32	33	0.3	1	18 15
33	34	0.3	1	16 18
34	33	0.3	1	19 14
35	46	0.3	1	17 29
36	26	0.3	1	7 19
37	20	0.3	1	7 13
38	32	0.3	1	9 23
39	37	0.3	1	15 22
40	23	0.3	1	8 15
41	22	0.3	1	6 16
42	9	0.3	1	0 9
43	33	0.3	1	10 23
44	27	0.3	1	7 20
45	21	0.3	1	8 13
46	14	0.3	1	1 13
47	17	0.3	1	5 12
48	22	0.3	1	9 13
49	17	0.3	1	1 16
50	39	0.3	1	2 37
51	24	0.3	1	2 22
52	18	0.3	1	7 11
53	12	0.3	1	0 12
54	21	0.3	1	3 18
55	12	0.3	1	3 9
56	14	0.3	1	1 13
57	22	0.3	1	1 21
58	20	0.3	1	1 19
59	21	0.3	1	3 18
60	21	0.3	1	0 21
61	27	0.3	1	0 27
62	23	0.3	1	0 23
63	32	0.3	1	0 32
64	27	0.3	1	1 26
65	23	0.3	1	1 22
66	21	0.3	1	2 19
67	24	0.3	1	0 24
68	17	0.3	1	3 14
69	17	0.3	1	1 16
70	23	0.3	1	4 19
71	25	0.3	1	2 23
72	16	0.3	1	0 16
73	19	0.3	1	0 19
74	9	0.3	1	1 8
75	12	0.3	1	0 12
76	22	0.3	1	0 22
77	23	0.3	1	0 23
78	19	0.3	1	1 18
79	14	0.3	1	0 14
80	13	0.3	1	0 13
81	32	0.3	1	2 30
82	23	0.3	1	1 22
83	27	0.3	1	1 26
84	19	0.3	1	0 19
85	15	0.3	1	0 15
86	16	0.3	1	1 15
87	10	0.3	1	0 10
88	13	0.3	1	2 11
89	14	0.3	1	1 13
90	24	0.3	1	1 23
91	10	0.3	1	0 10
92	22	0.3	1	0 22
93	17	0.3	1	0 17
94	22	0.3	1	0 22
95	17	0.3	1	0 17
96	17	0.3	1	3 14
97	16	0.3	1	1 15
98	24	0.3	1	3 21
99	26	0.3	1	0 26
100	8	0.3	1	0 8
101	28	0.3	1	2 26
102	22	0.3	1	2 20
103	17	0.3	1	3 14
104	21	0.3	1	3 18
105	28	0.3	1	4 24
106	22	0.3	1	1 21
107	24	0.3	1	2 22
108	21	0.3	1	4 17
109	30	0.3	1	7 23
110	21	0.3	1	4 17
111	19	0.3	1	3 16
112	25	0.3	1	4 21
113	17	0.3	1	1 16
114	23	0.3	1	5 18
115	40	0.3	1	6 34
116	26	0.3	1	4 22
117	30	0.3	1	3 27
118	27	0.3	1	3 24
119	21	0.3	1	6 15
120	29	0.3	1	5 24
121	27	0.3	1	5 22
122	24	0.3	1	5 19
123	26	0.3	1	4 22
124	23	0.3	1	4 19
125	37	0.3	1	3 34
126	31	0.3	1	5 26
127	20	0.3	1	6 14
128	26	0.3	1	9 17
129	23	0.3	1	2 21
130	28	0.3	1	8 20
131	24	0.3	1	2 22
132	19	0.3	1	2 17
133	22	0.3	1	1 21
134	37	0.3	1	0 37
135	17	0.3	1	2 15
136	20	0.3	1	0 20
137	14	0.3	1	0 14
138	12	0.3	1	1 11
139	14	0.3	1	1 13
140	23	0.3	1	1 22
141	15	0.3	1	4 11
142	7	0.3	1	1 6
143	8	0.3	1	0 8
144	2	0.3	1	0 2
145	9	0.3	1	1 8
146	11	0.3	1	0 11
147	43	0.3	1	0 43
148	5	0.3	1	0 5
149	11	0.3	1	1 10
150	5	0.3	1	0 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz
=============================================
22879266 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3265 (0.01%)
Total number of sequences analysed for the sequence pair length validation: 22879266

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 141682 (0.62%)
