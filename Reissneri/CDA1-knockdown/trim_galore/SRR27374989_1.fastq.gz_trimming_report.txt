
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using Illumina adapter for trimming (count: 10). Second best hit was smallRNA (count: 6)
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a AGATCGGAAGAGC /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,879,266
Reads with adapters:                   402,396 (1.8%)
Reads written (passing filters):    22,879,266 (100.0%)

Total basepairs processed: 3,300,752,404 bp
Quality-trimmed:              23,747,533 bp (0.7%)
Total written (filtered):  3,275,318,117 bp (99.2%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 402396 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 24.1%
  C: 28.5%
  G: 28.8%
  T: 18.6%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
3	322204	357488.5	0	322204
4	68842	89372.1	0	68842
5	3305	22343.0	0	3305
6	808	5585.8	0	808
7	322	1396.4	0	322
8	31	349.1	0	31
9	447	87.3	0	34 413
10	251	21.8	1	25 226
11	225	5.5	1	2 223
12	204	1.4	1	10 194
13	126	0.3	1	39 87
14	71	0.3	1	4 67
15	100	0.3	1	0 100
16	92	0.3	1	0 92
17	100	0.3	1	2 98
18	102	0.3	1	4 98
19	90	0.3	1	4 86
20	109	0.3	1	5 104
21	68	0.3	1	2 66
22	73	0.3	1	3 70
23	53	0.3	1	3 50
24	75	0.3	1	7 68
25	91	0.3	1	5 86
26	85	0.3	1	8 77
27	95	0.3	1	7 88
28	69	0.3	1	8 61
29	64	0.3	1	3 61
30	71	0.3	1	4 67
31	41	0.3	1	5 36
32	81	0.3	1	5 76
33	50	0.3	1	4 46
34	36	0.3	1	3 33
35	30	0.3	1	5 25
36	33	0.3	1	2 31
37	38	0.3	1	2 36
38	33	0.3	1	2 31
39	101	0.3	1	1 100
40	50	0.3	1	0 50
41	42	0.3	1	3 39
42	42	0.3	1	4 38
43	40	0.3	1	3 37
44	40	0.3	1	2 38
45	27	0.3	1	3 24
46	36	0.3	1	0 36
47	20	0.3	1	2 18
48	24	0.3	1	1 23
49	21	0.3	1	2 19
50	40	0.3	1	1 39
51	49	0.3	1	1 48
52	67	0.3	1	0 67
53	55	0.3	1	1 54
54	35	0.3	1	2 33
55	42	0.3	1	0 42
56	23	0.3	1	0 23
57	21	0.3	1	0 21
58	23	0.3	1	0 23
59	14	0.3	1	2 12
60	22	0.3	1	0 22
61	18	0.3	1	0 18
62	28	0.3	1	0 28
63	40	0.3	1	0 40
64	50	0.3	1	0 50
65	24	0.3	1	0 24
66	28	0.3	1	2 26
67	36	0.3	1	0 36
68	33	0.3	1	0 33
69	31	0.3	1	0 31
70	28	0.3	1	2 26
71	22	0.3	1	0 22
72	24	0.3	1	0 24
73	28	0.3	1	0 28
74	17	0.3	1	0 17
75	18	0.3	1	0 18
76	29	0.3	1	0 29
77	32	0.3	1	0 32
78	22	0.3	1	1 21
79	23	0.3	1	0 23
80	33	0.3	1	0 33
81	33	0.3	1	0 33
82	44	0.3	1	0 44
83	26	0.3	1	0 26
84	38	0.3	1	0 38
85	30	0.3	1	0 30
86	31	0.3	1	0 31
87	30	0.3	1	0 30
88	36	0.3	1	0 36
89	37	0.3	1	0 37
90	41	0.3	1	0 41
91	37	0.3	1	0 37
92	41	0.3	1	0 41
93	27	0.3	1	0 27
94	23	0.3	1	0 23
95	28	0.3	1	0 28
96	35	0.3	1	0 35
97	30	0.3	1	0 30
98	44	0.3	1	0 44
99	25	0.3	1	0 25
100	64	0.3	1	0 64
101	52	0.3	1	0 52
102	80	0.3	1	0 80
103	40	0.3	1	0 40
104	24	0.3	1	0 24
105	40	0.3	1	0 40
106	66	0.3	1	0 66
107	30	0.3	1	0 30
108	24	0.3	1	0 24
109	29	0.3	1	0 29
110	50	0.3	1	0 50
111	37	0.3	1	0 37
112	43	0.3	1	0 43
113	45	0.3	1	0 45
114	55	0.3	1	0 55
115	35	0.3	1	0 35
116	24	0.3	1	0 24
117	30	0.3	1	0 30
118	26	0.3	1	0 26
119	36	0.3	1	0 36
120	22	0.3	1	2 20
121	15	0.3	1	1 14
122	25	0.3	1	1 24
123	26	0.3	1	1 25
124	33	0.3	1	0 33
125	25	0.3	1	0 25
126	37	0.3	1	1 36
127	45	0.3	1	0 45
128	42	0.3	1	0 42
129	35	0.3	1	0 35
130	30	0.3	1	0 30
131	54	0.3	1	1 53
132	21	0.3	1	0 21
133	52	0.3	1	0 52
134	16	0.3	1	0 16
135	17	0.3	1	1 16
136	30	0.3	1	0 30
137	24	0.3	1	0 24
138	26	0.3	1	0 26
139	25	0.3	1	0 25
140	17	0.3	1	0 17
141	28	0.3	1	0 28
142	12	0.3	1	0 12
143	12	0.3	1	0 12
144	26	0.3	1	0 26
145	29	0.3	1	0 29
146	20	0.3	1	0 20
147	46	0.3	1	0 46
148	8	0.3	1	0 8
149	9	0.3	1	0 9
150	210	0.3	1	0 210

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz
=============================================
22879266 sequences processed in total

