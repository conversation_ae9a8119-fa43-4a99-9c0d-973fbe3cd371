
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 12). Second best hit was Illumina (count: 7)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,178,360
Reads with adapters:                   589,464 (2.7%)
Reads written (passing filters):    22,178,360 (100.0%)

Total basepairs processed: 3,209,236,794 bp
Quality-trimmed:              22,270,440 bp (0.7%)
Total written (filtered):  3,184,407,978 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 589464 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.4%
  C: 18.3%
  G: 34.2%
  T: 24.1%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	455350	346536.9	0	455350
4	80579	86634.2	0	80579
5	36261	21658.6	0	36261
6	5751	5414.6	0	5751
7	930	1353.7	0	930
8	237	338.4	0	237
9	525	84.6	0	81 444
10	923	21.2	1	33 890
11	397	5.3	1	7 390
12	157	1.3	1	3 154
13	94	1.3	1	1 93
14	76	1.3	1	4 72
15	60	1.3	1	1 59
16	79	1.3	1	1 78
17	58	1.3	1	4 54
18	74	1.3	1	1 73
19	55	1.3	1	2 53
20	81	1.3	1	1 80
21	84	1.3	1	2 82
22	56	1.3	1	0 56
23	76	1.3	1	4 72
24	54	1.3	1	2 52
25	70	1.3	1	1 69
26	109	1.3	1	6 103
27	135	1.3	1	13 122
28	80	1.3	1	3 77
29	73	1.3	1	4 69
30	92	1.3	1	1 91
31	68	1.3	1	2 66
32	96	1.3	1	2 94
33	92	1.3	1	1 91
34	65	1.3	1	0 65
35	78	1.3	1	1 77
36	76	1.3	1	5 71
37	67	1.3	1	0 67
38	40	1.3	1	1 39
39	104	1.3	1	0 104
40	58	1.3	1	4 54
41	55	1.3	1	4 51
42	42	1.3	1	3 39
43	75	1.3	1	2 73
44	37	1.3	1	0 37
45	39	1.3	1	2 37
46	55	1.3	1	2 53
47	54	1.3	1	7 47
48	72	1.3	1	2 70
49	113	1.3	1	4 109
50	95	1.3	1	0 95
51	105	1.3	1	0 105
52	56	1.3	1	0 56
53	136	1.3	1	1 135
54	151	1.3	1	0 151
55	93	1.3	1	2 91
56	105	1.3	1	8 97
57	108	1.3	1	4 104
58	37	1.3	1	1 36
59	59	1.3	1	3 56
60	69	1.3	1	1 68
61	64	1.3	1	0 64
62	51	1.3	1	5 46
63	56	1.3	1	0 56
64	47	1.3	1	0 47
65	59	1.3	1	1 58
66	67	1.3	1	2 65
67	76	1.3	1	0 76
68	58	1.3	1	0 58
69	87	1.3	1	0 87
70	32	1.3	1	0 32
71	31	1.3	1	0 31
72	68	1.3	1	1 67
73	51	1.3	1	2 49
74	78	1.3	1	1 77
75	72	1.3	1	3 69
76	52	1.3	1	3 49
77	68	1.3	1	0 68
78	52	1.3	1	2 50
79	62	1.3	1	3 59
80	44	1.3	1	1 43
81	54	1.3	1	0 54
82	41	1.3	1	0 41
83	41	1.3	1	1 40
84	39	1.3	1	0 39
85	65	1.3	1	3 62
86	67	1.3	1	0 67
87	35	1.3	1	0 35
88	50	1.3	1	4 46
89	50	1.3	1	3 47
90	36	1.3	1	1 35
91	20	1.3	1	1 19
92	48	1.3	1	1 47
93	37	1.3	1	3 34
94	48	1.3	1	0 48
95	41	1.3	1	2 39
96	74	1.3	1	0 74
97	65	1.3	1	2 63
98	39	1.3	1	0 39
99	40	1.3	1	4 36
100	39	1.3	1	0 39
101	95	1.3	1	1 94
102	85	1.3	1	0 85
103	66	1.3	1	0 66
104	27	1.3	1	7 20
105	64	1.3	1	2 62
106	41	1.3	1	1 40
107	48	1.3	1	3 45
108	46	1.3	1	0 46
109	50	1.3	1	1 49
110	33	1.3	1	0 33
111	62	1.3	1	2 60
112	45	1.3	1	0 45
113	46	1.3	1	4 42
114	40	1.3	1	0 40
115	37	1.3	1	1 36
116	35	1.3	1	3 32
117	38	1.3	1	0 38
118	61	1.3	1	1 60
119	56	1.3	1	0 56
120	49	1.3	1	3 46
121	37	1.3	1	0 37
122	49	1.3	1	1 48
123	43	1.3	1	0 43
124	43	1.3	1	0 43
125	36	1.3	1	2 34
126	47	1.3	1	0 47
127	39	1.3	1	1 38
128	44	1.3	1	4 40
129	48	1.3	1	4 44
130	34	1.3	1	1 33
131	25	1.3	1	4 21
132	38	1.3	1	3 35
133	67	1.3	1	2 65
134	42	1.3	1	4 38
135	37	1.3	1	1 36
136	42	1.3	1	3 39
137	26	1.3	1	1 25
138	33	1.3	1	0 33
139	30	1.3	1	0 30
140	29	1.3	1	0 29
141	53	1.3	1	0 53
142	23	1.3	1	0 23
143	58	1.3	1	0 58
144	170	1.3	1	4 166
145	42	1.3	1	1 41
146	22	1.3	1	0 22
147	40	1.3	1	0 40
148	72	1.3	1	0 72
149	171	1.3	1	5 166
150	80	1.3	1	3 77

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz
=============================================
22178360 sequences processed in total

