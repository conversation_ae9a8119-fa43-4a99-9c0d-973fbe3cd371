
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 17). Second best hit was Illumina (count: 11)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,996,933
Reads with adapters:                   326,557 (1.4%)
Reads written (passing filters):    22,996,933 (100.0%)

Total basepairs processed: 3,307,180,169 bp
Quality-trimmed:              29,196,485 bp (0.9%)
Total written (filtered):  3,276,664,852 bp (99.1%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 326557 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 29.6%
  C: 20.4%
  G: 18.5%
  T: 31.6%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	247850	359327.1	0	247850
4	52216	89831.8	0	52216
5	17431	22457.9	0	17431
6	3706	5614.5	0	3706
7	742	1403.6	0	742
8	358	350.9	0	358
9	609	87.7	0	43 566
10	401	21.9	1	12 389
11	172	5.5	1	0 172
12	25	1.4	1	0 25
13	17	1.4	1	0 17
14	13	1.4	1	0 13
15	26	1.4	1	0 26
16	16	1.4	1	0 16
17	30	1.4	1	0 30
18	18	1.4	1	0 18
19	20	1.4	1	0 20
20	36	1.4	1	0 36
21	18	1.4	1	1 17
22	22	1.4	1	0 22
23	29	1.4	1	0 29
24	21	1.4	1	0 21
25	25	1.4	1	0 25
26	51	1.4	1	0 51
27	26	1.4	1	0 26
28	36	1.4	1	0 36
29	17	1.4	1	0 17
30	19	1.4	1	0 19
31	26	1.4	1	0 26
32	21	1.4	1	0 21
33	25	1.4	1	0 25
34	43	1.4	1	1 42
35	27	1.4	1	0 27
36	17	1.4	1	0 17
37	36	1.4	1	2 34
38	27	1.4	1	0 27
39	23	1.4	1	0 23
40	37	1.4	1	0 37
41	24	1.4	1	0 24
42	18	1.4	1	0 18
43	24	1.4	1	0 24
44	28	1.4	1	1 27
45	17	1.4	1	1 16
46	28	1.4	1	0 28
47	17	1.4	1	0 17
48	27	1.4	1	0 27
49	21	1.4	1	0 21
50	17	1.4	1	0 17
51	20	1.4	1	0 20
52	25	1.4	1	0 25
53	28	1.4	1	0 28
54	23	1.4	1	0 23
55	19	1.4	1	0 19
56	15	1.4	1	0 15
57	13	1.4	1	0 13
58	12	1.4	1	0 12
59	22	1.4	1	0 22
60	37	1.4	1	1 36
61	33	1.4	1	0 33
62	21	1.4	1	0 21
63	33	1.4	1	0 33
64	20	1.4	1	0 20
65	32	1.4	1	0 32
66	22	1.4	1	0 22
67	13	1.4	1	0 13
68	19	1.4	1	0 19
69	24	1.4	1	0 24
70	21	1.4	1	2 19
71	23	1.4	1	0 23
72	25	1.4	1	0 25
73	17	1.4	1	0 17
74	15	1.4	1	0 15
75	30	1.4	1	0 30
76	14	1.4	1	0 14
77	18	1.4	1	0 18
78	28	1.4	1	1 27
79	18	1.4	1	0 18
80	27	1.4	1	0 27
81	25	1.4	1	2 23
82	25	1.4	1	0 25
83	13	1.4	1	0 13
84	19	1.4	1	1 18
85	11	1.4	1	0 11
86	20	1.4	1	0 20
87	15	1.4	1	0 15
88	32	1.4	1	0 32
89	24	1.4	1	0 24
90	21	1.4	1	0 21
91	16	1.4	1	0 16
92	15	1.4	1	0 15
93	22	1.4	1	1 21
94	29	1.4	1	0 29
95	32	1.4	1	1 31
96	22	1.4	1	0 22
97	18	1.4	1	0 18
98	24	1.4	1	0 24
99	28	1.4	1	0 28
100	31	1.4	1	0 31
101	23	1.4	1	0 23
102	26	1.4	1	0 26
103	23	1.4	1	0 23
104	23	1.4	1	0 23
105	17	1.4	1	0 17
106	20	1.4	1	1 19
107	21	1.4	1	0 21
108	6	1.4	1	0 6
109	21	1.4	1	0 21
110	20	1.4	1	0 20
111	23	1.4	1	0 23
112	21	1.4	1	0 21
113	24	1.4	1	0 24
114	22	1.4	1	0 22
115	24	1.4	1	0 24
116	28	1.4	1	0 28
117	22	1.4	1	1 21
118	14	1.4	1	0 14
119	16	1.4	1	1 15
120	26	1.4	1	0 26
121	17	1.4	1	0 17
122	17	1.4	1	0 17
123	23	1.4	1	1 22
124	31	1.4	1	1 30
125	25	1.4	1	0 25
126	21	1.4	1	0 21
127	31	1.4	1	0 31
128	26	1.4	1	0 26
129	21	1.4	1	0 21
130	25	1.4	1	1 24
131	23	1.4	1	0 23
132	12	1.4	1	0 12
133	21	1.4	1	0 21
134	17	1.4	1	0 17
135	25	1.4	1	0 25
136	21	1.4	1	1 20
137	19	1.4	1	1 18
138	14	1.4	1	0 14
139	13	1.4	1	0 13
140	14	1.4	1	0 14
141	14	1.4	1	0 14
142	14	1.4	1	0 14
143	18	1.4	1	1 17
144	14	1.4	1	1 13
145	7	1.4	1	0 7
146	34	1.4	1	1 33
147	13	1.4	1	0 13
148	16	1.4	1	0 16
149	23	1.4	1	0 23
150	6	1.4	1	0 6

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz
=============================================
22996933 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3363 (0.01%)
Total number of sequences analysed for the sequence pair length validation: 22996933

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 134588 (0.59%)
