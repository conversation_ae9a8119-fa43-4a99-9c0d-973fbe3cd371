
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 8). Second best hit was Illumina (count: 5)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,701,972
Reads with adapters:                   310,498 (1.5%)
Reads written (passing filters):    20,701,972 (100.0%)

Total basepairs processed: 2,989,421,519 bp
Quality-trimmed:              26,877,718 bp (0.9%)
Total written (filtered):  2,961,267,029 bp (99.1%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 310498 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 29.8%
  C: 20.4%
  G: 19.1%
  T: 30.7%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	237342	323468.3	0	237342
4	50279	80867.1	0	50279
5	14049	20216.8	0	14049
6	3380	5054.2	0	3380
7	737	1263.5	0	737
8	286	315.9	0	286
9	539	79.0	0	39 500
10	359	19.7	1	7 352
11	170	4.9	1	0 170
12	24	1.2	1	0 24
13	16	1.2	1	1 15
14	19	1.2	1	0 19
15	26	1.2	1	0 26
16	33	1.2	1	0 33
17	26	1.2	1	0 26
18	28	1.2	1	0 28
19	28	1.2	1	0 28
20	31	1.2	1	0 31
21	30	1.2	1	0 30
22	25	1.2	1	0 25
23	27	1.2	1	0 27
24	25	1.2	1	0 25
25	24	1.2	1	1 23
26	52	1.2	1	0 52
27	34	1.2	1	0 34
28	44	1.2	1	0 44
29	28	1.2	1	0 28
30	16	1.2	1	0 16
31	34	1.2	1	0 34
32	33	1.2	1	0 33
33	37	1.2	1	0 37
34	35	1.2	1	0 35
35	27	1.2	1	0 27
36	34	1.2	1	0 34
37	27	1.2	1	0 27
38	18	1.2	1	0 18
39	32	1.2	1	0 32
40	22	1.2	1	0 22
41	15	1.2	1	0 15
42	14	1.2	1	0 14
43	28	1.2	1	1 27
44	19	1.2	1	0 19
45	34	1.2	1	0 34
46	21	1.2	1	0 21
47	33	1.2	1	0 33
48	32	1.2	1	0 32
49	30	1.2	1	0 30
50	26	1.2	1	0 26
51	25	1.2	1	0 25
52	34	1.2	1	0 34
53	19	1.2	1	0 19
54	22	1.2	1	1 21
55	21	1.2	1	0 21
56	20	1.2	1	0 20
57	30	1.2	1	0 30
58	19	1.2	1	0 19
59	39	1.2	1	1 38
60	42	1.2	1	0 42
61	25	1.2	1	0 25
62	26	1.2	1	0 26
63	21	1.2	1	0 21
64	19	1.2	1	0 19
65	23	1.2	1	0 23
66	23	1.2	1	0 23
67	26	1.2	1	0 26
68	23	1.2	1	0 23
69	25	1.2	1	0 25
70	22	1.2	1	0 22
71	35	1.2	1	0 35
72	31	1.2	1	0 31
73	30	1.2	1	0 30
74	18	1.2	1	0 18
75	13	1.2	1	0 13
76	23	1.2	1	0 23
77	21	1.2	1	0 21
78	26	1.2	1	0 26
79	30	1.2	1	1 29
80	21	1.2	1	1 20
81	28	1.2	1	0 28
82	26	1.2	1	1 25
83	35	1.2	1	0 35
84	19	1.2	1	0 19
85	18	1.2	1	0 18
86	30	1.2	1	0 30
87	18	1.2	1	0 18
88	28	1.2	1	1 27
89	29	1.2	1	0 29
90	25	1.2	1	1 24
91	15	1.2	1	1 14
92	19	1.2	1	0 19
93	33	1.2	1	0 33
94	24	1.2	1	0 24
95	19	1.2	1	0 19
96	29	1.2	1	0 29
97	21	1.2	1	1 20
98	20	1.2	1	0 20
99	22	1.2	1	0 22
100	25	1.2	1	0 25
101	29	1.2	1	1 28
102	37	1.2	1	1 36
103	23	1.2	1	0 23
104	17	1.2	1	0 17
105	18	1.2	1	0 18
106	16	1.2	1	0 16
107	18	1.2	1	0 18
108	18	1.2	1	1 17
109	25	1.2	1	3 22
110	23	1.2	1	0 23
111	24	1.2	1	0 24
112	17	1.2	1	1 16
113	10	1.2	1	1 9
114	23	1.2	1	0 23
115	25	1.2	1	1 24
116	25	1.2	1	0 25
117	24	1.2	1	2 22
118	28	1.2	1	0 28
119	15	1.2	1	0 15
120	27	1.2	1	0 27
121	15	1.2	1	2 13
122	17	1.2	1	0 17
123	23	1.2	1	4 19
124	20	1.2	1	2 18
125	17	1.2	1	0 17
126	30	1.2	1	0 30
127	22	1.2	1	0 22
128	27	1.2	1	0 27
129	16	1.2	1	0 16
130	24	1.2	1	0 24
131	10	1.2	1	0 10
132	26	1.2	1	0 26
133	16	1.2	1	0 16
134	17	1.2	1	0 17
135	23	1.2	1	1 22
136	24	1.2	1	0 24
137	17	1.2	1	0 17
138	14	1.2	1	1 13
139	15	1.2	1	0 15
140	11	1.2	1	0 11
141	14	1.2	1	0 14
142	36	1.2	1	0 36
143	19	1.2	1	0 19
144	28	1.2	1	1 27
145	9	1.2	1	1 8
146	39	1.2	1	3 36
147	25	1.2	1	2 23
148	9	1.2	1	0 9
149	24	1.2	1	0 24
150	6	1.2	1	1 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz
=============================================
20701972 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3077 (0.01%)
Total number of sequences analysed for the sequence pair length validation: 20701972

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 124225 (0.60%)
