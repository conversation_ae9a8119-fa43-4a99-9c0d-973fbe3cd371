
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 17). Second best hit was Illumina (count: 11)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,996,933
Reads with adapters:                   608,298 (2.6%)
Reads written (passing filters):    22,996,933 (100.0%)

Total basepairs processed: 3,312,601,285 bp
Quality-trimmed:              23,405,018 bp (0.7%)
Total written (filtered):  3,286,570,668 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 608298 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.1%
  C: 18.2%
  G: 34.6%
  T: 24.1%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	471801	359327.1	0	471801
4	82215	89831.8	0	82215
5	36161	22457.9	0	36161
6	6197	5614.5	0	6197
7	1004	1403.6	0	1004
8	244	350.9	0	244
9	579	87.7	0	85 494
10	993	21.9	1	32 961
11	459	5.5	1	6 453
12	156	1.4	1	5 151
13	104	1.4	1	5 99
14	55	1.4	1	0 55
15	65	1.4	1	1 64
16	73	1.4	1	3 70
17	55	1.4	1	4 51
18	59	1.4	1	3 56
19	49	1.4	1	1 48
20	80	1.4	1	0 80
21	106	1.4	1	1 105
22	51	1.4	1	1 50
23	73	1.4	1	3 70
24	82	1.4	1	2 80
25	82	1.4	1	1 81
26	112	1.4	1	12 100
27	109	1.4	1	7 102
28	98	1.4	1	4 94
29	67	1.4	1	3 64
30	97	1.4	1	3 94
31	81	1.4	1	1 80
32	92	1.4	1	3 89
33	88	1.4	1	1 87
34	55	1.4	1	1 54
35	68	1.4	1	1 67
36	76	1.4	1	2 74
37	79	1.4	1	0 79
38	40	1.4	1	3 37
39	92	1.4	1	2 90
40	45	1.4	1	3 42
41	58	1.4	1	4 54
42	56	1.4	1	1 55
43	76	1.4	1	4 72
44	51	1.4	1	4 47
45	51	1.4	1	3 48
46	71	1.4	1	1 70
47	65	1.4	1	5 60
48	74	1.4	1	3 71
49	107	1.4	1	1 106
50	89	1.4	1	0 89
51	115	1.4	1	0 115
52	67	1.4	1	0 67
53	155	1.4	1	0 155
54	150	1.4	1	1 149
55	89	1.4	1	1 88
56	113	1.4	1	3 110
57	108	1.4	1	4 104
58	51	1.4	1	2 49
59	57	1.4	1	5 52
60	66	1.4	1	0 66
61	70	1.4	1	0 70
62	42	1.4	1	1 41
63	58	1.4	1	0 58
64	28	1.4	1	0 28
65	67	1.4	1	1 66
66	70	1.4	1	1 69
67	75	1.4	1	1 74
68	61	1.4	1	0 61
69	96	1.4	1	3 93
70	36	1.4	1	3 33
71	48	1.4	1	3 45
72	78	1.4	1	0 78
73	57	1.4	1	1 56
74	67	1.4	1	2 65
75	69	1.4	1	3 66
76	39	1.4	1	4 35
77	67	1.4	1	0 67
78	62	1.4	1	3 59
79	69	1.4	1	4 65
80	40	1.4	1	1 39
81	53	1.4	1	1 52
82	46	1.4	1	0 46
83	36	1.4	1	1 35
84	50	1.4	1	0 50
85	48	1.4	1	0 48
86	74	1.4	1	0 74
87	31	1.4	1	0 31
88	56	1.4	1	7 49
89	52	1.4	1	5 47
90	24	1.4	1	0 24
91	41	1.4	1	2 39
92	48	1.4	1	1 47
93	49	1.4	1	5 44
94	52	1.4	1	1 51
95	56	1.4	1	2 54
96	79	1.4	1	1 78
97	55	1.4	1	0 55
98	42	1.4	1	0 42
99	47	1.4	1	1 46
100	42	1.4	1	1 41
101	106	1.4	1	0 106
102	87	1.4	1	2 85
103	53	1.4	1	0 53
104	44	1.4	1	5 39
105	42	1.4	1	0 42
106	40	1.4	1	1 39
107	43	1.4	1	0 43
108	35	1.4	1	0 35
109	42	1.4	1	5 37
110	48	1.4	1	1 47
111	49	1.4	1	0 49
112	61	1.4	1	0 61
113	29	1.4	1	1 28
114	42	1.4	1	1 41
115	37	1.4	1	0 37
116	40	1.4	1	3 37
117	49	1.4	1	0 49
118	56	1.4	1	0 56
119	39	1.4	1	0 39
120	59	1.4	1	1 58
121	36	1.4	1	0 36
122	53	1.4	1	2 51
123	54	1.4	1	2 52
124	37	1.4	1	0 37
125	33	1.4	1	0 33
126	52	1.4	1	1 51
127	46	1.4	1	1 45
128	31	1.4	1	1 30
129	50	1.4	1	4 46
130	24	1.4	1	0 24
131	30	1.4	1	4 26
132	25	1.4	1	2 23
133	65	1.4	1	1 64
134	48	1.4	1	3 45
135	37	1.4	1	0 37
136	33	1.4	1	0 33
137	35	1.4	1	0 35
138	39	1.4	1	2 37
139	47	1.4	1	1 46
140	36	1.4	1	3 33
141	52	1.4	1	1 51
142	22	1.4	1	0 22
143	69	1.4	1	1 68
144	149	1.4	1	3 146
145	39	1.4	1	2 37
146	18	1.4	1	0 18
147	39	1.4	1	0 39
148	80	1.4	1	1 79
149	167	1.4	1	4 163
150	60	1.4	1	0 60

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz
=============================================
22996933 sequences processed in total

