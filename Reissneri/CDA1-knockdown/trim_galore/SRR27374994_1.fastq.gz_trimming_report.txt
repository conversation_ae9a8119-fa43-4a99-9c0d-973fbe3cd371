
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 9). Second best hit was Illumina (count: 8)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,556,950
Reads with adapters:                   550,952 (2.7%)
Reads written (passing filters):    20,556,950 (100.0%)

Total basepairs processed: 2,979,370,570 bp
Quality-trimmed:              20,663,388 bp (0.7%)
Total written (filtered):  2,956,338,984 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 550952 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.4%
  C: 17.9%
  G: 34.7%
  T: 24.0%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	426625	321202.3	0	426625
4	74919	80300.6	0	74919
5	33578	20075.1	0	33578
6	5432	5018.8	0	5432
7	901	1254.7	0	901
8	202	313.7	0	202
9	491	78.4	0	71 420
10	817	19.6	1	27 790
11	375	4.9	1	5 370
12	126	1.2	1	4 122
13	74	1.2	1	2 72
14	69	1.2	1	1 68
15	47	1.2	1	1 46
16	66	1.2	1	0 66
17	47	1.2	1	1 46
18	65	1.2	1	2 63
19	52	1.2	1	0 52
20	101	1.2	1	2 99
21	80	1.2	1	0 80
22	45	1.2	1	0 45
23	68	1.2	1	3 65
24	52	1.2	1	1 51
25	79	1.2	1	2 77
26	75	1.2	1	6 69
27	96	1.2	1	6 90
28	67	1.2	1	4 63
29	59	1.2	1	1 58
30	99	1.2	1	2 97
31	70	1.2	1	5 65
32	94	1.2	1	5 89
33	82	1.2	1	5 77
34	61	1.2	1	1 60
35	81	1.2	1	3 78
36	60	1.2	1	1 59
37	61	1.2	1	0 61
38	30	1.2	1	0 30
39	72	1.2	1	0 72
40	47	1.2	1	2 45
41	73	1.2	1	4 69
42	51	1.2	1	9 42
43	48	1.2	1	3 45
44	40	1.2	1	1 39
45	36	1.2	1	2 34
46	71	1.2	1	2 69
47	55	1.2	1	9 46
48	68	1.2	1	0 68
49	97	1.2	1	1 96
50	74	1.2	1	0 74
51	83	1.2	1	1 82
52	70	1.2	1	1 69
53	118	1.2	1	0 118
54	131	1.2	1	0 131
55	85	1.2	1	3 82
56	106	1.2	1	7 99
57	88	1.2	1	5 83
58	47	1.2	1	6 41
59	63	1.2	1	4 59
60	43	1.2	1	1 42
61	43	1.2	1	0 43
62	54	1.2	1	1 53
63	29	1.2	1	0 29
64	21	1.2	1	0 21
65	48	1.2	1	0 48
66	68	1.2	1	0 68
67	52	1.2	1	0 52
68	61	1.2	1	1 60
69	64	1.2	1	1 63
70	43	1.2	1	3 40
71	42	1.2	1	3 39
72	55	1.2	1	3 52
73	38	1.2	1	3 35
74	74	1.2	1	2 72
75	69	1.2	1	3 66
76	47	1.2	1	0 47
77	57	1.2	1	1 56
78	36	1.2	1	0 36
79	65	1.2	1	1 64
80	40	1.2	1	1 39
81	37	1.2	1	2 35
82	34	1.2	1	2 32
83	43	1.2	1	0 43
84	43	1.2	1	0 43
85	39	1.2	1	0 39
86	70	1.2	1	1 69
87	39	1.2	1	2 37
88	38	1.2	1	8 30
89	59	1.2	1	5 54
90	27	1.2	1	0 27
91	27	1.2	1	0 27
92	45	1.2	1	4 41
93	40	1.2	1	1 39
94	39	1.2	1	0 39
95	40	1.2	1	2 38
96	48	1.2	1	0 48
97	52	1.2	1	2 50
98	32	1.2	1	1 31
99	38	1.2	1	0 38
100	30	1.2	1	0 30
101	97	1.2	1	0 97
102	60	1.2	1	1 59
103	53	1.2	1	0 53
104	50	1.2	1	5 45
105	38	1.2	1	0 38
106	50	1.2	1	2 48
107	43	1.2	1	1 42
108	34	1.2	1	0 34
109	35	1.2	1	1 34
110	45	1.2	1	0 45
111	52	1.2	1	1 51
112	38	1.2	1	1 37
113	35	1.2	1	1 34
114	24	1.2	1	0 24
115	38	1.2	1	0 38
116	42	1.2	1	6 36
117	46	1.2	1	0 46
118	66	1.2	1	1 65
119	48	1.2	1	1 47
120	49	1.2	1	1 48
121	48	1.2	1	0 48
122	43	1.2	1	0 43
123	39	1.2	1	2 37
124	39	1.2	1	1 38
125	31	1.2	1	3 28
126	40	1.2	1	0 40
127	36	1.2	1	1 35
128	25	1.2	1	0 25
129	39	1.2	1	2 37
130	24	1.2	1	1 23
131	31	1.2	1	1 30
132	25	1.2	1	0 25
133	40	1.2	1	0 40
134	44	1.2	1	3 41
135	38	1.2	1	1 37
136	35	1.2	1	1 34
137	30	1.2	1	2 28
138	36	1.2	1	3 33
139	45	1.2	1	0 45
140	27	1.2	1	0 27
141	57	1.2	1	1 56
142	24	1.2	1	0 24
143	61	1.2	1	1 60
144	164	1.2	1	1 163
145	35	1.2	1	1 34
146	16	1.2	1	1 15
147	28	1.2	1	1 27
148	61	1.2	1	1 60
149	144	1.2	1	0 144
150	66	1.2	1	1 65

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz
=============================================
20556950 sequences processed in total

