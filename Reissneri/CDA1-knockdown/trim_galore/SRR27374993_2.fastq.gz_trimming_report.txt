
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 12). Second best hit was Illumina (count: 7)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,178,360
Reads with adapters:                   320,274 (1.4%)
Reads written (passing filters):    22,178,360 (100.0%)

Total basepairs processed: 3,203,587,003 bp
Quality-trimmed:              29,373,254 bp (0.9%)
Total written (filtered):  3,172,922,832 bp (99.0%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 320274 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 30.0%
  C: 20.0%
  G: 18.6%
  T: 31.4%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	242598	346536.9	0	242598
4	51756	86634.2	0	51756
5	17042	21658.6	0	17042
6	3742	5414.6	0	3742
7	691	1353.7	0	691
8	336	338.4	0	336
9	577	84.6	0	32 545
10	371	21.2	1	13 358
11	169	5.3	1	3 166
12	29	1.3	1	0 29
13	20	1.3	1	0 20
14	17	1.3	1	0 17
15	26	1.3	1	0 26
16	32	1.3	1	0 32
17	11	1.3	1	0 11
18	17	1.3	1	0 17
19	21	1.3	1	0 21
20	16	1.3	1	0 16
21	19	1.3	1	0 19
22	32	1.3	1	0 32
23	35	1.3	1	0 35
24	24	1.3	1	0 24
25	14	1.3	1	0 14
26	39	1.3	1	0 39
27	31	1.3	1	0 31
28	44	1.3	1	3 41
29	40	1.3	1	1 39
30	24	1.3	1	0 24
31	26	1.3	1	0 26
32	29	1.3	1	0 29
33	22	1.3	1	0 22
34	30	1.3	1	0 30
35	21	1.3	1	0 21
36	22	1.3	1	1 21
37	27	1.3	1	0 27
38	18	1.3	1	1 17
39	19	1.3	1	0 19
40	15	1.3	1	0 15
41	29	1.3	1	0 29
42	19	1.3	1	0 19
43	25	1.3	1	0 25
44	18	1.3	1	0 18
45	24	1.3	1	0 24
46	24	1.3	1	0 24
47	23	1.3	1	0 23
48	27	1.3	1	0 27
49	24	1.3	1	0 24
50	19	1.3	1	0 19
51	20	1.3	1	0 20
52	31	1.3	1	0 31
53	11	1.3	1	0 11
54	25	1.3	1	0 25
55	17	1.3	1	1 16
56	17	1.3	1	0 17
57	23	1.3	1	0 23
58	19	1.3	1	0 19
59	22	1.3	1	0 22
60	31	1.3	1	0 31
61	24	1.3	1	0 24
62	15	1.3	1	0 15
63	27	1.3	1	0 27
64	19	1.3	1	0 19
65	20	1.3	1	0 20
66	27	1.3	1	0 27
67	32	1.3	1	0 32
68	20	1.3	1	0 20
69	18	1.3	1	0 18
70	20	1.3	1	0 20
71	23	1.3	1	0 23
72	28	1.3	1	0 28
73	24	1.3	1	0 24
74	17	1.3	1	0 17
75	27	1.3	1	1 26
76	30	1.3	1	0 30
77	16	1.3	1	0 16
78	26	1.3	1	0 26
79	25	1.3	1	0 25
80	27	1.3	1	0 27
81	20	1.3	1	0 20
82	17	1.3	1	0 17
83	24	1.3	1	1 23
84	24	1.3	1	0 24
85	15	1.3	1	0 15
86	16	1.3	1	0 16
87	17	1.3	1	1 16
88	27	1.3	1	0 27
89	13	1.3	1	0 13
90	11	1.3	1	0 11
91	28	1.3	1	0 28
92	13	1.3	1	0 13
93	21	1.3	1	0 21
94	21	1.3	1	0 21
95	24	1.3	1	1 23
96	11	1.3	1	0 11
97	22	1.3	1	1 21
98	28	1.3	1	0 28
99	21	1.3	1	0 21
100	29	1.3	1	1 28
101	26	1.3	1	0 26
102	22	1.3	1	0 22
103	19	1.3	1	0 19
104	16	1.3	1	0 16
105	15	1.3	1	0 15
106	17	1.3	1	0 17
107	16	1.3	1	1 15
108	18	1.3	1	0 18
109	17	1.3	1	0 17
110	29	1.3	1	0 29
111	12	1.3	1	2 10
112	26	1.3	1	0 26
113	15	1.3	1	0 15
114	13	1.3	1	1 12
115	26	1.3	1	1 25
116	21	1.3	1	0 21
117	25	1.3	1	0 25
118	15	1.3	1	0 15
119	19	1.3	1	0 19
120	26	1.3	1	0 26
121	13	1.3	1	0 13
122	20	1.3	1	0 20
123	16	1.3	1	0 16
124	23	1.3	1	1 22
125	26	1.3	1	0 26
126	22	1.3	1	0 22
127	34	1.3	1	0 34
128	24	1.3	1	0 24
129	17	1.3	1	0 17
130	15	1.3	1	0 15
131	22	1.3	1	1 21
132	18	1.3	1	0 18
133	17	1.3	1	0 17
134	22	1.3	1	1 21
135	32	1.3	1	2 30
136	27	1.3	1	1 26
137	15	1.3	1	0 15
138	13	1.3	1	0 13
139	11	1.3	1	0 11
140	18	1.3	1	0 18
141	9	1.3	1	0 9
142	23	1.3	1	1 22
143	17	1.3	1	0 17
144	9	1.3	1	0 9
145	7	1.3	1	0 7
146	44	1.3	1	2 42
147	14	1.3	1	0 14
148	7	1.3	1	0 7
149	19	1.3	1	0 19
150	10	1.3	1	0 10

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz
=============================================
22178360 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3142 (0.01%)
Total number of sequences analysed for the sequence pair length validation: 22178360

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 132543 (0.60%)
