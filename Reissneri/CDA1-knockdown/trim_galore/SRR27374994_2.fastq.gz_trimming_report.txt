
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 9). Second best hit was Illumina (count: 8)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,556,950
Reads with adapters:                   295,227 (1.4%)
Reads written (passing filters):    20,556,950 (100.0%)

Total basepairs processed: 2,974,361,272 bp
Quality-trimmed:              26,447,542 bp (0.9%)
Total written (filtered):  2,946,732,875 bp (99.1%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 295227 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 29.8%
  C: 20.3%
  G: 18.4%
  T: 31.5%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	223515	321202.3	0	223515
4	47734	80300.6	0	47734
5	15941	20075.1	0	15941
6	3514	5018.8	0	3514
7	575	1254.7	0	575
8	281	313.7	0	281
9	519	78.4	0	26 493
10	353	19.6	1	9 344
11	163	4.9	1	2 161
12	17	1.2	1	0 17
13	11	1.2	1	0 11
14	11	1.2	1	0 11
15	19	1.2	1	0 19
16	24	1.2	1	0 24
17	22	1.2	1	0 22
18	19	1.2	1	1 18
19	22	1.2	1	0 22
20	23	1.2	1	1 22
21	16	1.2	1	0 16
22	23	1.2	1	0 23
23	17	1.2	1	0 17
24	23	1.2	1	1 22
25	21	1.2	1	1 20
26	42	1.2	1	0 42
27	27	1.2	1	0 27
28	40	1.2	1	0 40
29	22	1.2	1	0 22
30	16	1.2	1	0 16
31	8	1.2	1	0 8
32	41	1.2	1	0 41
33	25	1.2	1	0 25
34	26	1.2	1	0 26
35	22	1.2	1	0 22
36	20	1.2	1	0 20
37	25	1.2	1	0 25
38	21	1.2	1	1 20
39	19	1.2	1	0 19
40	17	1.2	1	0 17
41	19	1.2	1	0 19
42	14	1.2	1	0 14
43	15	1.2	1	0 15
44	14	1.2	1	0 14
45	18	1.2	1	0 18
46	15	1.2	1	0 15
47	16	1.2	1	0 16
48	20	1.2	1	0 20
49	21	1.2	1	0 21
50	20	1.2	1	0 20
51	14	1.2	1	0 14
52	32	1.2	1	1 31
53	23	1.2	1	0 23
54	13	1.2	1	0 13
55	21	1.2	1	0 21
56	16	1.2	1	0 16
57	26	1.2	1	0 26
58	17	1.2	1	0 17
59	14	1.2	1	1 13
60	30	1.2	1	0 30
61	20	1.2	1	0 20
62	20	1.2	1	0 20
63	19	1.2	1	0 19
64	25	1.2	1	0 25
65	30	1.2	1	0 30
66	27	1.2	1	0 27
67	28	1.2	1	0 28
68	13	1.2	1	0 13
69	17	1.2	1	0 17
70	11	1.2	1	0 11
71	28	1.2	1	1 27
72	16	1.2	1	0 16
73	26	1.2	1	0 26
74	24	1.2	1	0 24
75	22	1.2	1	0 22
76	19	1.2	1	0 19
77	11	1.2	1	0 11
78	22	1.2	1	0 22
79	18	1.2	1	0 18
80	10	1.2	1	0 10
81	17	1.2	1	1 16
82	21	1.2	1	0 21
83	17	1.2	1	0 17
84	16	1.2	1	0 16
85	16	1.2	1	0 16
86	19	1.2	1	0 19
87	22	1.2	1	0 22
88	24	1.2	1	0 24
89	19	1.2	1	0 19
90	17	1.2	1	0 17
91	15	1.2	1	0 15
92	22	1.2	1	0 22
93	14	1.2	1	0 14
94	23	1.2	1	0 23
95	15	1.2	1	1 14
96	20	1.2	1	0 20
97	18	1.2	1	0 18
98	19	1.2	1	0 19
99	21	1.2	1	0 21
100	23	1.2	1	0 23
101	30	1.2	1	2 28
102	21	1.2	1	1 20
103	15	1.2	1	0 15
104	18	1.2	1	0 18
105	20	1.2	1	1 19
106	24	1.2	1	0 24
107	15	1.2	1	0 15
108	19	1.2	1	0 19
109	13	1.2	1	0 13
110	16	1.2	1	0 16
111	9	1.2	1	0 9
112	18	1.2	1	0 18
113	15	1.2	1	0 15
114	19	1.2	1	0 19
115	15	1.2	1	1 14
116	14	1.2	1	0 14
117	16	1.2	1	0 16
118	14	1.2	1	0 14
119	15	1.2	1	0 15
120	20	1.2	1	0 20
121	16	1.2	1	1 15
122	15	1.2	1	2 13
123	13	1.2	1	0 13
124	19	1.2	1	1 18
125	21	1.2	1	0 21
126	18	1.2	1	1 17
127	27	1.2	1	0 27
128	21	1.2	1	0 21
129	18	1.2	1	0 18
130	23	1.2	1	0 23
131	21	1.2	1	0 21
132	12	1.2	1	0 12
133	9	1.2	1	0 9
134	12	1.2	1	1 11
135	24	1.2	1	1 23
136	19	1.2	1	0 19
137	16	1.2	1	2 14
138	9	1.2	1	0 9
139	21	1.2	1	1 20
140	10	1.2	1	0 10
141	10	1.2	1	0 10
142	14	1.2	1	0 14
143	9	1.2	1	0 9
144	16	1.2	1	0 16
145	10	1.2	1	0 10
146	29	1.2	1	1 28
147	15	1.2	1	1 14
148	7	1.2	1	0 7
149	21	1.2	1	0 21
150	10	1.2	1	0 10

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz
=============================================
20556950 sequences processed in total

Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3133 (0.02%)
Total number of sequences analysed for the sequence pair length validation: 20556950

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 119938 (0.58%)
