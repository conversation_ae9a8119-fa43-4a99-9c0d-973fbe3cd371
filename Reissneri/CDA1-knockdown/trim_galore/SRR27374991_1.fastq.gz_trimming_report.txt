
SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Using smallRNA adapter for trimming (count: 8). Second best hit was Illumina (count: 5)
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file will be GZIP compressed


This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,701,972
Reads with adapters:                   532,032 (2.6%)
Reads written (passing filters):    20,701,972 (100.0%)

Total basepairs processed: 2,994,272,811 bp
Quality-trimmed:              22,024,261 bp (0.7%)
Total written (filtered):  2,969,828,396 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 532032 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.5%
  C: 20.3%
  G: 33.4%
  T: 24.7%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	409513	323468.3	0	409513
4	76411	80867.1	0	76411
5	28130	20216.8	0	28130
6	5294	5054.2	0	5294
7	1014	1263.5	0	1014
8	292	315.9	0	292
9	483	79.0	0	120 363
10	1125	19.7	1	34 1091
11	448	4.9	1	5 443
12	174	1.2	1	1 173
13	93	1.2	1	0 93
14	70	1.2	1	1 69
15	75	1.2	1	1 74
16	82	1.2	1	0 82
17	55	1.2	1	3 52
18	78	1.2	1	2 76
19	68	1.2	1	0 68
20	88	1.2	1	0 88
21	122	1.2	1	1 121
22	57	1.2	1	1 56
23	94	1.2	1	1 93
24	52	1.2	1	0 52
25	114	1.2	1	2 112
26	89	1.2	1	4 85
27	141	1.2	1	4 137
28	115	1.2	1	5 110
29	57	1.2	1	0 57
30	150	1.2	1	2 148
31	83	1.2	1	2 81
32	112	1.2	1	2 110
33	122	1.2	1	1 121
34	68	1.2	1	0 68
35	98	1.2	1	2 96
36	70	1.2	1	0 70
37	80	1.2	1	0 80
38	53	1.2	1	0 53
39	99	1.2	1	2 97
40	52	1.2	1	6 46
41	48	1.2	1	0 48
42	44	1.2	1	5 39
43	57	1.2	1	0 57
44	39	1.2	1	2 37
45	46	1.2	1	2 44
46	65	1.2	1	1 64
47	51	1.2	1	1 50
48	85	1.2	1	1 84
49	129	1.2	1	0 129
50	98	1.2	1	0 98
51	100	1.2	1	0 100
52	76	1.2	1	0 76
53	168	1.2	1	0 168
54	176	1.2	1	0 176
55	100	1.2	1	1 99
56	103	1.2	1	4 99
57	123	1.2	1	6 117
58	49	1.2	1	3 46
59	74	1.2	1	4 70
60	72	1.2	1	0 72
61	63	1.2	1	2 61
62	75	1.2	1	2 73
63	47	1.2	1	0 47
64	45	1.2	1	1 44
65	51	1.2	1	0 51
66	94	1.2	1	2 92
67	55	1.2	1	0 55
68	74	1.2	1	0 74
69	105	1.2	1	0 105
70	41	1.2	1	0 41
71	46	1.2	1	2 44
72	62	1.2	1	0 62
73	76	1.2	1	4 72
74	78	1.2	1	2 76
75	82	1.2	1	5 77
76	53	1.2	1	3 50
77	70	1.2	1	0 70
78	59	1.2	1	0 59
79	67	1.2	1	2 65
80	44	1.2	1	1 43
81	45	1.2	1	0 45
82	40	1.2	1	0 40
83	47	1.2	1	0 47
84	55	1.2	1	0 55
85	49	1.2	1	1 48
86	80	1.2	1	0 80
87	39	1.2	1	0 39
88	52	1.2	1	3 49
89	71	1.2	1	2 69
90	43	1.2	1	1 42
91	27	1.2	1	1 26
92	49	1.2	1	1 48
93	43	1.2	1	2 41
94	50	1.2	1	1 49
95	48	1.2	1	2 46
96	74	1.2	1	0 74
97	49	1.2	1	1 48
98	30	1.2	1	0 30
99	39	1.2	1	1 38
100	63	1.2	1	0 63
101	123	1.2	1	1 122
102	107	1.2	1	0 107
103	79	1.2	1	0 79
104	38	1.2	1	2 36
105	49	1.2	1	0 49
106	36	1.2	1	2 34
107	42	1.2	1	1 41
108	39	1.2	1	1 38
109	39	1.2	1	1 38
110	32	1.2	1	1 31
111	75	1.2	1	1 74
112	52	1.2	1	2 50
113	26	1.2	1	1 25
114	31	1.2	1	0 31
115	31	1.2	1	0 31
116	43	1.2	1	3 40
117	52	1.2	1	0 52
118	71	1.2	1	5 66
119	31	1.2	1	0 31
120	24	1.2	1	0 24
121	57	1.2	1	0 57
122	46	1.2	1	0 46
123	43	1.2	1	0 43
124	34	1.2	1	0 34
125	28	1.2	1	0 28
126	42	1.2	1	0 42
127	29	1.2	1	0 29
128	34	1.2	1	1 33
129	42	1.2	1	1 41
130	33	1.2	1	0 33
131	42	1.2	1	2 40
132	39	1.2	1	0 39
133	60	1.2	1	1 59
134	31	1.2	1	2 29
135	52	1.2	1	1 51
136	30	1.2	1	0 30
137	55	1.2	1	0 55
138	31	1.2	1	1 30
139	41	1.2	1	1 40
140	26	1.2	1	0 26
141	72	1.2	1	1 71
142	18	1.2	1	0 18
143	66	1.2	1	0 66
144	239	1.2	1	2 237
145	52	1.2	1	1 51
146	17	1.2	1	0 17
147	47	1.2	1	0 47
148	121	1.2	1	2 119
149	199	1.2	1	2 197
150	82	1.2	1	2 80

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz
=============================================
20701972 sequences processed in total

