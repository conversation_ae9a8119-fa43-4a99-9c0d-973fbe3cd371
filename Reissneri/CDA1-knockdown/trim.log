检测到以下样本：
SRR27374989
SRR27374990
SRR27374991
SRR27374992
SRR27374993
SRR27374994
正在处理样本 SRR27374989 ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	10	AGATCGGAAGAGC	1000000	0.00
smallRNA	6	TGGAATTCTCGG	1000000	0.00
Nextera	5	CTGTCTCTTATA	1000000	0.00
Using Illumina adapter for trimming (count: 10). Second best hit was smallRNA (count: 6)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374989_1.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to SRR27374989_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a AGATCGGAAGAGC /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,879,266
Reads with adapters:                   402,396 (1.8%)
Reads written (passing filters):    22,879,266 (100.0%)

Total basepairs processed: 3,300,752,404 bp
Quality-trimmed:              23,747,533 bp (0.7%)
Total written (filtered):  3,275,318,117 bp (99.2%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 402396 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 24.1%
  C: 28.5%
  G: 28.8%
  T: 18.6%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
3	322204	357488.5	0	322204
4	68842	89372.1	0	68842
5	3305	22343.0	0	3305
6	808	5585.8	0	808
7	322	1396.4	0	322
8	31	349.1	0	31
9	447	87.3	0	34 413
10	251	21.8	1	25 226
11	225	5.5	1	2 223
12	204	1.4	1	10 194
13	126	0.3	1	39 87
14	71	0.3	1	4 67
15	100	0.3	1	0 100
16	92	0.3	1	0 92
17	100	0.3	1	2 98
18	102	0.3	1	4 98
19	90	0.3	1	4 86
20	109	0.3	1	5 104
21	68	0.3	1	2 66
22	73	0.3	1	3 70
23	53	0.3	1	3 50
24	75	0.3	1	7 68
25	91	0.3	1	5 86
26	85	0.3	1	8 77
27	95	0.3	1	7 88
28	69	0.3	1	8 61
29	64	0.3	1	3 61
30	71	0.3	1	4 67
31	41	0.3	1	5 36
32	81	0.3	1	5 76
33	50	0.3	1	4 46
34	36	0.3	1	3 33
35	30	0.3	1	5 25
36	33	0.3	1	2 31
37	38	0.3	1	2 36
38	33	0.3	1	2 31
39	101	0.3	1	1 100
40	50	0.3	1	0 50
41	42	0.3	1	3 39
42	42	0.3	1	4 38
43	40	0.3	1	3 37
44	40	0.3	1	2 38
45	27	0.3	1	3 24
46	36	0.3	1	0 36
47	20	0.3	1	2 18
48	24	0.3	1	1 23
49	21	0.3	1	2 19
50	40	0.3	1	1 39
51	49	0.3	1	1 48
52	67	0.3	1	0 67
53	55	0.3	1	1 54
54	35	0.3	1	2 33
55	42	0.3	1	0 42
56	23	0.3	1	0 23
57	21	0.3	1	0 21
58	23	0.3	1	0 23
59	14	0.3	1	2 12
60	22	0.3	1	0 22
61	18	0.3	1	0 18
62	28	0.3	1	0 28
63	40	0.3	1	0 40
64	50	0.3	1	0 50
65	24	0.3	1	0 24
66	28	0.3	1	2 26
67	36	0.3	1	0 36
68	33	0.3	1	0 33
69	31	0.3	1	0 31
70	28	0.3	1	2 26
71	22	0.3	1	0 22
72	24	0.3	1	0 24
73	28	0.3	1	0 28
74	17	0.3	1	0 17
75	18	0.3	1	0 18
76	29	0.3	1	0 29
77	32	0.3	1	0 32
78	22	0.3	1	1 21
79	23	0.3	1	0 23
80	33	0.3	1	0 33
81	33	0.3	1	0 33
82	44	0.3	1	0 44
83	26	0.3	1	0 26
84	38	0.3	1	0 38
85	30	0.3	1	0 30
86	31	0.3	1	0 31
87	30	0.3	1	0 30
88	36	0.3	1	0 36
89	37	0.3	1	0 37
90	41	0.3	1	0 41
91	37	0.3	1	0 37
92	41	0.3	1	0 41
93	27	0.3	1	0 27
94	23	0.3	1	0 23
95	28	0.3	1	0 28
96	35	0.3	1	0 35
97	30	0.3	1	0 30
98	44	0.3	1	0 44
99	25	0.3	1	0 25
100	64	0.3	1	0 64
101	52	0.3	1	0 52
102	80	0.3	1	0 80
103	40	0.3	1	0 40
104	24	0.3	1	0 24
105	40	0.3	1	0 40
106	66	0.3	1	0 66
107	30	0.3	1	0 30
108	24	0.3	1	0 24
109	29	0.3	1	0 29
110	50	0.3	1	0 50
111	37	0.3	1	0 37
112	43	0.3	1	0 43
113	45	0.3	1	0 45
114	55	0.3	1	0 55
115	35	0.3	1	0 35
116	24	0.3	1	0 24
117	30	0.3	1	0 30
118	26	0.3	1	0 26
119	36	0.3	1	0 36
120	22	0.3	1	2 20
121	15	0.3	1	1 14
122	25	0.3	1	1 24
123	26	0.3	1	1 25
124	33	0.3	1	0 33
125	25	0.3	1	0 25
126	37	0.3	1	1 36
127	45	0.3	1	0 45
128	42	0.3	1	0 42
129	35	0.3	1	0 35
130	30	0.3	1	0 30
131	54	0.3	1	1 53
132	21	0.3	1	0 21
133	52	0.3	1	0 52
134	16	0.3	1	0 16
135	17	0.3	1	1 16
136	30	0.3	1	0 30
137	24	0.3	1	0 24
138	26	0.3	1	0 26
139	25	0.3	1	0 25
140	17	0.3	1	0 17
141	28	0.3	1	0 28
142	12	0.3	1	0 12
143	12	0.3	1	0 12
144	26	0.3	1	0 26
145	29	0.3	1	0 29
146	20	0.3	1	0 20
147	46	0.3	1	0 46
148	8	0.3	1	0 8
149	9	0.3	1	0 9
150	210	0.3	1	0 210

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_1.fastq.gz
=============================================
22879266 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374989_2.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to SRR27374989_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a AGATCGGAAGAGC /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,879,266
Reads with adapters:                   464,675 (2.0%)
Reads written (passing filters):    22,879,266 (100.0%)

Total basepairs processed: 3,295,765,805 bp
Quality-trimmed:              30,468,890 bp (0.9%)
Total written (filtered):  3,263,494,129 bp (99.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 464675 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 30.6%
  C: 28.9%
  G: 29.3%
  T: 11.2%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	356507	357488.5	0	356507
4	71267	89372.1	0	71267
5	19910	22343.0	0	19910
6	7362	5585.8	0	7362
7	1870	1396.4	0	1870
8	583	349.1	0	583
9	1636	87.3	0	342 1294
10	715	21.8	1	178 537
11	392	5.5	1	110 282
12	230	1.4	1	122 108
13	104	0.3	1	64 40
14	122	0.3	1	78 44
15	114	0.3	1	84 30
16	123	0.3	1	88 35
17	130	0.3	1	96 34
18	137	0.3	1	101 36
19	195	0.3	1	146 49
20	85	0.3	1	54 31
21	57	0.3	1	30 27
22	49	0.3	1	20 29
23	44	0.3	1	24 20
24	84	0.3	1	60 24
25	46	0.3	1	20 26
26	90	0.3	1	39 51
27	104	0.3	1	66 38
28	47	0.3	1	23 24
29	42	0.3	1	20 22
30	45	0.3	1	32 13
31	43	0.3	1	19 24
32	33	0.3	1	18 15
33	34	0.3	1	16 18
34	33	0.3	1	19 14
35	46	0.3	1	17 29
36	26	0.3	1	7 19
37	20	0.3	1	7 13
38	32	0.3	1	9 23
39	37	0.3	1	15 22
40	23	0.3	1	8 15
41	22	0.3	1	6 16
42	9	0.3	1	0 9
43	33	0.3	1	10 23
44	27	0.3	1	7 20
45	21	0.3	1	8 13
46	14	0.3	1	1 13
47	17	0.3	1	5 12
48	22	0.3	1	9 13
49	17	0.3	1	1 16
50	39	0.3	1	2 37
51	24	0.3	1	2 22
52	18	0.3	1	7 11
53	12	0.3	1	0 12
54	21	0.3	1	3 18
55	12	0.3	1	3 9
56	14	0.3	1	1 13
57	22	0.3	1	1 21
58	20	0.3	1	1 19
59	21	0.3	1	3 18
60	21	0.3	1	0 21
61	27	0.3	1	0 27
62	23	0.3	1	0 23
63	32	0.3	1	0 32
64	27	0.3	1	1 26
65	23	0.3	1	1 22
66	21	0.3	1	2 19
67	24	0.3	1	0 24
68	17	0.3	1	3 14
69	17	0.3	1	1 16
70	23	0.3	1	4 19
71	25	0.3	1	2 23
72	16	0.3	1	0 16
73	19	0.3	1	0 19
74	9	0.3	1	1 8
75	12	0.3	1	0 12
76	22	0.3	1	0 22
77	23	0.3	1	0 23
78	19	0.3	1	1 18
79	14	0.3	1	0 14
80	13	0.3	1	0 13
81	32	0.3	1	2 30
82	23	0.3	1	1 22
83	27	0.3	1	1 26
84	19	0.3	1	0 19
85	15	0.3	1	0 15
86	16	0.3	1	1 15
87	10	0.3	1	0 10
88	13	0.3	1	2 11
89	14	0.3	1	1 13
90	24	0.3	1	1 23
91	10	0.3	1	0 10
92	22	0.3	1	0 22
93	17	0.3	1	0 17
94	22	0.3	1	0 22
95	17	0.3	1	0 17
96	17	0.3	1	3 14
97	16	0.3	1	1 15
98	24	0.3	1	3 21
99	26	0.3	1	0 26
100	8	0.3	1	0 8
101	28	0.3	1	2 26
102	22	0.3	1	2 20
103	17	0.3	1	3 14
104	21	0.3	1	3 18
105	28	0.3	1	4 24
106	22	0.3	1	1 21
107	24	0.3	1	2 22
108	21	0.3	1	4 17
109	30	0.3	1	7 23
110	21	0.3	1	4 17
111	19	0.3	1	3 16
112	25	0.3	1	4 21
113	17	0.3	1	1 16
114	23	0.3	1	5 18
115	40	0.3	1	6 34
116	26	0.3	1	4 22
117	30	0.3	1	3 27
118	27	0.3	1	3 24
119	21	0.3	1	6 15
120	29	0.3	1	5 24
121	27	0.3	1	5 22
122	24	0.3	1	5 19
123	26	0.3	1	4 22
124	23	0.3	1	4 19
125	37	0.3	1	3 34
126	31	0.3	1	5 26
127	20	0.3	1	6 14
128	26	0.3	1	9 17
129	23	0.3	1	2 21
130	28	0.3	1	8 20
131	24	0.3	1	2 22
132	19	0.3	1	2 17
133	22	0.3	1	1 21
134	37	0.3	1	0 37
135	17	0.3	1	2 15
136	20	0.3	1	0 20
137	14	0.3	1	0 14
138	12	0.3	1	1 11
139	14	0.3	1	1 13
140	23	0.3	1	1 22
141	15	0.3	1	4 11
142	7	0.3	1	1 6
143	8	0.3	1	0 8
144	2	0.3	1	0 2
145	9	0.3	1	1 8
146	11	0.3	1	0 11
147	43	0.3	1	0 43
148	5	0.3	1	0 5
149	11	0.3	1	1 10
150	5	0.3	1	0 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374989_2.fastq.gz
=============================================
22879266 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files SRR27374989_1_trimmed.fq.gz and SRR27374989_2_trimmed.fq.gz
file_1: SRR27374989_1_trimmed.fq.gz, file_2: SRR27374989_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: SRR27374989_1_trimmed.fq.gz and SRR27374989_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to SRR27374989_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to SRR27374989_2_val_2.fq.gz

Total number of sequences analysed: 22879266

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 141682 (0.62%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3265 (0.01%)


  >>> Now running FastQC on the validated data SRR27374989_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of SRR27374989_1_val_1.fq.gz
Approx 5% complete for SRR27374989_1_val_1.fq.gz
Approx 10% complete for SRR27374989_1_val_1.fq.gz
Approx 15% complete for SRR27374989_1_val_1.fq.gz
Approx 20% complete for SRR27374989_1_val_1.fq.gz
Approx 25% complete for SRR27374989_1_val_1.fq.gz
Approx 30% complete for SRR27374989_1_val_1.fq.gz
Approx 35% complete for SRR27374989_1_val_1.fq.gz
Approx 40% complete for SRR27374989_1_val_1.fq.gz
Approx 45% complete for SRR27374989_1_val_1.fq.gz
Approx 50% complete for SRR27374989_1_val_1.fq.gz
Approx 55% complete for SRR27374989_1_val_1.fq.gz
Approx 60% complete for SRR27374989_1_val_1.fq.gz
Approx 65% complete for SRR27374989_1_val_1.fq.gz
Approx 70% complete for SRR27374989_1_val_1.fq.gz
Approx 75% complete for SRR27374989_1_val_1.fq.gz
Approx 80% complete for SRR27374989_1_val_1.fq.gz
Approx 85% complete for SRR27374989_1_val_1.fq.gz
Approx 90% complete for SRR27374989_1_val_1.fq.gz
Approx 95% complete for SRR27374989_1_val_1.fq.gz
Analysis complete for SRR27374989_1_val_1.fq.gz

  >>> Now running FastQC on the validated data SRR27374989_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of SRR27374989_2_val_2.fq.gz
Approx 5% complete for SRR27374989_2_val_2.fq.gz
Approx 10% complete for SRR27374989_2_val_2.fq.gz
Approx 15% complete for SRR27374989_2_val_2.fq.gz
Approx 20% complete for SRR27374989_2_val_2.fq.gz
Approx 25% complete for SRR27374989_2_val_2.fq.gz
Approx 30% complete for SRR27374989_2_val_2.fq.gz
Approx 35% complete for SRR27374989_2_val_2.fq.gz
Approx 40% complete for SRR27374989_2_val_2.fq.gz
Approx 45% complete for SRR27374989_2_val_2.fq.gz
Approx 50% complete for SRR27374989_2_val_2.fq.gz
Approx 55% complete for SRR27374989_2_val_2.fq.gz
Approx 60% complete for SRR27374989_2_val_2.fq.gz
Approx 65% complete for SRR27374989_2_val_2.fq.gz
Approx 70% complete for SRR27374989_2_val_2.fq.gz
Approx 75% complete for SRR27374989_2_val_2.fq.gz
Approx 80% complete for SRR27374989_2_val_2.fq.gz
Approx 85% complete for SRR27374989_2_val_2.fq.gz
Approx 90% complete for SRR27374989_2_val_2.fq.gz
Approx 95% complete for SRR27374989_2_val_2.fq.gz
Analysis complete for SRR27374989_2_val_2.fq.gz
Deleting both intermediate output files SRR27374989_1_trimmed.fq.gz and SRR27374989_2_trimmed.fq.gz

====================================================================================================

正在处理样本 SRR27374990 ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_1.fastq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
Illumina	12	AGATCGGAAGAGC	1000000	0.00
smallRNA	7	TGGAATTCTCGG	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using Illumina adapter for trimming (count: 12). Second best hit was smallRNA (count: 7)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374990_1.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to SRR27374990_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_1.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a AGATCGGAAGAGC /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,853,947
Reads with adapters:                   421,726 (1.8%)
Reads written (passing filters):    23,853,947 (100.0%)

Total basepairs processed: 3,448,649,835 bp
Quality-trimmed:              24,350,508 bp (0.7%)
Total written (filtered):  3,422,531,938 bp (99.2%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 421726 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 24.2%
  C: 28.4%
  G: 28.7%
  T: 18.6%
  none/other: 0.1%

Overview of removed sequences
length	count	expect	max.err	error counts
3	338538	372717.9	0	338538
4	71392	93179.5	0	71392
5	3488	23294.9	0	3488
6	827	5823.7	0	827
7	335	1455.9	0	335
8	33	364.0	0	33
9	447	91.0	0	43 404
10	272	22.7	1	20 252
11	228	5.7	1	3 225
12	170	1.4	1	12 158
13	139	0.4	1	48 91
14	93	0.4	1	1 92
15	103	0.4	1	0 103
16	101	0.4	1	5 96
17	94	0.4	1	1 93
18	84	0.4	1	2 82
19	76	0.4	1	1 75
20	79	0.4	1	2 77
21	81	0.4	1	7 74
22	69	0.4	1	5 64
23	60	0.4	1	1 59
24	73	0.4	1	4 69
25	80	0.4	1	3 77
26	64	0.4	1	2 62
27	130	0.4	1	3 127
28	81	0.4	1	7 74
29	69	0.4	1	8 61
30	74	0.4	1	1 73
31	42	0.4	1	8 34
32	53	0.4	1	4 49
33	40	0.4	1	1 39
34	33	0.4	1	3 30
35	37	0.4	1	3 34
36	37	0.4	1	5 32
37	39	0.4	1	2 37
38	41	0.4	1	2 39
39	107	0.4	1	2 105
40	51	0.4	1	2 49
41	49	0.4	1	2 47
42	35	0.4	1	3 32
43	45	0.4	1	7 38
44	39	0.4	1	5 34
45	34	0.4	1	2 32
46	31	0.4	1	1 30
47	28	0.4	1	3 25
48	27	0.4	1	1 26
49	23	0.4	1	1 22
50	39	0.4	1	1 38
51	55	0.4	1	4 51
52	56	0.4	1	3 53
53	64	0.4	1	0 64
54	34	0.4	1	0 34
55	38	0.4	1	0 38
56	43	0.4	1	1 42
57	24	0.4	1	1 23
58	25	0.4	1	1 24
59	22	0.4	1	0 22
60	25	0.4	1	0 25
61	29	0.4	1	0 29
62	37	0.4	1	0 37
63	30	0.4	1	0 30
64	35	0.4	1	0 35
65	16	0.4	1	1 15
66	31	0.4	1	0 31
67	24	0.4	1	0 24
68	39	0.4	1	0 39
69	36	0.4	1	1 35
70	46	0.4	1	0 46
71	20	0.4	1	0 20
72	36	0.4	1	0 36
73	39	0.4	1	1 38
74	20	0.4	1	0 20
75	31	0.4	1	0 31
76	35	0.4	1	1 34
77	32	0.4	1	0 32
78	33	0.4	1	1 32
79	27	0.4	1	0 27
80	43	0.4	1	0 43
81	37	0.4	1	0 37
82	45	0.4	1	0 45
83	39	0.4	1	0 39
84	35	0.4	1	0 35
85	29	0.4	1	0 29
86	23	0.4	1	1 22
87	36	0.4	1	0 36
88	31	0.4	1	0 31
89	36	0.4	1	0 36
90	35	0.4	1	0 35
91	51	0.4	1	1 50
92	50	0.4	1	0 50
93	27	0.4	1	0 27
94	29	0.4	1	0 29
95	26	0.4	1	0 26
96	30	0.4	1	0 30
97	31	0.4	1	0 31
98	41	0.4	1	0 41
99	21	0.4	1	0 21
100	61	0.4	1	0 61
101	54	0.4	1	0 54
102	80	0.4	1	0 80
103	40	0.4	1	0 40
104	43	0.4	1	0 43
105	51	0.4	1	0 51
106	83	0.4	1	0 83
107	33	0.4	1	0 33
108	27	0.4	1	0 27
109	32	0.4	1	0 32
110	42	0.4	1	0 42
111	37	0.4	1	0 37
112	49	0.4	1	0 49
113	47	0.4	1	0 47
114	59	0.4	1	0 59
115	39	0.4	1	0 39
116	21	0.4	1	0 21
117	24	0.4	1	1 23
118	31	0.4	1	0 31
119	34	0.4	1	1 33
120	22	0.4	1	0 22
121	14	0.4	1	0 14
122	26	0.4	1	1 25
123	24	0.4	1	0 24
124	31	0.4	1	0 31
125	29	0.4	1	0 29
126	30	0.4	1	0 30
127	48	0.4	1	0 48
128	46	0.4	1	1 45
129	49	0.4	1	1 48
130	27	0.4	1	0 27
131	51	0.4	1	1 50
132	17	0.4	1	0 17
133	52	0.4	1	0 52
134	31	0.4	1	0 31
135	14	0.4	1	0 14
136	34	0.4	1	0 34
137	28	0.4	1	0 28
138	29	0.4	1	0 29
139	33	0.4	1	0 33
140	21	0.4	1	0 21
141	35	0.4	1	0 35
142	20	0.4	1	0 20
143	21	0.4	1	0 21
144	22	0.4	1	0 22
145	31	0.4	1	0 31
146	15	0.4	1	0 15
147	45	0.4	1	1 44
148	12	0.4	1	0 12
149	6	0.4	1	0 6
150	189	0.4	1	0 189

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_1.fastq.gz
=============================================
23853947 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374990_2.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'AGATCGGAAGAGC' (Illumina TruSeq, Sanger iPCR; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to SRR27374990_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'AGATCGGAAGAGC' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_2.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a AGATCGGAAGAGC /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              23,853,947
Reads with adapters:                   485,897 (2.0%)
Reads written (passing filters):    23,853,947 (100.0%)

Total basepairs processed: 3,442,772,866 bp
Quality-trimmed:              32,863,589 bp (1.0%)
Total written (filtered):  3,408,020,194 bp (99.0%)

=== Adapter 1 ===

Sequence: AGATCGGAAGAGC; Type: regular 3'; Length: 13; Trimmed: 485897 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-13 bp: 1

Bases preceding removed adapters:
  A: 30.8%
  C: 28.7%
  G: 29.1%
  T: 11.3%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	372313	372717.9	0	372313
4	75379	93179.5	0	75379
5	20632	23294.9	0	20632
6	7725	5823.7	0	7725
7	1929	1455.9	0	1929
8	566	364.0	0	566
9	1549	91.0	0	301 1248
10	760	22.7	1	159 601
11	375	5.7	1	114 261
12	212	1.4	1	116 96
13	121	0.4	1	76 45
14	113	0.4	1	78 35
15	129	0.4	1	89 40
16	119	0.4	1	76 43
17	130	0.4	1	89 41
18	122	0.4	1	94 28
19	165	0.4	1	121 44
20	110	0.4	1	74 36
21	61	0.4	1	36 25
22	49	0.4	1	23 26
23	53	0.4	1	32 21
24	81	0.4	1	55 26
25	42	0.4	1	19 23
26	90	0.4	1	43 47
27	107	0.4	1	58 49
28	50	0.4	1	18 32
29	53	0.4	1	31 22
30	40	0.4	1	24 16
31	54	0.4	1	22 32
32	43	0.4	1	19 24
33	57	0.4	1	27 30
34	37	0.4	1	17 20
35	26	0.4	1	9 17
36	44	0.4	1	12 32
37	52	0.4	1	26 26
38	20	0.4	1	5 15
39	32	0.4	1	12 20
40	31	0.4	1	16 15
41	30	0.4	1	9 21
42	20	0.4	1	1 19
43	25	0.4	1	6 19
44	23	0.4	1	5 18
45	22	0.4	1	6 16
46	22	0.4	1	4 18
47	20	0.4	1	6 14
48	17	0.4	1	5 12
49	25	0.4	1	5 20
50	21	0.4	1	3 18
51	15	0.4	1	1 14
52	36	0.4	1	8 28
53	26	0.4	1	3 23
54	20	0.4	1	3 17
55	24	0.4	1	4 20
56	21	0.4	1	1 20
57	34	0.4	1	6 28
58	33	0.4	1	3 30
59	19	0.4	1	3 16
60	22	0.4	1	1 21
61	29	0.4	1	2 27
62	23	0.4	1	0 23
63	12	0.4	1	0 12
64	25	0.4	1	2 23
65	18	0.4	1	1 17
66	25	0.4	1	0 25
67	30	0.4	1	1 29
68	17	0.4	1	4 13
69	27	0.4	1	3 24
70	13	0.4	1	1 12
71	34	0.4	1	3 31
72	25	0.4	1	2 23
73	22	0.4	1	1 21
74	16	0.4	1	1 15
75	16	0.4	1	0 16
76	19	0.4	1	0 19
77	16	0.4	1	1 15
78	12	0.4	1	3 9
79	22	0.4	1	0 22
80	19	0.4	1	0 19
81	27	0.4	1	2 25
82	26	0.4	1	0 26
83	30	0.4	1	3 27
84	27	0.4	1	2 25
85	11	0.4	1	1 10
86	14	0.4	1	1 13
87	16	0.4	1	1 15
88	18	0.4	1	0 18
89	20	0.4	1	0 20
90	17	0.4	1	1 16
91	21	0.4	1	2 19
92	21	0.4	1	1 20
93	24	0.4	1	1 23
94	22	0.4	1	1 21
95	26	0.4	1	3 23
96	15	0.4	1	1 14
97	21	0.4	1	0 21
98	15	0.4	1	1 14
99	18	0.4	1	1 17
100	15	0.4	1	2 13
101	18	0.4	1	0 18
102	21	0.4	1	6 15
103	25	0.4	1	4 21
104	35	0.4	1	4 31
105	22	0.4	1	4 18
106	31	0.4	1	2 29
107	35	0.4	1	6 29
108	20	0.4	1	2 18
109	24	0.4	1	1 23
110	28	0.4	1	5 23
111	24	0.4	1	3 21
112	32	0.4	1	6 26
113	17	0.4	1	3 14
114	23	0.4	1	5 18
115	29	0.4	1	4 25
116	36	0.4	1	3 33
117	34	0.4	1	9 25
118	30	0.4	1	3 27
119	18	0.4	1	2 16
120	20	0.4	1	4 16
121	26	0.4	1	6 20
122	25	0.4	1	7 18
123	28	0.4	1	9 19
124	18	0.4	1	4 14
125	28	0.4	1	1 27
126	21	0.4	1	1 20
127	17	0.4	1	3 14
128	35	0.4	1	6 29
129	27	0.4	1	4 23
130	28	0.4	1	5 23
131	31	0.4	1	4 27
132	25	0.4	1	1 24
133	22	0.4	1	1 21
134	30	0.4	1	2 28
135	21	0.4	1	1 20
136	15	0.4	1	0 15
137	17	0.4	1	2 15
138	16	0.4	1	1 15
139	11	0.4	1	0 11
140	17	0.4	1	0 17
141	19	0.4	1	6 13
142	10	0.4	1	4 6
143	14	0.4	1	1 13
144	8	0.4	1	1 7
145	14	0.4	1	0 14
146	5	0.4	1	0 5
147	37	0.4	1	1 36
148	11	0.4	1	3 8
149	25	0.4	1	0 25
150	4	0.4	1	0 4

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374990_2.fastq.gz
=============================================
23853947 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files SRR27374990_1_trimmed.fq.gz and SRR27374990_2_trimmed.fq.gz
file_1: SRR27374990_1_trimmed.fq.gz, file_2: SRR27374990_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: SRR27374990_1_trimmed.fq.gz and SRR27374990_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to SRR27374990_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to SRR27374990_2_val_2.fq.gz

Total number of sequences analysed: 23853947

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 148238 (0.62%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3346 (0.01%)


  >>> Now running FastQC on the validated data SRR27374990_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of SRR27374990_1_val_1.fq.gz
Approx 5% complete for SRR27374990_1_val_1.fq.gz
Approx 10% complete for SRR27374990_1_val_1.fq.gz
Approx 15% complete for SRR27374990_1_val_1.fq.gz
Approx 20% complete for SRR27374990_1_val_1.fq.gz
Approx 25% complete for SRR27374990_1_val_1.fq.gz
Approx 30% complete for SRR27374990_1_val_1.fq.gz
Approx 35% complete for SRR27374990_1_val_1.fq.gz
Approx 40% complete for SRR27374990_1_val_1.fq.gz
Approx 45% complete for SRR27374990_1_val_1.fq.gz
Approx 50% complete for SRR27374990_1_val_1.fq.gz
Approx 55% complete for SRR27374990_1_val_1.fq.gz
Approx 60% complete for SRR27374990_1_val_1.fq.gz
Approx 65% complete for SRR27374990_1_val_1.fq.gz
Approx 70% complete for SRR27374990_1_val_1.fq.gz
Approx 75% complete for SRR27374990_1_val_1.fq.gz
Approx 80% complete for SRR27374990_1_val_1.fq.gz
Approx 85% complete for SRR27374990_1_val_1.fq.gz
Approx 90% complete for SRR27374990_1_val_1.fq.gz
Approx 95% complete for SRR27374990_1_val_1.fq.gz
Analysis complete for SRR27374990_1_val_1.fq.gz

  >>> Now running FastQC on the validated data SRR27374990_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of SRR27374990_2_val_2.fq.gz
Approx 5% complete for SRR27374990_2_val_2.fq.gz
Approx 10% complete for SRR27374990_2_val_2.fq.gz
Approx 15% complete for SRR27374990_2_val_2.fq.gz
Approx 20% complete for SRR27374990_2_val_2.fq.gz
Approx 25% complete for SRR27374990_2_val_2.fq.gz
Approx 30% complete for SRR27374990_2_val_2.fq.gz
Approx 35% complete for SRR27374990_2_val_2.fq.gz
Approx 40% complete for SRR27374990_2_val_2.fq.gz
Approx 45% complete for SRR27374990_2_val_2.fq.gz
Approx 50% complete for SRR27374990_2_val_2.fq.gz
Approx 55% complete for SRR27374990_2_val_2.fq.gz
Approx 60% complete for SRR27374990_2_val_2.fq.gz
Approx 65% complete for SRR27374990_2_val_2.fq.gz
Approx 70% complete for SRR27374990_2_val_2.fq.gz
Approx 75% complete for SRR27374990_2_val_2.fq.gz
Approx 80% complete for SRR27374990_2_val_2.fq.gz
Approx 85% complete for SRR27374990_2_val_2.fq.gz
Approx 90% complete for SRR27374990_2_val_2.fq.gz
Approx 95% complete for SRR27374990_2_val_2.fq.gz
Analysis complete for SRR27374990_2_val_2.fq.gz
Deleting both intermediate output files SRR27374990_1_trimmed.fq.gz and SRR27374990_2_trimmed.fq.gz

====================================================================================================

正在处理样本 SRR27374991 ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	8	TGGAATTCTCGG	1000000	0.00
Illumina	5	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using smallRNA adapter for trimming (count: 8). Second best hit was Illumina (count: 5)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374991_1.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to SRR27374991_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,701,972
Reads with adapters:                   532,032 (2.6%)
Reads written (passing filters):    20,701,972 (100.0%)

Total basepairs processed: 2,994,272,811 bp
Quality-trimmed:              22,024,261 bp (0.7%)
Total written (filtered):  2,969,828,396 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 532032 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 21.5%
  C: 20.3%
  G: 33.4%
  T: 24.7%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	409513	323468.3	0	409513
4	76411	80867.1	0	76411
5	28130	20216.8	0	28130
6	5294	5054.2	0	5294
7	1014	1263.5	0	1014
8	292	315.9	0	292
9	483	79.0	0	120 363
10	1125	19.7	1	34 1091
11	448	4.9	1	5 443
12	174	1.2	1	1 173
13	93	1.2	1	0 93
14	70	1.2	1	1 69
15	75	1.2	1	1 74
16	82	1.2	1	0 82
17	55	1.2	1	3 52
18	78	1.2	1	2 76
19	68	1.2	1	0 68
20	88	1.2	1	0 88
21	122	1.2	1	1 121
22	57	1.2	1	1 56
23	94	1.2	1	1 93
24	52	1.2	1	0 52
25	114	1.2	1	2 112
26	89	1.2	1	4 85
27	141	1.2	1	4 137
28	115	1.2	1	5 110
29	57	1.2	1	0 57
30	150	1.2	1	2 148
31	83	1.2	1	2 81
32	112	1.2	1	2 110
33	122	1.2	1	1 121
34	68	1.2	1	0 68
35	98	1.2	1	2 96
36	70	1.2	1	0 70
37	80	1.2	1	0 80
38	53	1.2	1	0 53
39	99	1.2	1	2 97
40	52	1.2	1	6 46
41	48	1.2	1	0 48
42	44	1.2	1	5 39
43	57	1.2	1	0 57
44	39	1.2	1	2 37
45	46	1.2	1	2 44
46	65	1.2	1	1 64
47	51	1.2	1	1 50
48	85	1.2	1	1 84
49	129	1.2	1	0 129
50	98	1.2	1	0 98
51	100	1.2	1	0 100
52	76	1.2	1	0 76
53	168	1.2	1	0 168
54	176	1.2	1	0 176
55	100	1.2	1	1 99
56	103	1.2	1	4 99
57	123	1.2	1	6 117
58	49	1.2	1	3 46
59	74	1.2	1	4 70
60	72	1.2	1	0 72
61	63	1.2	1	2 61
62	75	1.2	1	2 73
63	47	1.2	1	0 47
64	45	1.2	1	1 44
65	51	1.2	1	0 51
66	94	1.2	1	2 92
67	55	1.2	1	0 55
68	74	1.2	1	0 74
69	105	1.2	1	0 105
70	41	1.2	1	0 41
71	46	1.2	1	2 44
72	62	1.2	1	0 62
73	76	1.2	1	4 72
74	78	1.2	1	2 76
75	82	1.2	1	5 77
76	53	1.2	1	3 50
77	70	1.2	1	0 70
78	59	1.2	1	0 59
79	67	1.2	1	2 65
80	44	1.2	1	1 43
81	45	1.2	1	0 45
82	40	1.2	1	0 40
83	47	1.2	1	0 47
84	55	1.2	1	0 55
85	49	1.2	1	1 48
86	80	1.2	1	0 80
87	39	1.2	1	0 39
88	52	1.2	1	3 49
89	71	1.2	1	2 69
90	43	1.2	1	1 42
91	27	1.2	1	1 26
92	49	1.2	1	1 48
93	43	1.2	1	2 41
94	50	1.2	1	1 49
95	48	1.2	1	2 46
96	74	1.2	1	0 74
97	49	1.2	1	1 48
98	30	1.2	1	0 30
99	39	1.2	1	1 38
100	63	1.2	1	0 63
101	123	1.2	1	1 122
102	107	1.2	1	0 107
103	79	1.2	1	0 79
104	38	1.2	1	2 36
105	49	1.2	1	0 49
106	36	1.2	1	2 34
107	42	1.2	1	1 41
108	39	1.2	1	1 38
109	39	1.2	1	1 38
110	32	1.2	1	1 31
111	75	1.2	1	1 74
112	52	1.2	1	2 50
113	26	1.2	1	1 25
114	31	1.2	1	0 31
115	31	1.2	1	0 31
116	43	1.2	1	3 40
117	52	1.2	1	0 52
118	71	1.2	1	5 66
119	31	1.2	1	0 31
120	24	1.2	1	0 24
121	57	1.2	1	0 57
122	46	1.2	1	0 46
123	43	1.2	1	0 43
124	34	1.2	1	0 34
125	28	1.2	1	0 28
126	42	1.2	1	0 42
127	29	1.2	1	0 29
128	34	1.2	1	1 33
129	42	1.2	1	1 41
130	33	1.2	1	0 33
131	42	1.2	1	2 40
132	39	1.2	1	0 39
133	60	1.2	1	1 59
134	31	1.2	1	2 29
135	52	1.2	1	1 51
136	30	1.2	1	0 30
137	55	1.2	1	0 55
138	31	1.2	1	1 30
139	41	1.2	1	1 40
140	26	1.2	1	0 26
141	72	1.2	1	1 71
142	18	1.2	1	0 18
143	66	1.2	1	0 66
144	239	1.2	1	2 237
145	52	1.2	1	1 51
146	17	1.2	1	0 17
147	47	1.2	1	0 47
148	121	1.2	1	2 119
149	199	1.2	1	2 197
150	82	1.2	1	2 80

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_1.fastq.gz
=============================================
20701972 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374991_2.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to SRR27374991_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,701,972
Reads with adapters:                   310,498 (1.5%)
Reads written (passing filters):    20,701,972 (100.0%)

Total basepairs processed: 2,989,421,519 bp
Quality-trimmed:              26,877,718 bp (0.9%)
Total written (filtered):  2,961,267,029 bp (99.1%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 310498 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 29.8%
  C: 20.4%
  G: 19.1%
  T: 30.7%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	237342	323468.3	0	237342
4	50279	80867.1	0	50279
5	14049	20216.8	0	14049
6	3380	5054.2	0	3380
7	737	1263.5	0	737
8	286	315.9	0	286
9	539	79.0	0	39 500
10	359	19.7	1	7 352
11	170	4.9	1	0 170
12	24	1.2	1	0 24
13	16	1.2	1	1 15
14	19	1.2	1	0 19
15	26	1.2	1	0 26
16	33	1.2	1	0 33
17	26	1.2	1	0 26
18	28	1.2	1	0 28
19	28	1.2	1	0 28
20	31	1.2	1	0 31
21	30	1.2	1	0 30
22	25	1.2	1	0 25
23	27	1.2	1	0 27
24	25	1.2	1	0 25
25	24	1.2	1	1 23
26	52	1.2	1	0 52
27	34	1.2	1	0 34
28	44	1.2	1	0 44
29	28	1.2	1	0 28
30	16	1.2	1	0 16
31	34	1.2	1	0 34
32	33	1.2	1	0 33
33	37	1.2	1	0 37
34	35	1.2	1	0 35
35	27	1.2	1	0 27
36	34	1.2	1	0 34
37	27	1.2	1	0 27
38	18	1.2	1	0 18
39	32	1.2	1	0 32
40	22	1.2	1	0 22
41	15	1.2	1	0 15
42	14	1.2	1	0 14
43	28	1.2	1	1 27
44	19	1.2	1	0 19
45	34	1.2	1	0 34
46	21	1.2	1	0 21
47	33	1.2	1	0 33
48	32	1.2	1	0 32
49	30	1.2	1	0 30
50	26	1.2	1	0 26
51	25	1.2	1	0 25
52	34	1.2	1	0 34
53	19	1.2	1	0 19
54	22	1.2	1	1 21
55	21	1.2	1	0 21
56	20	1.2	1	0 20
57	30	1.2	1	0 30
58	19	1.2	1	0 19
59	39	1.2	1	1 38
60	42	1.2	1	0 42
61	25	1.2	1	0 25
62	26	1.2	1	0 26
63	21	1.2	1	0 21
64	19	1.2	1	0 19
65	23	1.2	1	0 23
66	23	1.2	1	0 23
67	26	1.2	1	0 26
68	23	1.2	1	0 23
69	25	1.2	1	0 25
70	22	1.2	1	0 22
71	35	1.2	1	0 35
72	31	1.2	1	0 31
73	30	1.2	1	0 30
74	18	1.2	1	0 18
75	13	1.2	1	0 13
76	23	1.2	1	0 23
77	21	1.2	1	0 21
78	26	1.2	1	0 26
79	30	1.2	1	1 29
80	21	1.2	1	1 20
81	28	1.2	1	0 28
82	26	1.2	1	1 25
83	35	1.2	1	0 35
84	19	1.2	1	0 19
85	18	1.2	1	0 18
86	30	1.2	1	0 30
87	18	1.2	1	0 18
88	28	1.2	1	1 27
89	29	1.2	1	0 29
90	25	1.2	1	1 24
91	15	1.2	1	1 14
92	19	1.2	1	0 19
93	33	1.2	1	0 33
94	24	1.2	1	0 24
95	19	1.2	1	0 19
96	29	1.2	1	0 29
97	21	1.2	1	1 20
98	20	1.2	1	0 20
99	22	1.2	1	0 22
100	25	1.2	1	0 25
101	29	1.2	1	1 28
102	37	1.2	1	1 36
103	23	1.2	1	0 23
104	17	1.2	1	0 17
105	18	1.2	1	0 18
106	16	1.2	1	0 16
107	18	1.2	1	0 18
108	18	1.2	1	1 17
109	25	1.2	1	3 22
110	23	1.2	1	0 23
111	24	1.2	1	0 24
112	17	1.2	1	1 16
113	10	1.2	1	1 9
114	23	1.2	1	0 23
115	25	1.2	1	1 24
116	25	1.2	1	0 25
117	24	1.2	1	2 22
118	28	1.2	1	0 28
119	15	1.2	1	0 15
120	27	1.2	1	0 27
121	15	1.2	1	2 13
122	17	1.2	1	0 17
123	23	1.2	1	4 19
124	20	1.2	1	2 18
125	17	1.2	1	0 17
126	30	1.2	1	0 30
127	22	1.2	1	0 22
128	27	1.2	1	0 27
129	16	1.2	1	0 16
130	24	1.2	1	0 24
131	10	1.2	1	0 10
132	26	1.2	1	0 26
133	16	1.2	1	0 16
134	17	1.2	1	0 17
135	23	1.2	1	1 22
136	24	1.2	1	0 24
137	17	1.2	1	0 17
138	14	1.2	1	1 13
139	15	1.2	1	0 15
140	11	1.2	1	0 11
141	14	1.2	1	0 14
142	36	1.2	1	0 36
143	19	1.2	1	0 19
144	28	1.2	1	1 27
145	9	1.2	1	1 8
146	39	1.2	1	3 36
147	25	1.2	1	2 23
148	9	1.2	1	0 9
149	24	1.2	1	0 24
150	6	1.2	1	1 5

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374991_2.fastq.gz
=============================================
20701972 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files SRR27374991_1_trimmed.fq.gz and SRR27374991_2_trimmed.fq.gz
file_1: SRR27374991_1_trimmed.fq.gz, file_2: SRR27374991_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: SRR27374991_1_trimmed.fq.gz and SRR27374991_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to SRR27374991_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to SRR27374991_2_val_2.fq.gz

Total number of sequences analysed: 20701972

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 124225 (0.60%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3077 (0.01%)


  >>> Now running FastQC on the validated data SRR27374991_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of SRR27374991_1_val_1.fq.gz
Approx 5% complete for SRR27374991_1_val_1.fq.gz
Approx 10% complete for SRR27374991_1_val_1.fq.gz
Approx 15% complete for SRR27374991_1_val_1.fq.gz
Approx 20% complete for SRR27374991_1_val_1.fq.gz
Approx 25% complete for SRR27374991_1_val_1.fq.gz
Approx 30% complete for SRR27374991_1_val_1.fq.gz
Approx 35% complete for SRR27374991_1_val_1.fq.gz
Approx 40% complete for SRR27374991_1_val_1.fq.gz
Approx 45% complete for SRR27374991_1_val_1.fq.gz
Approx 50% complete for SRR27374991_1_val_1.fq.gz
Approx 55% complete for SRR27374991_1_val_1.fq.gz
Approx 60% complete for SRR27374991_1_val_1.fq.gz
Approx 65% complete for SRR27374991_1_val_1.fq.gz
Approx 70% complete for SRR27374991_1_val_1.fq.gz
Approx 75% complete for SRR27374991_1_val_1.fq.gz
Approx 80% complete for SRR27374991_1_val_1.fq.gz
Approx 85% complete for SRR27374991_1_val_1.fq.gz
Approx 90% complete for SRR27374991_1_val_1.fq.gz
Approx 95% complete for SRR27374991_1_val_1.fq.gz
Analysis complete for SRR27374991_1_val_1.fq.gz

  >>> Now running FastQC on the validated data SRR27374991_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of SRR27374991_2_val_2.fq.gz
Approx 5% complete for SRR27374991_2_val_2.fq.gz
Approx 10% complete for SRR27374991_2_val_2.fq.gz
Approx 15% complete for SRR27374991_2_val_2.fq.gz
Approx 20% complete for SRR27374991_2_val_2.fq.gz
Approx 25% complete for SRR27374991_2_val_2.fq.gz
Approx 30% complete for SRR27374991_2_val_2.fq.gz
Approx 35% complete for SRR27374991_2_val_2.fq.gz
Approx 40% complete for SRR27374991_2_val_2.fq.gz
Approx 45% complete for SRR27374991_2_val_2.fq.gz
Approx 50% complete for SRR27374991_2_val_2.fq.gz
Approx 55% complete for SRR27374991_2_val_2.fq.gz
Approx 60% complete for SRR27374991_2_val_2.fq.gz
Approx 65% complete for SRR27374991_2_val_2.fq.gz
Approx 70% complete for SRR27374991_2_val_2.fq.gz
Approx 75% complete for SRR27374991_2_val_2.fq.gz
Approx 80% complete for SRR27374991_2_val_2.fq.gz
Approx 85% complete for SRR27374991_2_val_2.fq.gz
Approx 90% complete for SRR27374991_2_val_2.fq.gz
Approx 95% complete for SRR27374991_2_val_2.fq.gz
Analysis complete for SRR27374991_2_val_2.fq.gz
Deleting both intermediate output files SRR27374991_1_trimmed.fq.gz and SRR27374991_2_trimmed.fq.gz

====================================================================================================

正在处理样本 SRR27374992 ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	17	TGGAATTCTCGG	1000000	0.00
Illumina	11	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using smallRNA adapter for trimming (count: 17). Second best hit was Illumina (count: 11)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374992_1.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to SRR27374992_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,996,933
Reads with adapters:                   608,298 (2.6%)
Reads written (passing filters):    22,996,933 (100.0%)

Total basepairs processed: 3,312,601,285 bp
Quality-trimmed:              23,405,018 bp (0.7%)
Total written (filtered):  3,286,570,668 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 608298 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.1%
  C: 18.2%
  G: 34.6%
  T: 24.1%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	471801	359327.1	0	471801
4	82215	89831.8	0	82215
5	36161	22457.9	0	36161
6	6197	5614.5	0	6197
7	1004	1403.6	0	1004
8	244	350.9	0	244
9	579	87.7	0	85 494
10	993	21.9	1	32 961
11	459	5.5	1	6 453
12	156	1.4	1	5 151
13	104	1.4	1	5 99
14	55	1.4	1	0 55
15	65	1.4	1	1 64
16	73	1.4	1	3 70
17	55	1.4	1	4 51
18	59	1.4	1	3 56
19	49	1.4	1	1 48
20	80	1.4	1	0 80
21	106	1.4	1	1 105
22	51	1.4	1	1 50
23	73	1.4	1	3 70
24	82	1.4	1	2 80
25	82	1.4	1	1 81
26	112	1.4	1	12 100
27	109	1.4	1	7 102
28	98	1.4	1	4 94
29	67	1.4	1	3 64
30	97	1.4	1	3 94
31	81	1.4	1	1 80
32	92	1.4	1	3 89
33	88	1.4	1	1 87
34	55	1.4	1	1 54
35	68	1.4	1	1 67
36	76	1.4	1	2 74
37	79	1.4	1	0 79
38	40	1.4	1	3 37
39	92	1.4	1	2 90
40	45	1.4	1	3 42
41	58	1.4	1	4 54
42	56	1.4	1	1 55
43	76	1.4	1	4 72
44	51	1.4	1	4 47
45	51	1.4	1	3 48
46	71	1.4	1	1 70
47	65	1.4	1	5 60
48	74	1.4	1	3 71
49	107	1.4	1	1 106
50	89	1.4	1	0 89
51	115	1.4	1	0 115
52	67	1.4	1	0 67
53	155	1.4	1	0 155
54	150	1.4	1	1 149
55	89	1.4	1	1 88
56	113	1.4	1	3 110
57	108	1.4	1	4 104
58	51	1.4	1	2 49
59	57	1.4	1	5 52
60	66	1.4	1	0 66
61	70	1.4	1	0 70
62	42	1.4	1	1 41
63	58	1.4	1	0 58
64	28	1.4	1	0 28
65	67	1.4	1	1 66
66	70	1.4	1	1 69
67	75	1.4	1	1 74
68	61	1.4	1	0 61
69	96	1.4	1	3 93
70	36	1.4	1	3 33
71	48	1.4	1	3 45
72	78	1.4	1	0 78
73	57	1.4	1	1 56
74	67	1.4	1	2 65
75	69	1.4	1	3 66
76	39	1.4	1	4 35
77	67	1.4	1	0 67
78	62	1.4	1	3 59
79	69	1.4	1	4 65
80	40	1.4	1	1 39
81	53	1.4	1	1 52
82	46	1.4	1	0 46
83	36	1.4	1	1 35
84	50	1.4	1	0 50
85	48	1.4	1	0 48
86	74	1.4	1	0 74
87	31	1.4	1	0 31
88	56	1.4	1	7 49
89	52	1.4	1	5 47
90	24	1.4	1	0 24
91	41	1.4	1	2 39
92	48	1.4	1	1 47
93	49	1.4	1	5 44
94	52	1.4	1	1 51
95	56	1.4	1	2 54
96	79	1.4	1	1 78
97	55	1.4	1	0 55
98	42	1.4	1	0 42
99	47	1.4	1	1 46
100	42	1.4	1	1 41
101	106	1.4	1	0 106
102	87	1.4	1	2 85
103	53	1.4	1	0 53
104	44	1.4	1	5 39
105	42	1.4	1	0 42
106	40	1.4	1	1 39
107	43	1.4	1	0 43
108	35	1.4	1	0 35
109	42	1.4	1	5 37
110	48	1.4	1	1 47
111	49	1.4	1	0 49
112	61	1.4	1	0 61
113	29	1.4	1	1 28
114	42	1.4	1	1 41
115	37	1.4	1	0 37
116	40	1.4	1	3 37
117	49	1.4	1	0 49
118	56	1.4	1	0 56
119	39	1.4	1	0 39
120	59	1.4	1	1 58
121	36	1.4	1	0 36
122	53	1.4	1	2 51
123	54	1.4	1	2 52
124	37	1.4	1	0 37
125	33	1.4	1	0 33
126	52	1.4	1	1 51
127	46	1.4	1	1 45
128	31	1.4	1	1 30
129	50	1.4	1	4 46
130	24	1.4	1	0 24
131	30	1.4	1	4 26
132	25	1.4	1	2 23
133	65	1.4	1	1 64
134	48	1.4	1	3 45
135	37	1.4	1	0 37
136	33	1.4	1	0 33
137	35	1.4	1	0 35
138	39	1.4	1	2 37
139	47	1.4	1	1 46
140	36	1.4	1	3 33
141	52	1.4	1	1 51
142	22	1.4	1	0 22
143	69	1.4	1	1 68
144	149	1.4	1	3 146
145	39	1.4	1	2 37
146	18	1.4	1	0 18
147	39	1.4	1	0 39
148	80	1.4	1	1 79
149	167	1.4	1	4 163
150	60	1.4	1	0 60

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_1.fastq.gz
=============================================
22996933 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374992_2.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to SRR27374992_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,996,933
Reads with adapters:                   326,557 (1.4%)
Reads written (passing filters):    22,996,933 (100.0%)

Total basepairs processed: 3,307,180,169 bp
Quality-trimmed:              29,196,485 bp (0.9%)
Total written (filtered):  3,276,664,852 bp (99.1%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 326557 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 29.6%
  C: 20.4%
  G: 18.5%
  T: 31.6%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	247850	359327.1	0	247850
4	52216	89831.8	0	52216
5	17431	22457.9	0	17431
6	3706	5614.5	0	3706
7	742	1403.6	0	742
8	358	350.9	0	358
9	609	87.7	0	43 566
10	401	21.9	1	12 389
11	172	5.5	1	0 172
12	25	1.4	1	0 25
13	17	1.4	1	0 17
14	13	1.4	1	0 13
15	26	1.4	1	0 26
16	16	1.4	1	0 16
17	30	1.4	1	0 30
18	18	1.4	1	0 18
19	20	1.4	1	0 20
20	36	1.4	1	0 36
21	18	1.4	1	1 17
22	22	1.4	1	0 22
23	29	1.4	1	0 29
24	21	1.4	1	0 21
25	25	1.4	1	0 25
26	51	1.4	1	0 51
27	26	1.4	1	0 26
28	36	1.4	1	0 36
29	17	1.4	1	0 17
30	19	1.4	1	0 19
31	26	1.4	1	0 26
32	21	1.4	1	0 21
33	25	1.4	1	0 25
34	43	1.4	1	1 42
35	27	1.4	1	0 27
36	17	1.4	1	0 17
37	36	1.4	1	2 34
38	27	1.4	1	0 27
39	23	1.4	1	0 23
40	37	1.4	1	0 37
41	24	1.4	1	0 24
42	18	1.4	1	0 18
43	24	1.4	1	0 24
44	28	1.4	1	1 27
45	17	1.4	1	1 16
46	28	1.4	1	0 28
47	17	1.4	1	0 17
48	27	1.4	1	0 27
49	21	1.4	1	0 21
50	17	1.4	1	0 17
51	20	1.4	1	0 20
52	25	1.4	1	0 25
53	28	1.4	1	0 28
54	23	1.4	1	0 23
55	19	1.4	1	0 19
56	15	1.4	1	0 15
57	13	1.4	1	0 13
58	12	1.4	1	0 12
59	22	1.4	1	0 22
60	37	1.4	1	1 36
61	33	1.4	1	0 33
62	21	1.4	1	0 21
63	33	1.4	1	0 33
64	20	1.4	1	0 20
65	32	1.4	1	0 32
66	22	1.4	1	0 22
67	13	1.4	1	0 13
68	19	1.4	1	0 19
69	24	1.4	1	0 24
70	21	1.4	1	2 19
71	23	1.4	1	0 23
72	25	1.4	1	0 25
73	17	1.4	1	0 17
74	15	1.4	1	0 15
75	30	1.4	1	0 30
76	14	1.4	1	0 14
77	18	1.4	1	0 18
78	28	1.4	1	1 27
79	18	1.4	1	0 18
80	27	1.4	1	0 27
81	25	1.4	1	2 23
82	25	1.4	1	0 25
83	13	1.4	1	0 13
84	19	1.4	1	1 18
85	11	1.4	1	0 11
86	20	1.4	1	0 20
87	15	1.4	1	0 15
88	32	1.4	1	0 32
89	24	1.4	1	0 24
90	21	1.4	1	0 21
91	16	1.4	1	0 16
92	15	1.4	1	0 15
93	22	1.4	1	1 21
94	29	1.4	1	0 29
95	32	1.4	1	1 31
96	22	1.4	1	0 22
97	18	1.4	1	0 18
98	24	1.4	1	0 24
99	28	1.4	1	0 28
100	31	1.4	1	0 31
101	23	1.4	1	0 23
102	26	1.4	1	0 26
103	23	1.4	1	0 23
104	23	1.4	1	0 23
105	17	1.4	1	0 17
106	20	1.4	1	1 19
107	21	1.4	1	0 21
108	6	1.4	1	0 6
109	21	1.4	1	0 21
110	20	1.4	1	0 20
111	23	1.4	1	0 23
112	21	1.4	1	0 21
113	24	1.4	1	0 24
114	22	1.4	1	0 22
115	24	1.4	1	0 24
116	28	1.4	1	0 28
117	22	1.4	1	1 21
118	14	1.4	1	0 14
119	16	1.4	1	1 15
120	26	1.4	1	0 26
121	17	1.4	1	0 17
122	17	1.4	1	0 17
123	23	1.4	1	1 22
124	31	1.4	1	1 30
125	25	1.4	1	0 25
126	21	1.4	1	0 21
127	31	1.4	1	0 31
128	26	1.4	1	0 26
129	21	1.4	1	0 21
130	25	1.4	1	1 24
131	23	1.4	1	0 23
132	12	1.4	1	0 12
133	21	1.4	1	0 21
134	17	1.4	1	0 17
135	25	1.4	1	0 25
136	21	1.4	1	1 20
137	19	1.4	1	1 18
138	14	1.4	1	0 14
139	13	1.4	1	0 13
140	14	1.4	1	0 14
141	14	1.4	1	0 14
142	14	1.4	1	0 14
143	18	1.4	1	1 17
144	14	1.4	1	1 13
145	7	1.4	1	0 7
146	34	1.4	1	1 33
147	13	1.4	1	0 13
148	16	1.4	1	0 16
149	23	1.4	1	0 23
150	6	1.4	1	0 6

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374992_2.fastq.gz
=============================================
22996933 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files SRR27374992_1_trimmed.fq.gz and SRR27374992_2_trimmed.fq.gz
file_1: SRR27374992_1_trimmed.fq.gz, file_2: SRR27374992_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: SRR27374992_1_trimmed.fq.gz and SRR27374992_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to SRR27374992_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to SRR27374992_2_val_2.fq.gz

Total number of sequences analysed: 22996933

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 134588 (0.59%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3363 (0.01%)


  >>> Now running FastQC on the validated data SRR27374992_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of SRR27374992_1_val_1.fq.gz
Approx 5% complete for SRR27374992_1_val_1.fq.gz
Approx 10% complete for SRR27374992_1_val_1.fq.gz
Approx 15% complete for SRR27374992_1_val_1.fq.gz
Approx 20% complete for SRR27374992_1_val_1.fq.gz
Approx 25% complete for SRR27374992_1_val_1.fq.gz
Approx 30% complete for SRR27374992_1_val_1.fq.gz
Approx 35% complete for SRR27374992_1_val_1.fq.gz
Approx 40% complete for SRR27374992_1_val_1.fq.gz
Approx 45% complete for SRR27374992_1_val_1.fq.gz
Approx 50% complete for SRR27374992_1_val_1.fq.gz
Approx 55% complete for SRR27374992_1_val_1.fq.gz
Approx 60% complete for SRR27374992_1_val_1.fq.gz
Approx 65% complete for SRR27374992_1_val_1.fq.gz
Approx 70% complete for SRR27374992_1_val_1.fq.gz
Approx 75% complete for SRR27374992_1_val_1.fq.gz
Approx 80% complete for SRR27374992_1_val_1.fq.gz
Approx 85% complete for SRR27374992_1_val_1.fq.gz
Approx 90% complete for SRR27374992_1_val_1.fq.gz
Approx 95% complete for SRR27374992_1_val_1.fq.gz
Analysis complete for SRR27374992_1_val_1.fq.gz

  >>> Now running FastQC on the validated data SRR27374992_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of SRR27374992_2_val_2.fq.gz
Approx 5% complete for SRR27374992_2_val_2.fq.gz
Approx 10% complete for SRR27374992_2_val_2.fq.gz
Approx 15% complete for SRR27374992_2_val_2.fq.gz
Approx 20% complete for SRR27374992_2_val_2.fq.gz
Approx 25% complete for SRR27374992_2_val_2.fq.gz
Approx 30% complete for SRR27374992_2_val_2.fq.gz
Approx 35% complete for SRR27374992_2_val_2.fq.gz
Approx 40% complete for SRR27374992_2_val_2.fq.gz
Approx 45% complete for SRR27374992_2_val_2.fq.gz
Approx 50% complete for SRR27374992_2_val_2.fq.gz
Approx 55% complete for SRR27374992_2_val_2.fq.gz
Approx 60% complete for SRR27374992_2_val_2.fq.gz
Approx 65% complete for SRR27374992_2_val_2.fq.gz
Approx 70% complete for SRR27374992_2_val_2.fq.gz
Approx 75% complete for SRR27374992_2_val_2.fq.gz
Approx 80% complete for SRR27374992_2_val_2.fq.gz
Approx 85% complete for SRR27374992_2_val_2.fq.gz
Approx 90% complete for SRR27374992_2_val_2.fq.gz
Approx 95% complete for SRR27374992_2_val_2.fq.gz
Analysis complete for SRR27374992_2_val_2.fq.gz
Deleting both intermediate output files SRR27374992_1_trimmed.fq.gz and SRR27374992_2_trimmed.fq.gz

====================================================================================================

正在处理样本 SRR27374993 ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	12	TGGAATTCTCGG	1000000	0.00
Illumina	7	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using smallRNA adapter for trimming (count: 12). Second best hit was Illumina (count: 7)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374993_1.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to SRR27374993_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,178,360
Reads with adapters:                   589,464 (2.7%)
Reads written (passing filters):    22,178,360 (100.0%)

Total basepairs processed: 3,209,236,794 bp
Quality-trimmed:              22,270,440 bp (0.7%)
Total written (filtered):  3,184,407,978 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 589464 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.4%
  C: 18.3%
  G: 34.2%
  T: 24.1%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	455350	346536.9	0	455350
4	80579	86634.2	0	80579
5	36261	21658.6	0	36261
6	5751	5414.6	0	5751
7	930	1353.7	0	930
8	237	338.4	0	237
9	525	84.6	0	81 444
10	923	21.2	1	33 890
11	397	5.3	1	7 390
12	157	1.3	1	3 154
13	94	1.3	1	1 93
14	76	1.3	1	4 72
15	60	1.3	1	1 59
16	79	1.3	1	1 78
17	58	1.3	1	4 54
18	74	1.3	1	1 73
19	55	1.3	1	2 53
20	81	1.3	1	1 80
21	84	1.3	1	2 82
22	56	1.3	1	0 56
23	76	1.3	1	4 72
24	54	1.3	1	2 52
25	70	1.3	1	1 69
26	109	1.3	1	6 103
27	135	1.3	1	13 122
28	80	1.3	1	3 77
29	73	1.3	1	4 69
30	92	1.3	1	1 91
31	68	1.3	1	2 66
32	96	1.3	1	2 94
33	92	1.3	1	1 91
34	65	1.3	1	0 65
35	78	1.3	1	1 77
36	76	1.3	1	5 71
37	67	1.3	1	0 67
38	40	1.3	1	1 39
39	104	1.3	1	0 104
40	58	1.3	1	4 54
41	55	1.3	1	4 51
42	42	1.3	1	3 39
43	75	1.3	1	2 73
44	37	1.3	1	0 37
45	39	1.3	1	2 37
46	55	1.3	1	2 53
47	54	1.3	1	7 47
48	72	1.3	1	2 70
49	113	1.3	1	4 109
50	95	1.3	1	0 95
51	105	1.3	1	0 105
52	56	1.3	1	0 56
53	136	1.3	1	1 135
54	151	1.3	1	0 151
55	93	1.3	1	2 91
56	105	1.3	1	8 97
57	108	1.3	1	4 104
58	37	1.3	1	1 36
59	59	1.3	1	3 56
60	69	1.3	1	1 68
61	64	1.3	1	0 64
62	51	1.3	1	5 46
63	56	1.3	1	0 56
64	47	1.3	1	0 47
65	59	1.3	1	1 58
66	67	1.3	1	2 65
67	76	1.3	1	0 76
68	58	1.3	1	0 58
69	87	1.3	1	0 87
70	32	1.3	1	0 32
71	31	1.3	1	0 31
72	68	1.3	1	1 67
73	51	1.3	1	2 49
74	78	1.3	1	1 77
75	72	1.3	1	3 69
76	52	1.3	1	3 49
77	68	1.3	1	0 68
78	52	1.3	1	2 50
79	62	1.3	1	3 59
80	44	1.3	1	1 43
81	54	1.3	1	0 54
82	41	1.3	1	0 41
83	41	1.3	1	1 40
84	39	1.3	1	0 39
85	65	1.3	1	3 62
86	67	1.3	1	0 67
87	35	1.3	1	0 35
88	50	1.3	1	4 46
89	50	1.3	1	3 47
90	36	1.3	1	1 35
91	20	1.3	1	1 19
92	48	1.3	1	1 47
93	37	1.3	1	3 34
94	48	1.3	1	0 48
95	41	1.3	1	2 39
96	74	1.3	1	0 74
97	65	1.3	1	2 63
98	39	1.3	1	0 39
99	40	1.3	1	4 36
100	39	1.3	1	0 39
101	95	1.3	1	1 94
102	85	1.3	1	0 85
103	66	1.3	1	0 66
104	27	1.3	1	7 20
105	64	1.3	1	2 62
106	41	1.3	1	1 40
107	48	1.3	1	3 45
108	46	1.3	1	0 46
109	50	1.3	1	1 49
110	33	1.3	1	0 33
111	62	1.3	1	2 60
112	45	1.3	1	0 45
113	46	1.3	1	4 42
114	40	1.3	1	0 40
115	37	1.3	1	1 36
116	35	1.3	1	3 32
117	38	1.3	1	0 38
118	61	1.3	1	1 60
119	56	1.3	1	0 56
120	49	1.3	1	3 46
121	37	1.3	1	0 37
122	49	1.3	1	1 48
123	43	1.3	1	0 43
124	43	1.3	1	0 43
125	36	1.3	1	2 34
126	47	1.3	1	0 47
127	39	1.3	1	1 38
128	44	1.3	1	4 40
129	48	1.3	1	4 44
130	34	1.3	1	1 33
131	25	1.3	1	4 21
132	38	1.3	1	3 35
133	67	1.3	1	2 65
134	42	1.3	1	4 38
135	37	1.3	1	1 36
136	42	1.3	1	3 39
137	26	1.3	1	1 25
138	33	1.3	1	0 33
139	30	1.3	1	0 30
140	29	1.3	1	0 29
141	53	1.3	1	0 53
142	23	1.3	1	0 23
143	58	1.3	1	0 58
144	170	1.3	1	4 166
145	42	1.3	1	1 41
146	22	1.3	1	0 22
147	40	1.3	1	0 40
148	72	1.3	1	0 72
149	171	1.3	1	5 166
150	80	1.3	1	3 77

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_1.fastq.gz
=============================================
22178360 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374993_2.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to SRR27374993_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              22,178,360
Reads with adapters:                   320,274 (1.4%)
Reads written (passing filters):    22,178,360 (100.0%)

Total basepairs processed: 3,203,587,003 bp
Quality-trimmed:              29,373,254 bp (0.9%)
Total written (filtered):  3,172,922,832 bp (99.0%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 320274 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 30.0%
  C: 20.0%
  G: 18.6%
  T: 31.4%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	242598	346536.9	0	242598
4	51756	86634.2	0	51756
5	17042	21658.6	0	17042
6	3742	5414.6	0	3742
7	691	1353.7	0	691
8	336	338.4	0	336
9	577	84.6	0	32 545
10	371	21.2	1	13 358
11	169	5.3	1	3 166
12	29	1.3	1	0 29
13	20	1.3	1	0 20
14	17	1.3	1	0 17
15	26	1.3	1	0 26
16	32	1.3	1	0 32
17	11	1.3	1	0 11
18	17	1.3	1	0 17
19	21	1.3	1	0 21
20	16	1.3	1	0 16
21	19	1.3	1	0 19
22	32	1.3	1	0 32
23	35	1.3	1	0 35
24	24	1.3	1	0 24
25	14	1.3	1	0 14
26	39	1.3	1	0 39
27	31	1.3	1	0 31
28	44	1.3	1	3 41
29	40	1.3	1	1 39
30	24	1.3	1	0 24
31	26	1.3	1	0 26
32	29	1.3	1	0 29
33	22	1.3	1	0 22
34	30	1.3	1	0 30
35	21	1.3	1	0 21
36	22	1.3	1	1 21
37	27	1.3	1	0 27
38	18	1.3	1	1 17
39	19	1.3	1	0 19
40	15	1.3	1	0 15
41	29	1.3	1	0 29
42	19	1.3	1	0 19
43	25	1.3	1	0 25
44	18	1.3	1	0 18
45	24	1.3	1	0 24
46	24	1.3	1	0 24
47	23	1.3	1	0 23
48	27	1.3	1	0 27
49	24	1.3	1	0 24
50	19	1.3	1	0 19
51	20	1.3	1	0 20
52	31	1.3	1	0 31
53	11	1.3	1	0 11
54	25	1.3	1	0 25
55	17	1.3	1	1 16
56	17	1.3	1	0 17
57	23	1.3	1	0 23
58	19	1.3	1	0 19
59	22	1.3	1	0 22
60	31	1.3	1	0 31
61	24	1.3	1	0 24
62	15	1.3	1	0 15
63	27	1.3	1	0 27
64	19	1.3	1	0 19
65	20	1.3	1	0 20
66	27	1.3	1	0 27
67	32	1.3	1	0 32
68	20	1.3	1	0 20
69	18	1.3	1	0 18
70	20	1.3	1	0 20
71	23	1.3	1	0 23
72	28	1.3	1	0 28
73	24	1.3	1	0 24
74	17	1.3	1	0 17
75	27	1.3	1	1 26
76	30	1.3	1	0 30
77	16	1.3	1	0 16
78	26	1.3	1	0 26
79	25	1.3	1	0 25
80	27	1.3	1	0 27
81	20	1.3	1	0 20
82	17	1.3	1	0 17
83	24	1.3	1	1 23
84	24	1.3	1	0 24
85	15	1.3	1	0 15
86	16	1.3	1	0 16
87	17	1.3	1	1 16
88	27	1.3	1	0 27
89	13	1.3	1	0 13
90	11	1.3	1	0 11
91	28	1.3	1	0 28
92	13	1.3	1	0 13
93	21	1.3	1	0 21
94	21	1.3	1	0 21
95	24	1.3	1	1 23
96	11	1.3	1	0 11
97	22	1.3	1	1 21
98	28	1.3	1	0 28
99	21	1.3	1	0 21
100	29	1.3	1	1 28
101	26	1.3	1	0 26
102	22	1.3	1	0 22
103	19	1.3	1	0 19
104	16	1.3	1	0 16
105	15	1.3	1	0 15
106	17	1.3	1	0 17
107	16	1.3	1	1 15
108	18	1.3	1	0 18
109	17	1.3	1	0 17
110	29	1.3	1	0 29
111	12	1.3	1	2 10
112	26	1.3	1	0 26
113	15	1.3	1	0 15
114	13	1.3	1	1 12
115	26	1.3	1	1 25
116	21	1.3	1	0 21
117	25	1.3	1	0 25
118	15	1.3	1	0 15
119	19	1.3	1	0 19
120	26	1.3	1	0 26
121	13	1.3	1	0 13
122	20	1.3	1	0 20
123	16	1.3	1	0 16
124	23	1.3	1	1 22
125	26	1.3	1	0 26
126	22	1.3	1	0 22
127	34	1.3	1	0 34
128	24	1.3	1	0 24
129	17	1.3	1	0 17
130	15	1.3	1	0 15
131	22	1.3	1	1 21
132	18	1.3	1	0 18
133	17	1.3	1	0 17
134	22	1.3	1	1 21
135	32	1.3	1	2 30
136	27	1.3	1	1 26
137	15	1.3	1	0 15
138	13	1.3	1	0 13
139	11	1.3	1	0 11
140	18	1.3	1	0 18
141	9	1.3	1	0 9
142	23	1.3	1	1 22
143	17	1.3	1	0 17
144	9	1.3	1	0 9
145	7	1.3	1	0 7
146	44	1.3	1	2 42
147	14	1.3	1	0 14
148	7	1.3	1	0 7
149	19	1.3	1	0 19
150	10	1.3	1	0 10

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374993_2.fastq.gz
=============================================
22178360 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files SRR27374993_1_trimmed.fq.gz and SRR27374993_2_trimmed.fq.gz
file_1: SRR27374993_1_trimmed.fq.gz, file_2: SRR27374993_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: SRR27374993_1_trimmed.fq.gz and SRR27374993_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to SRR27374993_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to SRR27374993_2_val_2.fq.gz

Total number of sequences analysed: 22178360

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 132543 (0.60%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3142 (0.01%)


  >>> Now running FastQC on the validated data SRR27374993_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of SRR27374993_1_val_1.fq.gz
Approx 5% complete for SRR27374993_1_val_1.fq.gz
Approx 10% complete for SRR27374993_1_val_1.fq.gz
Approx 15% complete for SRR27374993_1_val_1.fq.gz
Approx 20% complete for SRR27374993_1_val_1.fq.gz
Approx 25% complete for SRR27374993_1_val_1.fq.gz
Approx 30% complete for SRR27374993_1_val_1.fq.gz
Approx 35% complete for SRR27374993_1_val_1.fq.gz
Approx 40% complete for SRR27374993_1_val_1.fq.gz
Approx 45% complete for SRR27374993_1_val_1.fq.gz
Approx 50% complete for SRR27374993_1_val_1.fq.gz
Approx 55% complete for SRR27374993_1_val_1.fq.gz
Approx 60% complete for SRR27374993_1_val_1.fq.gz
Approx 65% complete for SRR27374993_1_val_1.fq.gz
Approx 70% complete for SRR27374993_1_val_1.fq.gz
Approx 75% complete for SRR27374993_1_val_1.fq.gz
Approx 80% complete for SRR27374993_1_val_1.fq.gz
Approx 85% complete for SRR27374993_1_val_1.fq.gz
Approx 90% complete for SRR27374993_1_val_1.fq.gz
Approx 95% complete for SRR27374993_1_val_1.fq.gz
Analysis complete for SRR27374993_1_val_1.fq.gz

  >>> Now running FastQC on the validated data SRR27374993_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of SRR27374993_2_val_2.fq.gz
Approx 5% complete for SRR27374993_2_val_2.fq.gz
Approx 10% complete for SRR27374993_2_val_2.fq.gz
Approx 15% complete for SRR27374993_2_val_2.fq.gz
Approx 20% complete for SRR27374993_2_val_2.fq.gz
Approx 25% complete for SRR27374993_2_val_2.fq.gz
Approx 30% complete for SRR27374993_2_val_2.fq.gz
Approx 35% complete for SRR27374993_2_val_2.fq.gz
Approx 40% complete for SRR27374993_2_val_2.fq.gz
Approx 45% complete for SRR27374993_2_val_2.fq.gz
Approx 50% complete for SRR27374993_2_val_2.fq.gz
Approx 55% complete for SRR27374993_2_val_2.fq.gz
Approx 60% complete for SRR27374993_2_val_2.fq.gz
Approx 65% complete for SRR27374993_2_val_2.fq.gz
Approx 70% complete for SRR27374993_2_val_2.fq.gz
Approx 75% complete for SRR27374993_2_val_2.fq.gz
Approx 80% complete for SRR27374993_2_val_2.fq.gz
Approx 85% complete for SRR27374993_2_val_2.fq.gz
Approx 90% complete for SRR27374993_2_val_2.fq.gz
Approx 95% complete for SRR27374993_2_val_2.fq.gz
Analysis complete for SRR27374993_2_val_2.fq.gz
Deleting both intermediate output files SRR27374993_1_trimmed.fq.gz and SRR27374993_2_trimmed.fq.gz

====================================================================================================

正在处理样本 SRR27374994 ...
Multicore support not enabled. Proceeding with single-core trimming.
Path to Cutadapt set as: 'cutadapt' (default)
Cutadapt seems to be working fine (tested command 'cutadapt --version')
Cutadapt version: 5.0
single-core operation.
igzip command line interface 2.31.1
igzip detected. Using igzip for decompressing

Output will be written into the directory: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/


AUTO-DETECTING ADAPTER TYPE
===========================
Attempting to auto-detect adapter type from the first 1 million sequences of the first file (>> /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz <<)

Found perfect matches for the following adapter sequences:
Adapter type	Count	Sequence	Sequences analysed	Percentage
smallRNA	9	TGGAATTCTCGG	1000000	0.00
Illumina	8	AGATCGGAAGAGC	1000000	0.00
Nextera	2	CTGTCTCTTATA	1000000	0.00
Using smallRNA adapter for trimming (count: 9). Second best hit was Illumina (count: 8)

Setting the Illumina smallRNA 5' adapter as adapter 2: 'GATCGTCGGACT'
Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374994_1.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j 1
Writing final adapter and quality trimmed output to SRR27374994_1_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'TGGAATTCTCGG' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a TGGAATTCTCGG /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,556,950
Reads with adapters:                   550,952 (2.7%)
Reads written (passing filters):    20,556,950 (100.0%)

Total basepairs processed: 2,979,370,570 bp
Quality-trimmed:              20,663,388 bp (0.7%)
Total written (filtered):  2,956,338,984 bp (99.2%)

=== Adapter 1 ===

Sequence: TGGAATTCTCGG; Type: regular 3'; Length: 12; Trimmed: 550952 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 23.4%
  C: 17.9%
  G: 34.7%
  T: 24.0%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	426625	321202.3	0	426625
4	74919	80300.6	0	74919
5	33578	20075.1	0	33578
6	5432	5018.8	0	5432
7	901	1254.7	0	901
8	202	313.7	0	202
9	491	78.4	0	71 420
10	817	19.6	1	27 790
11	375	4.9	1	5 370
12	126	1.2	1	4 122
13	74	1.2	1	2 72
14	69	1.2	1	1 68
15	47	1.2	1	1 46
16	66	1.2	1	0 66
17	47	1.2	1	1 46
18	65	1.2	1	2 63
19	52	1.2	1	0 52
20	101	1.2	1	2 99
21	80	1.2	1	0 80
22	45	1.2	1	0 45
23	68	1.2	1	3 65
24	52	1.2	1	1 51
25	79	1.2	1	2 77
26	75	1.2	1	6 69
27	96	1.2	1	6 90
28	67	1.2	1	4 63
29	59	1.2	1	1 58
30	99	1.2	1	2 97
31	70	1.2	1	5 65
32	94	1.2	1	5 89
33	82	1.2	1	5 77
34	61	1.2	1	1 60
35	81	1.2	1	3 78
36	60	1.2	1	1 59
37	61	1.2	1	0 61
38	30	1.2	1	0 30
39	72	1.2	1	0 72
40	47	1.2	1	2 45
41	73	1.2	1	4 69
42	51	1.2	1	9 42
43	48	1.2	1	3 45
44	40	1.2	1	1 39
45	36	1.2	1	2 34
46	71	1.2	1	2 69
47	55	1.2	1	9 46
48	68	1.2	1	0 68
49	97	1.2	1	1 96
50	74	1.2	1	0 74
51	83	1.2	1	1 82
52	70	1.2	1	1 69
53	118	1.2	1	0 118
54	131	1.2	1	0 131
55	85	1.2	1	3 82
56	106	1.2	1	7 99
57	88	1.2	1	5 83
58	47	1.2	1	6 41
59	63	1.2	1	4 59
60	43	1.2	1	1 42
61	43	1.2	1	0 43
62	54	1.2	1	1 53
63	29	1.2	1	0 29
64	21	1.2	1	0 21
65	48	1.2	1	0 48
66	68	1.2	1	0 68
67	52	1.2	1	0 52
68	61	1.2	1	1 60
69	64	1.2	1	1 63
70	43	1.2	1	3 40
71	42	1.2	1	3 39
72	55	1.2	1	3 52
73	38	1.2	1	3 35
74	74	1.2	1	2 72
75	69	1.2	1	3 66
76	47	1.2	1	0 47
77	57	1.2	1	1 56
78	36	1.2	1	0 36
79	65	1.2	1	1 64
80	40	1.2	1	1 39
81	37	1.2	1	2 35
82	34	1.2	1	2 32
83	43	1.2	1	0 43
84	43	1.2	1	0 43
85	39	1.2	1	0 39
86	70	1.2	1	1 69
87	39	1.2	1	2 37
88	38	1.2	1	8 30
89	59	1.2	1	5 54
90	27	1.2	1	0 27
91	27	1.2	1	0 27
92	45	1.2	1	4 41
93	40	1.2	1	1 39
94	39	1.2	1	0 39
95	40	1.2	1	2 38
96	48	1.2	1	0 48
97	52	1.2	1	2 50
98	32	1.2	1	1 31
99	38	1.2	1	0 38
100	30	1.2	1	0 30
101	97	1.2	1	0 97
102	60	1.2	1	1 59
103	53	1.2	1	0 53
104	50	1.2	1	5 45
105	38	1.2	1	0 38
106	50	1.2	1	2 48
107	43	1.2	1	1 42
108	34	1.2	1	0 34
109	35	1.2	1	1 34
110	45	1.2	1	0 45
111	52	1.2	1	1 51
112	38	1.2	1	1 37
113	35	1.2	1	1 34
114	24	1.2	1	0 24
115	38	1.2	1	0 38
116	42	1.2	1	6 36
117	46	1.2	1	0 46
118	66	1.2	1	1 65
119	48	1.2	1	1 47
120	49	1.2	1	1 48
121	48	1.2	1	0 48
122	43	1.2	1	0 43
123	39	1.2	1	2 37
124	39	1.2	1	1 38
125	31	1.2	1	3 28
126	40	1.2	1	0 40
127	36	1.2	1	1 35
128	25	1.2	1	0 25
129	39	1.2	1	2 37
130	24	1.2	1	1 23
131	31	1.2	1	1 30
132	25	1.2	1	0 25
133	40	1.2	1	0 40
134	44	1.2	1	3 41
135	38	1.2	1	1 37
136	35	1.2	1	1 34
137	30	1.2	1	2 28
138	36	1.2	1	3 33
139	45	1.2	1	0 45
140	27	1.2	1	0 27
141	57	1.2	1	1 56
142	24	1.2	1	0 24
143	61	1.2	1	1 60
144	164	1.2	1	1 163
145	35	1.2	1	1 34
146	16	1.2	1	1 15
147	28	1.2	1	1 27
148	61	1.2	1	1 60
149	144	1.2	1	0 144
150	66	1.2	1	1 65

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_1.fastq.gz
=============================================
20556950 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Writing report to '/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/SRR27374994_2.fastq.gz_trimming_report.txt'

SUMMARISING RUN PARAMETERS
==========================
Input filename: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz
Trimming mode: paired-end
Trim Galore version: 0.6.10
Cutadapt version: 5.0
Number of cores used for trimming: 1
Quality Phred score cutoff: 20
Quality encoding type selected: ASCII+33
Adapter sequence: 'TGGAATTCTCGG' (Illumina small RNA adapter; auto-detected)
Maximum trimming error rate: 0.1 (default)
Maximum number of tolerated Ns: 3
Optional adapter 2 sequence (only used for read 2 of paired-end files): 'GATCGTCGGACT'
Minimum required adapter overlap (stringency): 3 bp
Minimum required sequence length for both reads before a sequence pair gets removed: 36 bp
Running FastQC on the data once trimming has completed
Output file(s) will be GZIP compressed

Cutadapt seems to be fairly up-to-date (version 5.0). Setting -j -j 1
Writing final adapter and quality trimmed output to SRR27374994_2_trimmed.fq.gz


  >>> Now performing quality (cutoff '-q 20') and adapter trimming in a single pass for the adapter sequence: 'GATCGTCGGACT' from file /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz <<< 
10000000 sequences processed
20000000 sequences processed
This is cutadapt 5.0 with Python 3.10.14
Command line parameters: -j 1 -e 0.1 -q 20 -O 3 -a GATCGTCGGACT /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz
Processing single-end reads on 1 core ...

=== Summary ===

Total reads processed:              20,556,950
Reads with adapters:                   295,227 (1.4%)
Reads written (passing filters):    20,556,950 (100.0%)

Total basepairs processed: 2,974,361,272 bp
Quality-trimmed:              26,447,542 bp (0.9%)
Total written (filtered):  2,946,732,875 bp (99.1%)

=== Adapter 1 ===

Sequence: GATCGTCGGACT; Type: regular 3'; Length: 12; Trimmed: 295227 times

Minimum overlap: 3
No. of allowed errors:
1-9 bp: 0; 10-12 bp: 1

Bases preceding removed adapters:
  A: 29.8%
  C: 20.3%
  G: 18.4%
  T: 31.5%
  none/other: 0.0%

Overview of removed sequences
length	count	expect	max.err	error counts
3	223515	321202.3	0	223515
4	47734	80300.6	0	47734
5	15941	20075.1	0	15941
6	3514	5018.8	0	3514
7	575	1254.7	0	575
8	281	313.7	0	281
9	519	78.4	0	26 493
10	353	19.6	1	9 344
11	163	4.9	1	2 161
12	17	1.2	1	0 17
13	11	1.2	1	0 11
14	11	1.2	1	0 11
15	19	1.2	1	0 19
16	24	1.2	1	0 24
17	22	1.2	1	0 22
18	19	1.2	1	1 18
19	22	1.2	1	0 22
20	23	1.2	1	1 22
21	16	1.2	1	0 16
22	23	1.2	1	0 23
23	17	1.2	1	0 17
24	23	1.2	1	1 22
25	21	1.2	1	1 20
26	42	1.2	1	0 42
27	27	1.2	1	0 27
28	40	1.2	1	0 40
29	22	1.2	1	0 22
30	16	1.2	1	0 16
31	8	1.2	1	0 8
32	41	1.2	1	0 41
33	25	1.2	1	0 25
34	26	1.2	1	0 26
35	22	1.2	1	0 22
36	20	1.2	1	0 20
37	25	1.2	1	0 25
38	21	1.2	1	1 20
39	19	1.2	1	0 19
40	17	1.2	1	0 17
41	19	1.2	1	0 19
42	14	1.2	1	0 14
43	15	1.2	1	0 15
44	14	1.2	1	0 14
45	18	1.2	1	0 18
46	15	1.2	1	0 15
47	16	1.2	1	0 16
48	20	1.2	1	0 20
49	21	1.2	1	0 21
50	20	1.2	1	0 20
51	14	1.2	1	0 14
52	32	1.2	1	1 31
53	23	1.2	1	0 23
54	13	1.2	1	0 13
55	21	1.2	1	0 21
56	16	1.2	1	0 16
57	26	1.2	1	0 26
58	17	1.2	1	0 17
59	14	1.2	1	1 13
60	30	1.2	1	0 30
61	20	1.2	1	0 20
62	20	1.2	1	0 20
63	19	1.2	1	0 19
64	25	1.2	1	0 25
65	30	1.2	1	0 30
66	27	1.2	1	0 27
67	28	1.2	1	0 28
68	13	1.2	1	0 13
69	17	1.2	1	0 17
70	11	1.2	1	0 11
71	28	1.2	1	1 27
72	16	1.2	1	0 16
73	26	1.2	1	0 26
74	24	1.2	1	0 24
75	22	1.2	1	0 22
76	19	1.2	1	0 19
77	11	1.2	1	0 11
78	22	1.2	1	0 22
79	18	1.2	1	0 18
80	10	1.2	1	0 10
81	17	1.2	1	1 16
82	21	1.2	1	0 21
83	17	1.2	1	0 17
84	16	1.2	1	0 16
85	16	1.2	1	0 16
86	19	1.2	1	0 19
87	22	1.2	1	0 22
88	24	1.2	1	0 24
89	19	1.2	1	0 19
90	17	1.2	1	0 17
91	15	1.2	1	0 15
92	22	1.2	1	0 22
93	14	1.2	1	0 14
94	23	1.2	1	0 23
95	15	1.2	1	1 14
96	20	1.2	1	0 20
97	18	1.2	1	0 18
98	19	1.2	1	0 19
99	21	1.2	1	0 21
100	23	1.2	1	0 23
101	30	1.2	1	2 28
102	21	1.2	1	1 20
103	15	1.2	1	0 15
104	18	1.2	1	0 18
105	20	1.2	1	1 19
106	24	1.2	1	0 24
107	15	1.2	1	0 15
108	19	1.2	1	0 19
109	13	1.2	1	0 13
110	16	1.2	1	0 16
111	9	1.2	1	0 9
112	18	1.2	1	0 18
113	15	1.2	1	0 15
114	19	1.2	1	0 19
115	15	1.2	1	1 14
116	14	1.2	1	0 14
117	16	1.2	1	0 16
118	14	1.2	1	0 14
119	15	1.2	1	0 15
120	20	1.2	1	0 20
121	16	1.2	1	1 15
122	15	1.2	1	2 13
123	13	1.2	1	0 13
124	19	1.2	1	1 18
125	21	1.2	1	0 21
126	18	1.2	1	1 17
127	27	1.2	1	0 27
128	21	1.2	1	0 21
129	18	1.2	1	0 18
130	23	1.2	1	0 23
131	21	1.2	1	0 21
132	12	1.2	1	0 12
133	9	1.2	1	0 9
134	12	1.2	1	1 11
135	24	1.2	1	1 23
136	19	1.2	1	0 19
137	16	1.2	1	2 14
138	9	1.2	1	0 9
139	21	1.2	1	1 20
140	10	1.2	1	0 10
141	10	1.2	1	0 10
142	14	1.2	1	0 14
143	9	1.2	1	0 9
144	16	1.2	1	0 16
145	10	1.2	1	0 10
146	29	1.2	1	1 28
147	15	1.2	1	1 14
148	7	1.2	1	0 7
149	21	1.2	1	0 21
150	10	1.2	1	0 10

RUN STATISTICS FOR INPUT FILE: /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq/SRR27374994_2.fastq.gz
=============================================
20556950 sequences processed in total
The length threshold of paired-end sequences gets evaluated later on (in the validation step)

Validate paired-end files SRR27374994_1_trimmed.fq.gz and SRR27374994_2_trimmed.fq.gz
file_1: SRR27374994_1_trimmed.fq.gz, file_2: SRR27374994_2_trimmed.fq.gz


>>>>> Now validing the length of the 2 paired-end infiles: SRR27374994_1_trimmed.fq.gz and SRR27374994_2_trimmed.fq.gz <<<<<
Writing validated paired-end Read 1 reads to SRR27374994_1_val_1.fq.gz
Writing validated paired-end Read 2 reads to SRR27374994_2_val_2.fq.gz

Total number of sequences analysed: 20556950

Number of sequence pairs removed because at least one read was shorter than the length cutoff (36 bp): 119938 (0.58%)
Number of sequence pairs removed because at least one read contained more N(s) than the specified limit of 3: 3133 (0.02%)


  >>> Now running FastQC on the validated data SRR27374994_1_val_1.fq.gz<<<

application/octet-stream
Started analysis of SRR27374994_1_val_1.fq.gz
Approx 5% complete for SRR27374994_1_val_1.fq.gz
Approx 10% complete for SRR27374994_1_val_1.fq.gz
Approx 15% complete for SRR27374994_1_val_1.fq.gz
Approx 20% complete for SRR27374994_1_val_1.fq.gz
Approx 25% complete for SRR27374994_1_val_1.fq.gz
Approx 30% complete for SRR27374994_1_val_1.fq.gz
Approx 35% complete for SRR27374994_1_val_1.fq.gz
Approx 40% complete for SRR27374994_1_val_1.fq.gz
Approx 45% complete for SRR27374994_1_val_1.fq.gz
Approx 50% complete for SRR27374994_1_val_1.fq.gz
Approx 55% complete for SRR27374994_1_val_1.fq.gz
Approx 60% complete for SRR27374994_1_val_1.fq.gz
Approx 65% complete for SRR27374994_1_val_1.fq.gz
Approx 70% complete for SRR27374994_1_val_1.fq.gz
Approx 75% complete for SRR27374994_1_val_1.fq.gz
Approx 80% complete for SRR27374994_1_val_1.fq.gz
Approx 85% complete for SRR27374994_1_val_1.fq.gz
Approx 90% complete for SRR27374994_1_val_1.fq.gz
Approx 95% complete for SRR27374994_1_val_1.fq.gz
Approx 100% complete for SRR27374994_1_val_1.fq.gz
Analysis complete for SRR27374994_1_val_1.fq.gz

  >>> Now running FastQC on the validated data SRR27374994_2_val_2.fq.gz<<<

application/octet-stream
Started analysis of SRR27374994_2_val_2.fq.gz
Approx 5% complete for SRR27374994_2_val_2.fq.gz
Approx 10% complete for SRR27374994_2_val_2.fq.gz
Approx 15% complete for SRR27374994_2_val_2.fq.gz
Approx 20% complete for SRR27374994_2_val_2.fq.gz
Approx 25% complete for SRR27374994_2_val_2.fq.gz
Approx 30% complete for SRR27374994_2_val_2.fq.gz
Approx 35% complete for SRR27374994_2_val_2.fq.gz
Approx 40% complete for SRR27374994_2_val_2.fq.gz
Approx 45% complete for SRR27374994_2_val_2.fq.gz
Approx 50% complete for SRR27374994_2_val_2.fq.gz
Approx 55% complete for SRR27374994_2_val_2.fq.gz
Approx 60% complete for SRR27374994_2_val_2.fq.gz
Approx 65% complete for SRR27374994_2_val_2.fq.gz
Approx 70% complete for SRR27374994_2_val_2.fq.gz
Approx 75% complete for SRR27374994_2_val_2.fq.gz
Approx 80% complete for SRR27374994_2_val_2.fq.gz
Approx 85% complete for SRR27374994_2_val_2.fq.gz
Approx 90% complete for SRR27374994_2_val_2.fq.gz
Approx 95% complete for SRR27374994_2_val_2.fq.gz
Analysis complete for SRR27374994_2_val_2.fq.gz
Deleting both intermediate output files SRR27374994_1_trimmed.fq.gz and SRR27374994_2_trimmed.fq.gz

====================================================================================================

所有样本处理完成！
