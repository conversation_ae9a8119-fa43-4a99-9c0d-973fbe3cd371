#!/bin/bash
# 批量处理所有 SRR 开头的 FASTQ 文件，进行截短与质控

# 定义原始数据目录和输出目录（请根据实际路径修改）
rawdata="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq"
cleandata="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore"

# 如果输出目录不存在则创建
mkdir -p "$cleandata"

# 提取所有样本的唯一 ID（假定文件命名格式为 SRRxxxxxx_1.fastq.gz）
samples=$(ls "$rawdata"/SRR*_1.fastq.gz 2>/dev/null | sed 's/_1\.fastq\.gz//' | xargs -n1 basename | sort | uniq)

echo "检测到以下样本："
echo "$samples"

# 对每个样本进行处理
for id in $samples; do
    echo "正在处理样本 $id ..."
    trim_galore --phred33 -q 20 --length 36 --stringency 3 --fastqc --paired --max_n 3 \
        -o "$cleandata" \
        "${rawdata}/${id}_1.fastq.gz" "${rawdata}/${id}_2.fastq.gz"
done

echo "所有样本处理完成！"