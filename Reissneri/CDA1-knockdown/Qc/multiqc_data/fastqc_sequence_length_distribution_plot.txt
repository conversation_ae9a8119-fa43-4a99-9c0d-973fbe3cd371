Sample	0	1	2	3	4	5	6	7	8	9	10	11	12	13	14	15	16	17	18	19	20	21	22	23	24	25	26	27	28	29	30	31	32	33	34	35	36	37	38
SRR27374989_1	(74, 5923.0)	(76, 12928.0)	(78, 14857.0)	(80, 16907.0)	(82, 19521.0)	(84, 22194.0)	(86, 25152.0)	(88, 28826.0)	(90, 33178.0)	(92, 38087.0)	(94, 43806.0)	(96, 49281.0)	(98, 56266.0)	(100, 63817.0)	(102, 72576.0)	(104, 81147.0)	(106, 91694.0)	(108, 102737.0)	(110, 113479.0)	(112, 127871.0)	(114, 141692.0)	(116, 156080.0)	(118, 171291.0)	(120, 188056.0)	(122, 204801.0)	(124, 225127.0)	(126, 244458.0)	(128, 264076.0)	(130, 285248.0)	(132, 306821.0)	(134, 330936.0)	(136, 354291.0)	(138, 379390.0)	(140, 405999.0)	(142, 443033.0)	(144, 502561.0)	(146, 481298.0)	(148, 408374.0)	(150, 16365487.0)
SRR27374989_2	(74, 6551.0)	(76, 14004.0)	(78, 16164.0)	(80, 18392.0)	(82, 20973.0)	(84, 23766.0)	(86, 26973.0)	(88, 30458.0)	(90, 35134.0)	(92, 40129.0)	(94, 46058.0)	(96, 51837.0)	(98, 58784.0)	(100, 66700.0)	(102, 75446.0)	(104, 84024.0)	(106, 94976.0)	(108, 106402.0)	(110, 117243.0)	(112, 131854.0)	(114, 145978.0)	(116, 160766.0)	(118, 176337.0)	(120, 193393.0)	(122, 210540.0)	(124, 231578.0)	(126, 251584.0)	(128, 271975.0)	(130, 294012.0)	(132, 317085.0)	(134, 341862.0)	(136, 366586.0)	(138, 393643.0)	(140, 422564.0)	(142, 461068.0)	(144, 516416.0)	(146, 510812.0)	(148, 405195.0)	(150, 16142004.0)
SRR27374990_1	(74, 6459.0)	(76, 13375.0)	(78, 15166.0)	(80, 17144.0)	(82, 19474.0)	(84, 22378.0)	(86, 25476.0)	(88, 29058.0)	(90, 32822.0)	(92, 37369.0)	(94, 42824.0)	(96, 48251.0)	(98, 54075.0)	(100, 62152.0)	(102, 69982.0)	(104, 78940.0)	(106, 88804.0)	(108, 99715.0)	(110, 110815.0)	(112, 122965.0)	(114, 138186.0)	(116, 152275.0)	(118, 168034.0)	(120, 184115.0)	(122, 201818.0)	(124, 221621.0)	(126, 240959.0)	(128, 261422.0)	(130, 282055.0)	(132, 305737.0)	(134, 331373.0)	(136, 353929.0)	(138, 380359.0)	(140, 408537.0)	(142, 447840.0)	(144, 510909.0)	(146, 485832.0)	(148, 412251.0)	(150, 17369451.0)
SRR27374990_2	(74, 7048.0)	(76, 14688.0)	(78, 16608.0)	(80, 18770.0)	(82, 21183.0)	(84, 24345.0)	(86, 27467.0)	(88, 31058.0)	(90, 35018.0)	(92, 39774.0)	(94, 45485.0)	(96, 51064.0)	(98, 56944.0)	(100, 65398.0)	(102, 73228.0)	(104, 82246.0)	(106, 92545.0)	(108, 103981.0)	(110, 115150.0)	(112, 127702.0)	(114, 143073.0)	(116, 157908.0)	(118, 173917.0)	(120, 190831.0)	(122, 208749.0)	(124, 229311.0)	(126, 249620.0)	(128, 270771.0)	(130, 292579.0)	(132, 317410.0)	(134, 344426.0)	(136, 368799.0)	(138, 398116.0)	(140, 428515.0)	(142, 470337.0)	(144, 530629.0)	(146, 520926.0)	(148, 409191.0)	(150, 17099137.0)
SRR27374991_1	(74, 4942.0)	(76, 10861.0)	(78, 12164.0)	(80, 14199.0)	(82, 16047.0)	(84, 18383.0)	(86, 21021.0)	(88, 23903.0)	(90, 27347.0)	(92, 31574.0)	(94, 36358.0)	(96, 41048.0)	(98, 46649.0)	(100, 53456.0)	(102, 60386.0)	(104, 67899.0)	(106, 76476.0)	(108, 85171.0)	(110, 95497.0)	(112, 106848.0)	(114, 119076.0)	(116, 130753.0)	(118, 144710.0)	(120, 159156.0)	(122, 173573.0)	(124, 191940.0)	(126, 207578.0)	(128, 224237.0)	(130, 243272.0)	(132, 262437.0)	(134, 285265.0)	(136, 306561.0)	(138, 329303.0)	(140, 354082.0)	(142, 387050.0)	(144, 443433.0)	(146, 422674.0)	(148, 358453.0)	(150, 15108190.0)
SRR27374991_2	(74, 5550.0)	(76, 12002.0)	(78, 13385.0)	(80, 15571.0)	(82, 17547.0)	(84, 19970.0)	(86, 22697.0)	(88, 25759.0)	(90, 29376.0)	(92, 33591.0)	(94, 38605.0)	(96, 43539.0)	(98, 49089.0)	(100, 56347.0)	(102, 63333.0)	(104, 70843.0)	(106, 79838.0)	(108, 88965.0)	(110, 99465.0)	(112, 111003.0)	(114, 123231.0)	(116, 135523.0)	(118, 149552.0)	(120, 164598.0)	(122, 179559.0)	(124, 198580.0)	(126, 214450.0)	(128, 231905.0)	(130, 251584.0)	(132, 271270.0)	(134, 295113.0)	(136, 318290.0)	(138, 342706.0)	(140, 368133.0)	(142, 402593.0)	(144, 452899.0)	(146, 446974.0)	(148, 355855.0)	(150, 14902682.0)
SRR27374992_1	(74, 5885.0)	(76, 12878.0)	(78, 14887.0)	(80, 17017.0)	(82, 19929.0)	(84, 22745.0)	(86, 26013.0)	(88, 29940.0)	(90, 34310.0)	(92, 40013.0)	(94, 45128.0)	(96, 51857.0)	(98, 58907.0)	(100, 67337.0)	(102, 75874.0)	(104, 85486.0)	(106, 96110.0)	(108, 108130.0)	(110, 120762.0)	(112, 134921.0)	(114, 149725.0)	(116, 163368.0)	(118, 181306.0)	(120, 198783.0)	(122, 215039.0)	(124, 236975.0)	(126, 255036.0)	(128, 275261.0)	(130, 297114.0)	(132, 320406.0)	(134, 344275.0)	(136, 366075.0)	(138, 392318.0)	(140, 418500.0)	(142, 457620.0)	(144, 515516.0)	(146, 495256.0)	(148, 427489.0)	(150, 16218742.0)
SRR27374992_2	(74, 6475.0)	(76, 14110.0)	(78, 16418.0)	(80, 18452.0)	(82, 21432.0)	(84, 24422.0)	(86, 27847.0)	(88, 31912.0)	(90, 36364.0)	(92, 42135.0)	(94, 47534.0)	(96, 54510.0)	(98, 61793.0)	(100, 70372.0)	(102, 79116.0)	(104, 88840.0)	(106, 99693.0)	(108, 112111.0)	(110, 124834.0)	(112, 139352.0)	(114, 154494.0)	(116, 168636.0)	(118, 186887.0)	(120, 204718.0)	(122, 221495.0)	(124, 244349.0)	(126, 263290.0)	(128, 283902.0)	(130, 306799.0)	(132, 331082.0)	(134, 356356.0)	(136, 379695.0)	(138, 408145.0)	(140, 435145.0)	(142, 476175.0)	(144, 529617.0)	(146, 522891.0)	(148, 424921.0)	(150, 15980614.0)
SRR27374993_1	(74, 5718.0)	(76, 12269.0)	(78, 14177.0)	(80, 16049.0)	(82, 18153.0)	(84, 20706.0)	(86, 23090.0)	(88, 26915.0)	(90, 30249.0)	(92, 34220.0)	(94, 39421.0)	(96, 44358.0)	(98, 50238.0)	(100, 57042.0)	(102, 63575.0)	(104, 72461.0)	(106, 80690.0)	(108, 89920.0)	(110, 100672.0)	(112, 112642.0)	(114, 124670.0)	(116, 136924.0)	(118, 151302.0)	(120, 166969.0)	(122, 180980.0)	(124, 201193.0)	(126, 216908.0)	(128, 234799.0)	(130, 255451.0)	(132, 275452.0)	(134, 298264.0)	(136, 319492.0)	(138, 343410.0)	(140, 369160.0)	(142, 406353.0)	(144, 466021.0)	(146, 446327.0)	(148, 379622.0)	(150, 16292498.0)
SRR27374993_2	(74, 6335.0)	(76, 13450.0)	(78, 15545.0)	(80, 17564.0)	(82, 19721.0)	(84, 22211.0)	(86, 24910.0)	(88, 28881.0)	(90, 32176.0)	(92, 36486.0)	(94, 41919.0)	(96, 46932.0)	(98, 53017.0)	(100, 60170.0)	(102, 66618.0)	(104, 75845.0)	(106, 84393.0)	(108, 93965.0)	(110, 105026.0)	(112, 117113.0)	(114, 129710.0)	(116, 142232.0)	(118, 157031.0)	(120, 173218.0)	(122, 187946.0)	(124, 208694.0)	(126, 225168.0)	(128, 243952.0)	(130, 265846.0)	(132, 287326.0)	(134, 311388.0)	(136, 334275.0)	(138, 360067.0)	(140, 388762.0)	(142, 429074.0)	(144, 485251.0)	(146, 478124.0)	(148, 376959.0)	(150, 16031060.0)
SRR27374994_1	(74, 4276.0)	(76, 8984.0)	(78, 10302.0)	(80, 11892.0)	(82, 13583.0)	(84, 15272.0)	(86, 17584.0)	(88, 20077.0)	(90, 23237.0)	(92, 26369.0)	(94, 30418.0)	(96, 34885.0)	(98, 39867.0)	(100, 45780.0)	(102, 52193.0)	(104, 59139.0)	(106, 67727.0)	(108, 76450.0)	(110, 87031.0)	(112, 98147.0)	(114, 110876.0)	(116, 123510.0)	(118, 136752.0)	(120, 152816.0)	(122, 166073.0)	(124, 187396.0)	(126, 202844.0)	(128, 221320.0)	(130, 240357.0)	(132, 261979.0)	(134, 284581.0)	(136, 305713.0)	(138, 330366.0)	(140, 355371.0)	(142, 392270.0)	(144, 447604.0)	(146, 432010.0)	(148, 371532.0)	(150, 15090367.0)
SRR27374994_2	(74, 4763.0)	(76, 10022.0)	(78, 11627.0)	(80, 13196.0)	(82, 14933.0)	(84, 16817.0)	(86, 19097.0)	(88, 21765.0)	(90, 24967.0)	(92, 28348.0)	(94, 32534.0)	(96, 37216.0)	(98, 42440.0)	(100, 48545.0)	(102, 54947.0)	(104, 62391.0)	(106, 71064.0)	(108, 80164.0)	(110, 90887.0)	(112, 102395.0)	(114, 115407.0)	(116, 128208.0)	(118, 141695.0)	(120, 158558.0)	(122, 172563.0)	(124, 194187.0)	(126, 210075.0)	(128, 229610.0)	(130, 249609.0)	(132, 271795.0)	(134, 295630.0)	(136, 318463.0)	(138, 345121.0)	(140, 372260.0)	(142, 411278.0)	(144, 462438.0)	(146, 459564.0)	(148, 368780.0)	(150, 14863591.0)
