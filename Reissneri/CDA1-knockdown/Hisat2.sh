#!/bin/bash
# Hisat2_batch.sh
# 批量对样本进行比对
# 请根据实际情况修改以下目录变量

# HISAT2 索引前缀（确保索引文件已构建）
index="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore/genome_index"

# FASTQ 文件所在目录
fqdir="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/trim_galore"

# 输出目录（比对结果存放目录）
outdir="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/Hisat2"
mkdir -p "$outdir"

# 生成样本 ID 列表：假设样本 ID 就是文件名前缀，例如 SRR27374994, SRR27374993, …, SRR27374989
# 如果你还没有 sample.ID 文件，可以用下面的方法生成：
ls "$fqdir"/SRR*_1_val_1.fq.gz | sed 's/_1_val_1\.fq\.gz//' | xargs -n1 basename | sort | uniq > sample.ID

# 循环处理每个样本
while read id; do
    echo "正在处理样本 $id ..."
    # HISAT2 比对
    hisat2 -p 10 -x "$index" -1 "${fqdir}/${id}_1_val_1.fq.gz" -2 "${fqdir}/${id}_2_val_2.fq.gz" -S "${outdir}/${id}.sam" 2> "${outdir}/${id}.log"
    
    # SAM 转 BAM 排序并建立索引
    samtools sort -@ 3 -o "${outdir}/${id}.sorted.bam" "${outdir}/${id}.sam"
    samtools index "${outdir}/${id}.sorted.bam" "${outdir}/${id}.sorted.bam.bai"
    
    # 可选择删除中间的 SAM 文件释放空间
    rm "${outdir}/${id}.sam"
done < sample.ID

echo "所有样本比对完成！"
