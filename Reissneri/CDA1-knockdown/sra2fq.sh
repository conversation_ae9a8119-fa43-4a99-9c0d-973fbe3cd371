#!/bin/bash
# convert_sra_to_fastq.sh
# 本脚本将 /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/sra 目录下所有以 SRR 开头的 SRA 文件转换为 FASTQ 格式，
# 将输出文件存放到 /public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq 目录下

# 设置输出 FASTQ 文件存放目录
fqdir="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/fastq"
# 设置 SRA 文件存放目录
sra_dir="/public/home/<USER>/RNA-seq/Reissneri/CDA1-knockdown/sra"

# 如果输出目录不存在，则创建它
if [ ! -d "$fqdir" ]; then
  mkdir -p "$fqdir"
fi

# 遍历 SRA 文件并转换
for sra_file in "$sra_dir"/SRR*; do
    echo "正在处理 $sra_file ..."
    # fastq-dump 命令将 SRA 转换为 FASTQ
    # --gzip: 输出为 .fastq.gz 格式
    # --split-3: 对于双端数据拆分为两个文件（单端数据也适用）
    # -X 25000: 限制每个 SRA 文件转换最多 25000 条记录（如果想转换全部记录，可去掉 -X 参数）
    fastq-dump --gzip --split-3 -O "$fqdir" "$sra_file"
done

echo "转换完成！"
